import logging
import re
from difflib import SequenceMatcher
from typing import Any, Dict, List, Optional, Tuple, Union, Callable
import unicodedata
try:
    from rapidfuzz import fuzz, process  # Preferred, fast
except ImportError:                      # Soft-fallback
    fuzz = None
    process = None
import threading
import time
from collections import OrderedDict
from .language_config import language_config, get_primary_language, get_supported_languages

# Use module-local logger without configuring global logging
logger = logging.getLogger(__name__)


class LRUCache:
    """
    Thread-safe LRU (Least Recently Used) cache implementation.
    Optimized for production environments with configurable size limits and statistics.
    """

    def __init__(self, max_size: int = 1000):
        """
        Initialize LRU cache.

        Args:
            max_size: Maximum number of items to store in cache
        """
        self.max_size = max_size
        self._cache = OrderedDict()
        self._lock = threading.RLock()  # Use RLock for nested locking
        self._hits = 0
        self._misses = 0
        self._evictions = 0

    def get(self, key: str) -> Optional[str]:
        """
        Get value from cache and mark as recently used.

        Args:
            key: Cache key

        Returns:
            Cached value or None if not found
        """
        with self._lock:
            if key in self._cache:
                # Move to end (most recently used)
                value = self._cache.pop(key)
                self._cache[key] = value
                self._hits += 1
                return value
            else:
                self._misses += 1
                return None

    def put(self, key: str, value: str) -> None:
        """
        Add or update value in cache.

        Args:
            key: Cache key
            value: Value to cache
        """
        with self._lock:
            if key in self._cache:
                # Update existing key and move to end
                self._cache.pop(key)
                self._cache[key] = value
            else:
                # Add new key
                if len(self._cache) >= self.max_size:
                    # Remove least recently used item (first item)
                    self._cache.popitem(last=False)
                    self._evictions += 1
                self._cache[key] = value

    def clear(self) -> None:
        """Clear all cached items."""
        with self._lock:
            self._cache.clear()
            self._hits = 0
            self._misses = 0
            self._evictions = 0

    def size(self) -> int:
        """Get current cache size."""
        with self._lock:
            return len(self._cache)

    def get_stats(self) -> Dict[str, int]:
        """
        Get cache statistics.

        Returns:
            Dictionary with cache statistics
        """
        with self._lock:
            total_requests = self._hits + self._misses
            hit_rate = (self._hits / total_requests * 100) if total_requests > 0 else 0

            return {
                'size': len(self._cache),
                'max_size': self.max_size,
                'hits': self._hits,
                'misses': self._misses,
                'evictions': self._evictions,
                'hit_rate_percent': round(hit_rate, 2),
                'total_requests': total_requests
            }

class FuzzyMatcher:
    """
    High-performance fuzzy matching for voice inputs.
    Optimized for real-time voice agent responses with multi-language support.
    Uses RapidFuzz for faster string matching operations.
    """

    def __init__(self, threshold: float = 0.6, cache_size: int = 2000):
        """
        Initialize the fuzzy matcher.

        Args:
            threshold: Default similarity threshold (0.0 to 1.0)
            cache_size: Maximum cache size for normalized strings (production default: 2000)
        """
        self.threshold = threshold
        self._cache = LRUCache(max_size=cache_size)  # LRU cache for normalized strings
        self._cache_lock = threading.Lock()  # Additional lock for cache operations

        # Enhanced language-specific normalization patterns for Indian languages
        # Hindi is the primary language
        self.language_patterns = {
            'hi': {  # Hindi (primary language)
                'doctor_titles': ['डॉक्टर', 'डॉ', 'वैद्य', 'चिकित्सक', 'हकीम'],
                'common_words': ['का', 'की', 'के', 'में', 'से', 'को', 'और', 'है', 'हैं', 'था', 'थी', 'यह', 'वह'],
                'medical_terms': ['अस्पताल', 'दवा', 'जांच', 'इलाज', 'बीमारी', 'दर्द'],
                'time_words': ['सुबह', 'दोपहर', 'शाम', 'रात', 'कल', 'आज', 'परसों']
            },
            'bn': {  # Bengali
                'doctor_titles': ['ডাক্তার', 'ডাঃ', 'চিকিৎসক', 'বৈদ্য'],
                'common_words': ['এর', 'এই', 'সেই', 'যে', 'কি', 'কে', 'তে', 'থেকে', 'এবং', 'আছে', 'ছিল'],
                'medical_terms': ['হাসপাতাল', 'ওষুধ', 'পরীক্ষা', 'চিকিৎসা', 'রোগ', 'ব্যথা'],
                'time_words': ['সকাল', 'দুপুর', 'সন্ধ্যা', 'রাত', 'কাল', 'আজ', 'পরশু']
            },
            'en': {  # English
                'doctor_titles': ['doctor', 'dr', 'doc', 'physician'],
                'common_words': ['the', 'a', 'an', 'of', 'in', 'to', 'for', 'and', 'is', 'are', 'was', 'were'],
                'medical_terms': ['hospital', 'medicine', 'test', 'treatment', 'disease', 'pain'],
                'time_words': ['morning', 'afternoon', 'evening', 'night', 'tomorrow', 'today', 'yesterday']
            }

            # TODO: Add patterns for other Indian languages when scaling:
            # 'ta': {  # Tamil
            #     'doctor_titles': ['டாக்டர்', 'மருத்துவர்', 'வைத்தியர்'],
            #     'common_words': ['இன்', 'இது', 'அது', 'என்', 'எது', 'யார்', 'எங்கே'],
            #     'medical_terms': ['மருத்துவமனை', 'மருந்து', 'பரிசோதனை', 'சிகிச்சை'],
            #     'time_words': ['காலை', 'மதியம்', 'மாலை', 'இரவு', 'நாளை', 'இன்று']
            # },
            # 'te': {  # Telugu
            #     'doctor_titles': ['డాక్టర్', 'వైద్యుడు', 'చికిత్సకుడు'],
            #     'common_words': ['యొక్క', 'ఇది', 'అది', 'ఏది', 'ఎవరు', 'ఎక్కడ'],
            #     'medical_terms': ['ఆసుపత్రి', 'మందు', 'పరీక్ష', 'చికిత్స'],
            #     'time_words': ['ఉదయం', 'మధ్యాహ్నం', 'సాయంత్రం', 'రాత్రి', 'రేపు', 'ఈరోజు']
            # }
        }
    
    def match(self,
             input_text: str,
             options: List[Union[str, Dict[str, Any]]],
             key: str = None,
             threshold: float = None,
             language: str = None) -> Tuple[Optional[Union[str, Dict[str, Any]]], float]:
        """
        Find the best fuzzy match for input text among options.

        Args:
            input_text: The input text to match
            options: List of strings or dictionaries to match against
            key: If options are dictionaries, the key to use for matching
            threshold: Optional custom threshold (overrides default)
            language: Language code for proper normalization (defaults to Hindi)

        Returns:
            Tuple containing (best_match, similarity_score)
            If no match meets the threshold, returns (None, 0.0)
        """
        if not input_text or not options:
            return None, 0.0

        # Clean and normalize input text (language-aware & cached)
        input_text = self.multilingual_normalize(input_text, language)

        # Use provided threshold or default
        match_threshold = threshold if threshold is not None else self.threshold

        best_match = None
        best_score = 0.0

        for option in options:
            # Get the text to compare based on whether options are strings or dicts
            compare_text = option if isinstance(option, str) else option.get(key, "")

            # Skip empty options
            if not compare_text:
                continue

            # Normalize option text (language-aware & cached)
            compare_text = self.multilingual_normalize(compare_text, language)

            # Calculate similarity score
            score = self._calculate_similarity(input_text, compare_text)

            # Update best match if score is higher
            if score > best_score:
                best_score = score
                best_match = option

        # Only return a match if it meets the threshold
        if best_score >= match_threshold:
            return best_match, best_score
        else:
            return None, 0.0
    
    def match_with_dtmf(self,
                      speech: str,
                      dtmf: str,
                      options: List[Dict[str, Any]],
                      key: str = "name",
                      id_key: str = "id",
                      threshold: float = None,
                      language: str = None) -> Optional[Dict[str, Any]]:
        """
        Match using both speech and DTMF input.
        Prioritizes exact DTMF matching if available.

        Args:
            speech: Speech input
            dtmf: DTMF input (digits)
            options: List of options to match against
            key: Key for text matching in options
            id_key: Key for id matching with DTMF input
            threshold: Optional custom threshold for fuzzy matching
            language: Language code for proper normalization (defaults to Hindi)

        Returns:
            Best matching option or None if no match
        """
        # First try to match by DTMF if provided
        if dtmf:
            try:
                dtmf_index = int(dtmf) - 1  # Convert to zero-based index
                if 0 <= dtmf_index < len(options):
                    return options[dtmf_index]
            except ValueError:
                # DTMF may not be a simple index, try matching against IDs
                for option in options:
                    if option.get(id_key, "") == dtmf:
                        return option
        
        # Fall back to speech matching if available
        if speech:
            match, score = self.match(speech, options, key, threshold, language)
            return match
            
        return None
    
    def match_entities(self,
                     speech: str,
                     entity_types: Dict[str, List[str]],
                     threshold: float = None,
                     language: str = None) -> Dict[str, Any]:
        """
        Match speech against multiple entity types.

        Args:
            speech: Speech input
            entity_types: Dict mapping entity types to lists of possible values
            threshold: Optional custom threshold
            language: Language code for proper normalization (defaults to Hindi)

        Returns:
            Dict mapping entity types to matched values and scores
        """
        if not speech:
            return {}

        # Clean and normalize input text using multilingual normalization
        speech = self.multilingual_normalize(speech, language)
        match_threshold = threshold if threshold is not None else self.threshold

        results = {}

        for entity_type, values in entity_types.items():
            match, score = self.match(speech, values, threshold=match_threshold, language=language)
            if match:
                results[entity_type] = {
                    "value": match,
                    "score": score
                }

        return results
    
    def _normalize_text(self, text: str, language: str = None) -> str:
        """
        Normalize text for consistent matching.

        DEPRECATED: This method now delegates to multilingual_normalize() for consistency.
        Use multilingual_normalize() directly for new code.

        Args:
            text: Input text
            language: Language code for proper normalization (defaults to Hindi)

        Returns:
            Normalized text
        """
        # Delegate to the multilingual normalization method for consistency
        # This ensures all normalization goes through the same pipeline
        return self.multilingual_normalize(text, language)
    
    def _calculate_similarity(self, str1: str, str2: str) -> float:
        """
        Calculate similarity between two strings using multiple algorithms.

        Args:
            str1: First string
            str2: Second string

        Returns:
            Similarity score between 0.0 and 1.0
        """
        if not str1 or not str2:
            return 0.0

        # Check if fuzz is available (RapidFuzz)
        fuzz_available = 'fuzz' in globals() and fuzz is not None

        if fuzz_available:
            try:
                # Use RapidFuzz for faster computation
                # Combine multiple similarity metrics for better accuracy
                ratio_score = fuzz.ratio(str1, str2) / 100.0
                partial_score = fuzz.partial_ratio(str1, str2) / 100.0
                token_sort_score = fuzz.token_sort_ratio(str1, str2) / 100.0

                # Weighted combination of scores
                combined_score = (
                    ratio_score * 0.4 +
                    partial_score * 0.3 +
                    token_sort_score * 0.3
                )

                return combined_score
            except Exception:
                # Fallback to SequenceMatcher if RapidFuzz fails
                return SequenceMatcher(None, str1, str2).ratio()
        else:
            # RapidFuzz not available, fallback to SequenceMatcher
            return SequenceMatcher(None, str1, str2).ratio()

    def fast_match(self, input_text: str, options: List[str],
                   limit: int = 1, threshold: float = None, language: str = None) -> List[Tuple[str, float]]:
        """
        Fast matching using RapidFuzz process.extractBests.
        Optimized for large option lists.

        Args:
            input_text: Input text to match
            options: List of options to match against
            limit: Number of top matches to return
            threshold: Minimum similarity threshold
            language: Language code for proper normalization (defaults to Hindi)

        Returns:
            List of (match, score) tuples
        """
        if not input_text or not options:
            return []

        try:
            # Normalize input (language-aware & cached)
            normalized_input = self.multilingual_normalize(input_text, language)
            match_threshold = threshold if threshold is not None else self.threshold

            # Check if RapidFuzz process is available
            if process is not None:
                # Normalize options up-front for consistent matching quality
                normalized_options = [self.multilingual_normalize(o, language) for o in options]

                # Use RapidFuzz for fast matching
                results = process.extract(
                    normalized_input,
                    normalized_options,
                    limit=limit,
                    score_cutoff=match_threshold * 100
                )

                # Map selected indices back to original option strings
                # Convert scores to 0-1 range and return original options
                return [(options[idx], scr / 100.0) for _, scr, idx in results]
            else:
                # Fallback to manual matching when RapidFuzz is not available
                results = []
                for option in options:
                    score = self._calculate_similarity(normalized_input, self.multilingual_normalize(option, language))
                    if score >= match_threshold:
                        results.append((option, score))

                # Sort by score and limit results
                results.sort(key=lambda x: x[1], reverse=True)
                return results[:limit]

        except Exception as e:
            logger.error(f"Error in fast matching: {e}")
            return []

    def multilingual_normalize(self, text: str, language: str = None) -> str:
        """
        Enhanced normalization with multi-language support.
        Hindi is the primary language.

        Args:
            text: Input text
            language: Language code (defaults to Hindi)

        Returns:
            Normalized text
        """
        if not text:
            return ""

        # Default to Hindi (primary language) if not specified
        if not language:
            language = get_primary_language()

        # Check cache first
        cache_key = f"{language}:{text}"
        cached_result = self._cache.get(cache_key)
        if cached_result is not None:
            return cached_result

        # Unicode normalization
        normalized = unicodedata.normalize('NFKD', text)

        # Convert to lowercase
        normalized = normalized.lower()

        # Remove language-specific common words
        if language in self.language_patterns:
            common_words = self.language_patterns[language]['common_words']
            words = normalized.split()
            words = [w for w in words if w not in common_words]
            normalized = ' '.join(words)

        # Remove punctuation and extra whitespace (Unicode-aware)
        # Use Unicode categories to preserve all language characters
        normalized = re.sub(r'[^\w\s]', '', normalized, flags=re.UNICODE)
        normalized = re.sub(r'\s+', ' ', normalized).strip()

        # Cache the result using LRU cache
        self._cache.put(cache_key, normalized)

        return normalized

    def get_cache_stats(self) -> Dict[str, Any]:
        """
        Get cache performance statistics.

        Returns:
            Dictionary containing cache statistics
        """
        return self._cache.get_stats()

    def clear_cache(self) -> None:
        """
        Clear the normalization cache.
        Useful for memory management or testing.
        """
        self._cache.clear()
        logger.info("Fuzzy matcher cache cleared")

    def get_top_matches(self,
                      input_text: str,
                      options: List[Union[str, Dict[str, Any]]],
                      key: str = None,
                      limit: int = 3,
                      threshold: float = None,
                      language: str = None) -> List[Tuple[Union[str, Dict[str, Any]], float]]:
        """
        Get top N matches for input text among options.

        Args:
            input_text: The input text to match
            options: List of strings or dictionaries to match against
            key: If options are dictionaries, the key to use for matching
            limit: Maximum number of matches to return
            threshold: Optional custom threshold
            language: Language code for proper normalization (defaults to Hindi)

        Returns:
            List of tuples containing (match, score), sorted by score
        """
        if not input_text or not options:
            return []

        # Clean and normalize input text (language-aware & cached)
        input_text = self.multilingual_normalize(input_text, language)

        # Use provided threshold or default
        match_threshold = threshold if threshold is not None else self.threshold

        results = []

        for option in options:
            # Get the text to compare based on whether options are strings or dicts
            compare_text = option if isinstance(option, str) else option.get(key, "")

            # Skip empty options
            if not compare_text:
                continue

            # Normalize option text (language-aware & cached)
            compare_text = self.multilingual_normalize(compare_text, language)

            # Calculate similarity score
            score = self._calculate_similarity(input_text, compare_text)

            # Add to results if it meets the threshold
            if score >= match_threshold:
                results.append((option, score))

        # Sort by score (descending) and limit results
        results.sort(key=lambda x: x[1], reverse=True)
        return results[:limit]
    
    def extract_custom_entity(self, 
                            text: str, 
                            patterns: List[str], 
                            post_process: Callable[[str], str] = None) -> Optional[str]:
        """
        Extract a custom entity using regex patterns.
        
        Args:
            text: Text to extract from
            patterns: List of regex patterns to try
            post_process: Optional function to process matched text
            
        Returns:
            Extracted entity or None if not found
        """
        if not text or not patterns:
            return None
            
        for pattern in patterns:
            try:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    result = match.group(1)
                    if post_process:
                        result = post_process(result)
                    return result
            except re.error:
                logger.error(f"Invalid regex pattern: {pattern}")
                
        return None
    
    def contains_keywords(self, text: str, keywords: List[str], match_all: bool = False, language: str = None) -> bool:
        """
        Check if text contains specified keywords.
        Uses substring search to preserve phrase semantics for multi-word keywords.

        Args:
            text: Text to check
            keywords: List of keywords to look for (can be single words or phrases)
            match_all: Whether all keywords must be present (True) or any (False)
            language: Language code for proper normalization (defaults to Hindi)

        Returns:
            True if keywords are found, False otherwise
        """
        if not text or not keywords:
            return False

        # Use multilingual normalization for consistent language-aware processing
        normalized_text = self.multilingual_normalize(text, language)
        matches = 0

        for keyword in keywords:
            normalized_keyword = self.multilingual_normalize(keyword, language)

            # Use regex word boundary matching for more robust phrase detection
            # This handles punctuation and ensures we match complete words/phrases
            import re
            pattern = r'\b' + re.escape(normalized_keyword) + r'\b'
            if re.search(pattern, normalized_text, re.IGNORECASE | re.UNICODE):
                if not match_all:
                    return True
                matches += 1

        return matches == len(keywords) if match_all else False

# Create global instance with production-level cache settings
# Cache size of 2000 items should handle multiple hospitals, languages, and concurrent calls
fuzzy_matcher = FuzzyMatcher(cache_size=2000)


def create_production_fuzzy_matcher(cache_size: int = 5000, threshold: float = 0.6) -> FuzzyMatcher:
    """
    Create a FuzzyMatcher instance optimized for production environments.

    Args:
        cache_size: Maximum cache size (default: 5000 for high-volume production)
        threshold: Default similarity threshold

    Returns:
        Configured FuzzyMatcher instance
    """
    return FuzzyMatcher(threshold=threshold, cache_size=cache_size)


def log_cache_performance(matcher: FuzzyMatcher = None) -> None:
    """
    Log cache performance statistics for monitoring.

    Args:
        matcher: FuzzyMatcher instance (defaults to global instance)
    """
    if matcher is None:
        matcher = fuzzy_matcher

    stats = matcher.get_cache_stats()
    logger.info(f"Cache Performance - Size: {stats['size']}/{stats['max_size']}, "
                f"Hit Rate: {stats['hit_rate_percent']}%, "
                f"Hits: {stats['hits']}, Misses: {stats['misses']}, "
                f"Evictions: {stats['evictions']}")


# Helper functions for common matching scenarios

def match_doctor_by_name(speech: str, 
                       dtmf: str, 
                       doctors: List[Dict[str, Any]], 
                       threshold: float = 0.6) -> Optional[Dict[str, Any]]:
    """
    Match doctor by name from speech or DTMF input.
    
    Args:
        speech: Speech input
        dtmf: DTMF input
        doctors: List of doctor dictionaries
        threshold: Similarity threshold
        
    Returns:
        Matched doctor or None
    """
    return fuzzy_matcher.match_with_dtmf(speech, dtmf, doctors, "name", "id", threshold)

def match_test_by_name(speech: str, 
                     dtmf: str, 
                     tests: List[Dict[str, Any]], 
                     threshold: float = 0.6) -> Optional[Dict[str, Any]]:
    """
    Match test by name from speech or DTMF input.
    
    Args:
        speech: Speech input
        dtmf: DTMF input
        tests: List of test dictionaries
        threshold: Similarity threshold
        
    Returns:
        Matched test or None
    """
    return fuzzy_matcher.match_with_dtmf(speech, dtmf, tests, "name", "id", threshold)

def match_time_slot(speech: str, 
                   dtmf: str, 
                   time_slots: List[str], 
                   threshold: float = 0.7) -> Optional[str]:
    """
    Match time slot from speech or DTMF input.
    
    Args:
        speech: Speech input
        dtmf: DTMF input
        time_slots: List of time slot strings
        threshold: Similarity threshold
        
    Returns:
        Matched time slot or None
    """
    # Try DTMF first if provided
    if dtmf:
        try:
            dtmf_index = int(dtmf) - 1  # Convert to zero-based index
            if 0 <= dtmf_index < len(time_slots):
                return time_slots[dtmf_index]
        except ValueError:
            pass
    
    # Try speech matching
    if speech:
        match, score = fuzzy_matcher.match(speech, time_slots, threshold=threshold)
        return match
        
    return None

def is_confirmation(speech: str, language: str = "en") -> bool:
    """
    Check if speech contains confirmation (yes, correct, etc.).

    Args:
        speech: Speech input
        language: Language code

    Returns:
        True if confirmed, False otherwise
    """
    if not speech:
        return False

    # Confirmation keywords for different languages (including multi-word phrases)
    confirmations = {
        "en": ["yes", "yeah", "correct", "right", "confirm", "sure", "ok", "okay", "yes please", "that's right", "sounds good"],
        "hi": ["हां", "जी", "ठीक", "सही", "बिलकुल", "हाँ", "जी हां", "बिलकुल सही", "ठीक है", "हां जी"],
        "bn": ["হ্যাঁ", "হুঁ", "ঠিক", "সঠিক", "হ্যাঁ ভাই", "ঠিক আছে", "একদম ঠিক"]
    }

    lang = language.split("-")[0] if "-" in language else language
    keywords = confirmations.get(lang, confirmations["en"])

    # Pass language parameter for proper multilingual normalization
    return fuzzy_matcher.contains_keywords(speech, keywords, language=lang)

def is_negation(speech: str, language: str = "en") -> bool:
    """
    Check if speech contains negation (no, incorrect, etc.).

    Args:
        speech: Speech input
        language: Language code

    Returns:
        True if negated, False otherwise
    """
    if not speech:
        return False

    # Negation keywords for different languages (including multi-word phrases)
    negations = {
        "en": ["no", "nope", "not", "wrong", "incorrect", "don't", "cancel", "no thanks", "not correct", "that's wrong"],
        "hi": ["नहीं", "ना", "गलत", "नही", "रद्द", "नहीं जी", "गलत है", "ना भाई", "रद्द करें"],
        "bn": ["না", "নাহ", "ভুল", "বাতিল", "না ভাই", "ভুল আছে", "বাতিল করুন"]
    }

    lang = language.split("-")[0] if "-" in language else language
    keywords = negations.get(lang, negations["en"])

    # Pass language parameter for proper multilingual normalization
    return fuzzy_matcher.contains_keywords(speech, keywords, language=lang)

def extract_patient_name(speech: str) -> Optional[str]:
    """
    Extract patient name from speech.
    
    Args:
        speech: Speech input
        
    Returns:
        Extracted name or None
    """
    patterns = [
        r"(?:my name is|this is|i am|i'm|name)(?:\s+)([a-zA-Z\s]+)(?:\s|$)",
        r"(?:patient|person)(?:\s+)([a-zA-Z\s]+)(?:\s|$)",
        r"(?:call me)(?:\s+)([a-zA-Z\s]+)(?:\s|$)"
    ]
    
    name = fuzzy_matcher.extract_custom_entity(speech, patterns)
    
    if name:
        # Clean up the name
        name = re.sub(r'\s+', ' ', name).strip()
        # Check if it's a valid name (at least 2 characters, only letters and spaces)
        if len(name) >= 2 and re.match(r'^[a-zA-Z\s]+$', name):
            return name
            
    return None