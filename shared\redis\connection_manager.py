"""
Redis Connection Manager for Voice Health Portal

Provides production-ready Redis connection management with:
- Connection pooling for high concurrency
- Automatic reconnection and health checks
- Support for both sync and async operations
- Comprehensive error handling and monitoring
"""

import asyncio
import logging
import threading
import time
from typing import Optional, Dict, Any, Union
import redis
import redis.asyncio as aioredis
from .config import RedisConfig, get_redis_config

logger = logging.getLogger(__name__)


class RedisConnectionManager:
    """
    Production-ready Redis connection manager with pooling and health monitoring.
    Supports both synchronous and asynchronous operations for different use cases.
    """
    
    def __init__(self, config: Optional[RedisConfig] = None):
        """
        Initialize Redis connection manager.
        
        Args:
            config: Optional Redis configuration. Uses global config if not provided.
        """
        self.config = config or get_redis_config()
        self._sync_pool: Optional[redis.ConnectionPool] = None
        self._async_pool: Optional[aioredis.ConnectionPool] = None
        self._lock = threading.Lock()
        self._initialized = False
        self._last_health_check = 0
        self._health_check_interval = self.config.health_check_interval
        
        # Connection statistics
        self._connection_stats = {
            "total_connections": 0,
            "active_connections": 0,
            "failed_connections": 0,
            "reconnections": 0,
            "last_error": None,
            "last_error_time": None
        }
    
    def _create_sync_pool(self) -> redis.ConnectionPool:
        """Create synchronous Redis connection pool."""
        try:
            pool_kwargs = self.config.get_pool_kwargs()
            
            if self.config.url and self.config.url != "redis://localhost:6379/0":
                pool = redis.ConnectionPool.from_url(
                    self.config.url,
                    max_connections=self.config.max_connections,
                    retry_on_timeout=self.config.retry_on_timeout,
                    socket_timeout=self.config.socket_timeout,
                    socket_connect_timeout=self.config.socket_connect_timeout,
                    health_check_interval=self.config.health_check_interval,
                    decode_responses=self.config.decode_responses
                )
            else:
                pool = redis.ConnectionPool(**pool_kwargs)
            
            # Test the pool
            test_client = redis.Redis(connection_pool=pool)
            test_client.ping()
            
            logger.info("Created synchronous Redis connection pool")
            return pool
            
        except Exception as e:
            self._connection_stats["failed_connections"] += 1
            self._connection_stats["last_error"] = str(e)
            self._connection_stats["last_error_time"] = time.time()
            logger.error(f"Failed to create sync Redis pool: {e}")
            raise
    
    def _create_async_pool(self) -> aioredis.ConnectionPool:
        """Create asynchronous Redis connection pool."""
        try:
            pool_kwargs = self.config.get_pool_kwargs()
            
            if self.config.url and self.config.url != "redis://localhost:6379/0":
                pool = aioredis.ConnectionPool.from_url(
                    self.config.url,
                    max_connections=self.config.max_connections,
                    retry_on_timeout=self.config.retry_on_timeout,
                    socket_timeout=self.config.socket_timeout,
                    socket_connect_timeout=self.config.socket_connect_timeout,
                    health_check_interval=self.config.health_check_interval,
                    decode_responses=self.config.decode_responses
                )
            else:
                pool = aioredis.ConnectionPool(**pool_kwargs)
            
            logger.info("Created asynchronous Redis connection pool")
            return pool
            
        except Exception as e:
            self._connection_stats["failed_connections"] += 1
            self._connection_stats["last_error"] = str(e)
            self._connection_stats["last_error_time"] = time.time()
            logger.error(f"Failed to create async Redis pool: {e}")
            raise
    
    def initialize(self) -> bool:
        """
        Initialize Redis connection pools.
        
        Returns:
            bool: True if initialization successful, False otherwise
        """
        with self._lock:
            if self._initialized:
                return True
            
            try:
                # Create synchronous pool
                self._sync_pool = self._create_sync_pool()
                
                # Create asynchronous pool
                self._async_pool = self._create_async_pool()
                
                self._initialized = True
                self._connection_stats["total_connections"] += 1
                logger.info("Redis connection manager initialized successfully")
                return True
                
            except Exception as e:
                logger.error(f"Failed to initialize Redis connection manager: {e}")
                self._sync_pool = None
                self._async_pool = None
                return False
    
    def get_sync_client(self) -> Optional[redis.Redis]:
        """
        Get synchronous Redis client from pool.
        
        Returns:
            Redis client instance or None if not available
        """
        if not self._initialized and not self.initialize():
            return None
        
        if not self._sync_pool:
            logger.error("Sync Redis pool not available")
            return None
        
        try:
            client = redis.Redis(connection_pool=self._sync_pool)
            self._connection_stats["active_connections"] += 1
            return client
        except Exception as e:
            logger.error(f"Failed to get sync Redis client: {e}")
            self._connection_stats["failed_connections"] += 1
            return None
    
    def get_async_client(self) -> Optional[aioredis.Redis]:
        """
        Get asynchronous Redis client from pool.
        
        Returns:
            Async Redis client instance or None if not available
        """
        if not self._initialized and not self.initialize():
            return None
        
        if not self._async_pool:
            logger.error("Async Redis pool not available")
            return None
        
        try:
            client = aioredis.Redis(connection_pool=self._async_pool)
            self._connection_stats["active_connections"] += 1
            return client
        except Exception as e:
            logger.error(f"Failed to get async Redis client: {e}")
            self._connection_stats["failed_connections"] += 1
            return None
    
    async def test_async_connection(self) -> bool:
        """
        Test asynchronous Redis connection.
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            client = self.get_async_client()
            if not client:
                return False
            
            await client.ping()
            logger.info("Async Redis connection test successful")
            return True
            
        except Exception as e:
            logger.error(f"Async Redis connection test failed: {e}")
            return False
    
    def test_sync_connection(self) -> bool:
        """
        Test synchronous Redis connection.
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            client = self.get_sync_client()
            if not client:
                return False
            
            client.ping()
            logger.info("Sync Redis connection test successful")
            return True
            
        except Exception as e:
            logger.error(f"Sync Redis connection test failed: {e}")
            return False
    
    def reconnect(self) -> bool:
        """
        Attempt to reconnect Redis pools.
        
        Returns:
            bool: True if reconnection successful, False otherwise
        """
        with self._lock:
            try:
                # Close existing pools
                if self._sync_pool:
                    self._sync_pool.disconnect()
                if self._async_pool:
                    # Async pool doesn't have disconnect method, just set to None
                    pass
                
                # Reset state
                self._sync_pool = None
                self._async_pool = None
                self._initialized = False
                
                # Reinitialize
                success = self.initialize()
                if success:
                    self._connection_stats["reconnections"] += 1
                    logger.info("Redis reconnection successful")
                else:
                    logger.error("Redis reconnection failed")
                
                return success
                
            except Exception as e:
                logger.error(f"Error during Redis reconnection: {e}")
                return False
    
    def is_healthy(self) -> bool:
        """
        Check if Redis connection is healthy.
        Performs health check if enough time has passed since last check.
        
        Returns:
            bool: True if connection is healthy, False otherwise
        """
        current_time = time.time()
        
        # Check if we need to perform health check
        if current_time - self._last_health_check < self._health_check_interval:
            return self._initialized and self._sync_pool is not None
        
        # Perform health check
        self._last_health_check = current_time
        
        try:
            # Test sync connection
            if not self.test_sync_connection():
                return False
            
            return True
            
        except Exception as e:
            logger.warning(f"Health check failed: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get connection statistics.
        
        Returns:
            Dict with connection statistics
        """
        return {
            **self._connection_stats,
            "initialized": self._initialized,
            "sync_pool_available": self._sync_pool is not None,
            "async_pool_available": self._async_pool is not None,
            "config": self.config.to_dict(),
            "last_health_check": self._last_health_check,
            "health_check_interval": self._health_check_interval
        }
    
    def close(self):
        """Close all Redis connections and pools."""
        with self._lock:
            try:
                if self._sync_pool:
                    self._sync_pool.disconnect()
                    self._sync_pool = None
                
                if self._async_pool:
                    # Async pool cleanup will happen automatically
                    self._async_pool = None
                
                self._initialized = False
                logger.info("Redis connection manager closed")
                
            except Exception as e:
                logger.error(f"Error closing Redis connection manager: {e}")


# Global connection manager instance
_connection_manager: Optional[RedisConnectionManager] = None
_manager_lock = threading.Lock()


def get_redis_connection() -> RedisConnectionManager:
    """
    Get global Redis connection manager instance.
    
    Returns:
        RedisConnectionManager instance
    """
    global _connection_manager
    
    with _manager_lock:
        if _connection_manager is None:
            _connection_manager = RedisConnectionManager()
            _connection_manager.initialize()
            logger.info("Initialized global Redis connection manager")
    
    return _connection_manager


def reset_redis_connection():
    """Reset global Redis connection manager (useful for testing)."""
    global _connection_manager
    
    with _manager_lock:
        if _connection_manager:
            _connection_manager.close()
        _connection_manager = None
        logger.info("Reset global Redis connection manager")
