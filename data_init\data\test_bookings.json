{"123": [{"patient_name": "<PERSON>", "phone": "+19871234567", "test_type_id": "test_001", "booking_time": "2025-04-23T09:00:00Z", "status": "scheduled", "notes": "Routine annual CBC test."}, {"patient_name": "<PERSON>", "phone": "+19872345678", "test_type_id": "test_002", "booking_time": "2025-04-23T10:30:00Z", "status": "scheduled", "notes": "ECG for asthma patient with occasional chest pain."}, {"patient_name": "<PERSON>", "phone": "+19873456789", "test_type_id": "test_004", "booking_time": "2025-04-24T09:30:00Z", "status": "scheduled", "notes": "Lipid profile for diabetes patient."}], "456": [{"patient_name": "<PERSON>", "phone": "+18761234567", "test_type_id": "test_103", "booking_time": "2025-04-23T11:00:00Z", "status": "scheduled", "notes": "Thyroid ultrasound for suspected nodule."}, {"patient_name": "<PERSON>", "phone": "+18762345678", "test_type_id": "test_101", "booking_time": "2025-04-24T14:00:00Z", "status": "scheduled", "notes": "Brain MRI for persistent headaches."}], "789": [{"patient_name": "<PERSON>", "phone": "+17651234567", "test_type_id": "test_202", "booking_time": "2025-04-22T10:00:00Z", "status": "scheduled", "notes": "Blood glucose test for routine check-up."}, {"patient_name": "<PERSON>", "phone": "+17652345678", "test_type_id": "test_203", "booking_time": "2025-04-23T13:30:00Z", "status": "scheduled", "notes": "Thyroid function test for medication adjustment."}, {"patient_name": "<PERSON>", "phone": "+17653456789", "test_type_id": "test_201", "booking_time": "2025-04-24T11:00:00Z", "status": "scheduled", "notes": "Urinalysis for routine check-up."}]}