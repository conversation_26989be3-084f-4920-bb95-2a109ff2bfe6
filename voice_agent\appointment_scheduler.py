"""
Appointment Scheduling Service for Voice Health Portal

This module provides production-ready appointment scheduling functionality
that replaces hardcoded time slots with dynamic data from Firebase and PostgreSQL.

Features:
- Dynamic time slot generation based on doctor schedules
- Real-time availability checking against existing appointments
- Hospital-specific configuration support
- Redis caching for performance
- Robust error handling and fallbacks
"""

import logging
import asyncio
import os
import aiohttp
import re
from datetime import datetime, timedelta, time
from typing import Dict, Any, List, Optional, Tuple
import json

from .database import (
    get_firestore_db, get_postgres_connection, get_appointments,
    get_test_bookings, get_cached_data, set_cached_data
)
from shared.redis.adapters.python_adapter import get_python_adapter
from .datetime_utils import (
    parse_datetime_robust, parse_date_robust, parse_time_robust,
    combine_date_time_safe, validate_time_format, validate_date_format,
    format_time_display
)

logger = logging.getLogger(__name__)

class AppointmentScheduler:
    """
    Production-ready appointment scheduling service that generates dynamic time slots
    based on doctor schedules, hospital configurations, and existing bookings.
    """

    def __init__(self):
        self.failure_count = {}  # Track failures per hospital for circuit breaker
        self.failure_threshold = 3  # Number of failures before using fallback
        self.failure_reset_time = 300  # Reset failure count after 5 minutes
        self.last_failure_time = {}

        # Performance monitoring
        self.performance_metrics = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'average_response_time': 0.0,
            'last_reset': datetime.now()
        }

        self.default_config = {
            "appointment_duration_minutes": 30,
            "working_hours": {
                "start": "09:00",
                "end": "17:00",
                "lunch_break": {
                    "start": "13:00",
                    "end": "14:00"
                }
            },
            "time_slot_interval_minutes": 30,
            "max_slots_per_day": 16,
            "advance_booking_days": 30,
            "same_day_booking_cutoff_hours": 2
        }

        # Database connectivity status
        self.db_status = {
            'firebase_connected': False,
            'postgres_connections': {},
            'redis_connected': False,
            'last_health_check': None
        }
    
    def _validate_input_parameters(self, hospital_id: str, doctor_id: str, date: str = None) -> Dict[str, Any]:
        """
        Comprehensive input validation for appointment scheduling parameters.

        Args:
            hospital_id: Hospital identifier
            doctor_id: Doctor identifier
            date: Date in YYYY-MM-DD format

        Returns:
            Dict with validation result and sanitized parameters
        """
        validation_result = {
            'valid': True,
            'errors': [],
            'sanitized_params': {},
            'warnings': []
        }

        # Validate hospital_id
        if not hospital_id or not isinstance(hospital_id, str):
            validation_result['valid'] = False
            validation_result['errors'].append("hospital_id is required and must be a non-empty string")
        elif not re.match(r'^[a-zA-Z0-9_-]+$', hospital_id):
            validation_result['valid'] = False
            validation_result['errors'].append("hospital_id contains invalid characters. Only alphanumeric, underscore, and dash allowed")
        elif len(hospital_id) > 50:
            validation_result['valid'] = False
            validation_result['errors'].append("hospital_id is too long (maximum 50 characters)")
        else:
            validation_result['sanitized_params']['hospital_id'] = hospital_id.strip()

        # Validate doctor_id
        if not doctor_id or not isinstance(doctor_id, str):
            validation_result['valid'] = False
            validation_result['errors'].append("doctor_id is required and must be a non-empty string")
        elif not re.match(r'^[a-zA-Z0-9_-]+$', doctor_id):
            validation_result['valid'] = False
            validation_result['errors'].append("doctor_id contains invalid characters. Only alphanumeric, underscore, and dash allowed")
        elif len(doctor_id) > 50:
            validation_result['valid'] = False
            validation_result['errors'].append("doctor_id is too long (maximum 50 characters)")
        else:
            validation_result['sanitized_params']['doctor_id'] = doctor_id.strip()

        # Validate and sanitize date
        if date:
            if not isinstance(date, str):
                validation_result['valid'] = False
                validation_result['errors'].append("date must be a string in YYYY-MM-DD format")
            else:
                # Use robust date parsing
                date_obj = parse_date_robust(date.strip())
                if date_obj:
                    # Check if date is not too far in the past
                    today = datetime.now().date()
                    if date_obj < today:
                        validation_result['warnings'].append(f"Requested date {date} is in the past")

                    # Check if date is not too far in the future (1 year)
                    max_future_date = today.replace(year=today.year + 1)
                    if date_obj > max_future_date:
                        validation_result['valid'] = False
                        validation_result['errors'].append(f"Date {date} is too far in the future (maximum 1 year ahead)")

                    validation_result['sanitized_params']['date'] = date_obj.strftime('%Y-%m-%d')
                else:
                    validation_result['valid'] = False
                    validation_result['errors'].append("Invalid date format. Expected YYYY-MM-DD")
        else:
            # Default to today
            validation_result['sanitized_params']['date'] = datetime.now().strftime('%Y-%m-%d')

        return validation_result

    async def get_available_time_slots(
        self,
        hospital_id: str,
        doctor_id: str,
        date: str = None,
        force_refresh: bool = False
    ) -> List[str]:
        """
        Get available time slots for a doctor on a specific date.

        Args:
            hospital_id: Hospital identifier
            doctor_id: Doctor identifier
            date: Date in YYYY-MM-DD format (defaults to today)
            force_refresh: Skip cache and fetch fresh data

        Returns:
            List of available time slots in "HH:MM AM/PM" format
        """
        start_time = datetime.now()
        cache_hit = False
        success = False

        try:
            # Validate input parameters
            validation = self._validate_input_parameters(hospital_id, doctor_id, date)
            if not validation['valid']:
                error_msg = f"Input validation failed: {'; '.join(validation['errors'])}"
                logger.error(error_msg)
                raise ValueError(error_msg)

            # Log warnings if any
            for warning in validation['warnings']:
                logger.warning(warning)

            # Use sanitized parameters
            hospital_id = validation['sanitized_params']['hospital_id']
            doctor_id = validation['sanitized_params']['doctor_id']
            date = validation['sanitized_params']['date']

            # Check cache first unless force refresh
            cache_key = f"time_slots:{hospital_id}:{doctor_id}:{date}"
            if not force_refresh:
                cached_slots = await get_cached_data(cache_key)
                if cached_slots:
                    cache_hit = True
                    success = True
                    logger.info(f"Retrieved cached time slots for doctor {doctor_id} on {date}")
                    return cached_slots
            
            # Get doctor data and hospital configuration
            doctor_data = await self._get_doctor_data(hospital_id, doctor_id)
            if not doctor_data:
                logger.warning(f"Doctor {doctor_id} not found in hospital {hospital_id}")
                return []
            
            hospital_config = await self._get_hospital_scheduling_config(hospital_id)
            
            # Check if doctor is available on this date
            if not await self._is_doctor_available_on_date(doctor_data, date):
                logger.info(f"Doctor {doctor_id} is not available on {date}")
                return []
            
            # Generate potential time slots based on doctor's schedule
            potential_slots = await self._generate_potential_time_slots(
                doctor_data, hospital_config, date
            )
            
            # Filter out slots that are already booked
            available_slots = await self._filter_available_slots(
                hospital_id, doctor_id, date, potential_slots
            )
            
            # Format slots for display
            formatted_slots = self._format_time_slots(available_slots)
            
            # Cache the results for 5 minutes
            await set_cached_data(cache_key, formatted_slots, ttl=300)

            success = True
            logger.info(f"Generated {len(formatted_slots)} available time slots for doctor {doctor_id} on {date}")
            return formatted_slots

        except Exception as e:
            logger.error(f"Error getting available time slots for doctor {doctor_id}: {e}")
            self._record_failure(hospital_id)
            # Return fallback slots in case of error
            return await self._get_fallback_time_slots(hospital_id)
        finally:
            # Update performance metrics
            response_time = (datetime.now() - start_time).total_seconds()
            self._update_performance_metrics(success, cache_hit, response_time)
    
    async def get_available_test_slots(
        self,
        hospital_id: str,
        test_id: str,
        date: str = None,
        force_refresh: bool = False
    ) -> List[str]:
        """
        Get available time slots for test bookings.

        Args:
            hospital_id: Hospital identifier
            test_id: Test identifier
            date: Date in YYYY-MM-DD format (defaults to today)
            force_refresh: Skip cache and fetch fresh data

        Returns:
            List of available time slots in "HH:MM AM/PM" format
        """
        try:
            # Validate input parameters (reuse doctor validation for test_id)
            validation = self._validate_input_parameters(hospital_id, test_id, date)
            if not validation['valid']:
                error_msg = f"Input validation failed for test slots: {'; '.join(validation['errors'])}"
                logger.error(error_msg)
                raise ValueError(error_msg)

            # Log warnings if any
            for warning in validation['warnings']:
                logger.warning(warning)

            # Use sanitized parameters
            hospital_id = validation['sanitized_params']['hospital_id']
            test_id = validation['sanitized_params']['doctor_id']  # Reusing doctor_id validation for test_id
            date = validation['sanitized_params']['date']
            
            # Check cache first unless force refresh
            cache_key = f"test_slots:{hospital_id}:{test_id}:{date}"
            if not force_refresh:
                cached_slots = await get_cached_data(cache_key)
                if cached_slots:
                    logger.info(f"Retrieved cached test slots for test {test_id} on {date}")
                    return cached_slots
            
            # Get test data and hospital configuration
            test_data = await self._get_test_data(hospital_id, test_id)
            if not test_data:
                logger.warning(f"Test {test_id} not found in hospital {hospital_id}")
                return []
            
            hospital_config = await self._get_hospital_scheduling_config(hospital_id)
            
            # Check if test is available on this date
            if not await self._is_test_available_on_date(test_data, date):
                logger.info(f"Test {test_id} is not available on {date}")
                return []
            
            # Generate potential time slots for tests (usually more flexible than doctor appointments)
            potential_slots = await self._generate_test_time_slots(
                test_data, hospital_config, date
            )
            
            # Filter out slots that are already booked
            available_slots = await self._filter_available_test_slots(
                hospital_id, test_id, date, potential_slots
            )
            
            # Format slots for display
            formatted_slots = self._format_time_slots(available_slots)
            
            # Cache the results for 5 minutes
            await set_cached_data(cache_key, formatted_slots, ttl=300)
            
            logger.info(f"Generated {len(formatted_slots)} available test slots for test {test_id} on {date}")
            return formatted_slots
            
        except Exception as e:
            logger.error(f"Error getting available test slots for test {test_id}: {e}")
            # Return fallback slots in case of error
            return await self._get_fallback_time_slots(hospital_id)

    async def _get_doctor_data(self, hospital_id: str, doctor_id: str) -> Optional[Dict[str, Any]]:
        """Get doctor data from Firebase with caching."""
        try:
            # Check cache first
            cache_key = f"doctor_data:{hospital_id}:{doctor_id}"
            cached_data = await get_cached_data(cache_key)
            if cached_data:
                return cached_data

            db = get_firestore_db()
            loop = asyncio.get_running_loop()

            doctor_ref = db.collection(f'hospital_{hospital_id}_data').document('doctors').collection('doctors').document(doctor_id)
            doctor_doc = await loop.run_in_executor(None, doctor_ref.get)

            if doctor_doc.exists:
                doctor_data = doctor_doc.to_dict()
                doctor_data['id'] = doctor_doc.id

                # Cache for 15 minutes (doctor data changes less frequently)
                await set_cached_data(cache_key, doctor_data, ttl=900)
                return doctor_data

            return None

        except Exception as e:
            logger.error(f"Error getting doctor data for {doctor_id}: {e}")
            return None

    async def _get_test_data(self, hospital_id: str, test_id: str) -> Optional[Dict[str, Any]]:
        """Get test data from Firebase with caching."""
        try:
            # Check cache first
            cache_key = f"test_data:{hospital_id}:{test_id}"
            cached_data = await get_cached_data(cache_key)
            if cached_data:
                return cached_data

            db = get_firestore_db()
            loop = asyncio.get_running_loop()

            test_ref = db.collection(f'hospital_{hospital_id}_data').document('test_info').collection('tests').document(test_id)
            test_doc = await loop.run_in_executor(None, test_ref.get)

            if test_doc.exists:
                test_data = test_doc.to_dict()
                test_data['id'] = test_doc.id

                # Cache for 15 minutes (test data changes less frequently)
                await set_cached_data(cache_key, test_data, ttl=900)
                return test_data

            return None

        except Exception as e:
            logger.error(f"Error getting test data for {test_id}: {e}")
            return None

    async def _get_hospital_scheduling_config(self, hospital_id: str) -> Dict[str, Any]:
        """Get hospital-specific scheduling configuration with caching from staff portal API."""
        try:
            # Check cache first
            cache_key = f"hospital_config:{hospital_id}"
            cached_config = await get_cached_data(cache_key)
            if cached_config:
                return cached_config

            # Try to fetch from staff portal API first
            config = await self._fetch_config_from_staff_portal(hospital_id)
            if config:
                # Cache for 30 minutes (hospital config doesn't change often)
                await set_cached_data(cache_key, config, ttl=1800)
                return config

            # Fallback to Firebase if staff portal is unavailable
            logger.warning(f"Staff portal unavailable, falling back to Firebase for hospital {hospital_id}")
            config = await self._fetch_config_from_firebase(hospital_id)
            if config:
                # Cache for shorter time when using fallback
                await set_cached_data(cache_key, config, ttl=300)
                return config

            # Return default config if both sources fail
            logger.warning(f"Both staff portal and Firebase unavailable, using default config for hospital {hospital_id}")
            return self.default_config

        except Exception as e:
            logger.error(f"Error getting hospital scheduling config for {hospital_id}: {e}")
            return self.default_config

    async def _fetch_config_from_staff_portal(self, hospital_id: str) -> Optional[Dict[str, Any]]:
        """Fetch scheduling configuration from staff portal API with comprehensive error handling."""
        try:
            # Validate environment variables
            staff_portal_url = os.getenv('STAFF_PORTAL_URL')
            if not staff_portal_url:
                logger.warning("STAFF_PORTAL_URL environment variable not set, using default")
                staff_portal_url = 'http://localhost:3000'

            internal_api_key = os.getenv('INTERNAL_API_KEY')
            if not internal_api_key:
                logger.warning("INTERNAL_API_KEY environment variable not set, API authentication may fail")

            # Validate hospital_id before making API call
            if not hospital_id or not isinstance(hospital_id, str):
                logger.error("Invalid hospital_id provided to staff portal config fetch")
                return None

            # Construct API URL with proper encoding
            api_url = f"{staff_portal_url.rstrip('/')}/api/admin/scheduling-config"
            params = {'hospital_id': hospital_id}

            # Use internal API key for authentication
            headers = {
                'Authorization': f"Bearer {internal_api_key}" if internal_api_key else "",
                'Content-Type': 'application/json',
                'User-Agent': 'VoiceAgent/1.0'
            }

            timeout = aiohttp.ClientTimeout(total=10, connect=5)  # 10 second total, 5 second connect

            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(api_url, headers=headers, params=params) as response:
                    if response.status == 200:
                        try:
                            data = await response.json()
                            if isinstance(data, dict) and data.get('success'):
                                config_data = data.get('data')
                                if self._validate_config_data(config_data):
                                    logger.info(f"Successfully fetched and validated scheduling config from staff portal for hospital {hospital_id}")
                                    return config_data
                                else:
                                    logger.warning(f"Invalid config data received from staff portal for hospital {hospital_id}")
                                    return None
                            else:
                                logger.warning(f"Staff portal API returned unsuccessful response for hospital {hospital_id}: {data}")
                                return None
                        except (ValueError, TypeError) as json_error:
                            logger.error(f"Invalid JSON response from staff portal for hospital {hospital_id}: {json_error}")
                            return None
                    elif response.status == 404:
                        logger.info(f"No scheduling config found in staff portal for hospital {hospital_id}")
                        return None
                    elif response.status == 401:
                        logger.error(f"Authentication failed for staff portal API (hospital {hospital_id})")
                        return None
                    else:
                        logger.warning(f"Staff portal API returned status {response.status} for hospital {hospital_id}")
                        return None

        except aiohttp.ClientTimeout:
            logger.warning(f"Timeout fetching config from staff portal for hospital {hospital_id}")
            return None
        except aiohttp.ClientError as client_error:
            logger.warning(f"Client error fetching config from staff portal for hospital {hospital_id}: {client_error}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error fetching config from staff portal for hospital {hospital_id}: {e}")
            return None

    def _validate_config_data(self, config_data: Any) -> bool:
        """Validate configuration data structure and values."""
        if not isinstance(config_data, dict):
            return False

        # Check required fields and their types
        required_fields = {
            'appointment_duration_minutes': int,
            'time_slot_interval_minutes': int,
            'max_slots_per_day': int
        }

        for field, expected_type in required_fields.items():
            if field in config_data:
                if not isinstance(config_data[field], expected_type):
                    logger.warning(f"Invalid type for {field}: expected {expected_type.__name__}, got {type(config_data[field]).__name__}")
                    return False

                # Validate ranges
                if field == 'appointment_duration_minutes' and not (5 <= config_data[field] <= 240):
                    logger.warning(f"Invalid appointment_duration_minutes: {config_data[field]} (must be 5-240)")
                    return False
                elif field == 'time_slot_interval_minutes' and not (5 <= config_data[field] <= 120):
                    logger.warning(f"Invalid time_slot_interval_minutes: {config_data[field]} (must be 5-120)")
                    return False
                elif field == 'max_slots_per_day' and not (1 <= config_data[field] <= 100):
                    logger.warning(f"Invalid max_slots_per_day: {config_data[field]} (must be 1-100)")
                    return False

        # Validate working hours if present
        if 'working_hours' in config_data:
            working_hours = config_data['working_hours']
            if isinstance(working_hours, dict):
                time_pattern = r'^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$'
                for time_field in ['start', 'end']:
                    if time_field in working_hours:
                        if not isinstance(working_hours[time_field], str) or not re.match(time_pattern, working_hours[time_field]):
                            logger.warning(f"Invalid working_hours.{time_field}: {working_hours[time_field]}")
                            return False

        return True

    async def _fetch_config_from_firebase(self, hospital_id: str) -> Optional[Dict[str, Any]]:
        """Fallback method to fetch configuration from Firebase."""
        try:
            db = get_firestore_db()
            loop = asyncio.get_running_loop()

            hospital_ref = db.collection('hospitals').document(f'hospital_{hospital_id}_data')
            hospital_doc = await loop.run_in_executor(None, hospital_ref.get)

            if hospital_doc.exists:
                hospital_data = hospital_doc.to_dict()
                scheduling_config = hospital_data.get('scheduling_config', {})

                # Merge with default config
                config = self.default_config.copy()
                config.update(scheduling_config)

                logger.info(f"Successfully fetched scheduling config from Firebase for hospital {hospital_id}")
                return config

            return None

        except Exception as e:
            logger.error(f"Error fetching config from Firebase for hospital {hospital_id}: {e}")
            return None

    async def _is_doctor_available_on_date(self, doctor_data: Dict[str, Any], date: str) -> bool:
        """Check if doctor is available on the specified date."""
        try:
            # Check daily availability
            daily_availability = doctor_data.get('daily_availability', {})
            if date in daily_availability:
                return daily_availability[date]

            # Check weekly schedule
            date_obj = parse_date_robust(date)
            if not date_obj:
                logger.error(f"Invalid date format for availability check: {date}")
                return False
            day_of_week = date_obj.strftime('%A').lower()

            schedule = doctor_data.get('schedule', {})
            weekly_schedule = schedule.get('weekly', {})

            if day_of_week in weekly_schedule:
                day_schedule = weekly_schedule[day_of_week]
                return day_schedule.get('available', True)

            # Default to available if no specific schedule found
            return True

        except Exception as e:
            logger.error(f"Error checking doctor availability: {e}")
            return False

    async def _is_test_available_on_date(self, test_data: Dict[str, Any], date: str) -> bool:
        """Check if test is available on the specified date."""
        try:
            # Check daily availability
            daily_availability = test_data.get('daily_availability', {})
            if date in daily_availability:
                return daily_availability[date]

            # Tests are generally available unless specifically disabled
            return True

        except Exception as e:
            logger.error(f"Error checking test availability: {e}")
            return False

    async def _generate_potential_time_slots(
        self,
        doctor_data: Dict[str, Any],
        hospital_config: Dict[str, Any],
        date: str
    ) -> List[datetime]:
        """Generate potential time slots based on doctor's schedule and hospital config."""
        try:
            date_obj = parse_date_robust(date)
            if not date_obj:
                logger.error(f"Invalid date format for time slot generation: {date}")
                return []
            day_of_week = date_obj.strftime('%A').lower()

            # Get doctor's schedule for this day
            schedule = doctor_data.get('schedule', {})
            day_schedule = schedule.get(day_of_week, [])

            # If doctor has no schedule for this day, return empty slots
            if not day_schedule:
                logger.info(f"Doctor {doctor_data.get('id')} has no schedule for {day_of_week}")
                return []

            # Parse doctor's schedule (format: ["09:00-12:00", "14:00-17:00"])
            time_ranges = []
            for time_range in day_schedule:
                if isinstance(time_range, str) and '-' in time_range:
                    try:
                        start_str, end_str = time_range.split('-', 1)  # Split only on first dash
                        start_str, end_str = start_str.strip(), end_str.strip()

                        # Validate time format using robust parsing
                        if validate_time_format(start_str) and validate_time_format(end_str):
                            # Additional validation: ensure start time is before end time
                            start_time_obj = parse_time_robust(start_str)
                            end_time_obj = parse_time_robust(end_str)

                            if start_time_obj and end_time_obj and start_time_obj < end_time_obj:
                                time_ranges.append((start_str, end_str))
                                logger.debug(f"Valid time range parsed: {start_str}-{end_str}")
                            else:
                                logger.warning(f"Invalid time range - start time {start_str} is not before end time {end_str}")
                        else:
                            logger.warning(f"Invalid time format in schedule: {time_range}. Expected HH:MM-HH:MM format")
                    except ValueError as ve:
                        logger.warning(f"Error parsing time range {time_range}: {ve}")
                    except Exception as pe:
                        logger.warning(f"Unexpected error parsing time range {time_range}: {pe}")
                else:
                    logger.warning(f"Invalid schedule format: {time_range}. Expected string with '-' separator")

            if not time_ranges:
                # Fallback to hospital default hours with validation
                try:
                    start_time_str = hospital_config.get('working_hours', {}).get('start', '09:00')
                    end_time_str = hospital_config.get('working_hours', {}).get('end', '17:00')

                    # Validate fallback times using robust parsing
                    if validate_time_format(start_time_str) and validate_time_format(end_time_str):
                        start_time_obj = parse_time_robust(start_time_str)
                        end_time_obj = parse_time_robust(end_time_str)

                        if start_time_obj and end_time_obj and start_time_obj < end_time_obj:
                            time_ranges = [(start_time_str, end_time_str)]
                            logger.info(f"Using fallback working hours for doctor {doctor_data.get('id')}: {start_time_str}-{end_time_str}")
                        else:
                            logger.error(f"Invalid fallback working hours: start {start_time_str} >= end {end_time_str}")
                            return []
                    else:
                        logger.error(f"Invalid fallback working hours format: start={start_time_str}, end={end_time_str}")
                        return []
                except Exception as fallback_error:
                    logger.error(f"Error processing fallback working hours: {fallback_error}")
                    return []

            # Generate slots for each time range
            slots = []
            try:
                # Validate interval configuration
                interval_minutes = hospital_config.get('time_slot_interval_minutes', 30)
                if not isinstance(interval_minutes, int) or interval_minutes <= 0 or interval_minutes > 240:
                    logger.warning(f"Invalid time slot interval: {interval_minutes}. Using default 30 minutes")
                    interval_minutes = 30

                interval = timedelta(minutes=interval_minutes)
                max_slots_per_day = hospital_config.get('max_slots_per_day', 16)

                for start_time_str, end_time_str in time_ranges:
                    try:
                        # Parse times for this range using safe datetime construction
                        start_time = combine_date_time_safe(date_obj, start_time_str)
                        end_time = combine_date_time_safe(date_obj, end_time_str)

                        # Validate that parsing was successful and start time is before end time
                        if not start_time or not end_time:
                            logger.warning(f"Failed to parse time range: {start_time_str}-{end_time_str}")
                            continue

                        if start_time >= end_time:
                            logger.warning(f"Invalid time range: start {start_time_str} >= end {end_time_str}")
                            continue

                        current_time = start_time
                        range_slots = 0

                        while current_time < end_time and len(slots) < max_slots_per_day:
                            # Check if slot is in the future (for same-day bookings)
                            if date == datetime.now().strftime('%Y-%m-%d'):
                                cutoff_hours = hospital_config.get('same_day_booking_cutoff_hours', 2)
                                if not isinstance(cutoff_hours, (int, float)) or cutoff_hours < 0:
                                    cutoff_hours = 2
                                cutoff_time = datetime.now() + timedelta(hours=cutoff_hours)
                                if current_time < cutoff_time:
                                    current_time += interval
                                    continue

                            slots.append(current_time)
                            current_time += interval
                            range_slots += 1

                            # Safety check to prevent infinite loops
                            if range_slots > 50:  # Maximum 50 slots per time range
                                logger.warning(f"Reached maximum slots per time range for {start_time_str}-{end_time_str}")
                                break

                    except ValueError as time_error:
                        logger.error(f"Error parsing time range {start_time_str}-{end_time_str}: {time_error}")
                        continue
                    except Exception as range_error:
                        logger.error(f"Unexpected error processing time range {start_time_str}-{end_time_str}: {range_error}")
                        continue

            except Exception as slot_error:
                logger.error(f"Error generating time slots: {slot_error}")
                return []

            return slots

        except Exception as e:
            logger.error(f"Error generating potential time slots: {e}")
            return []

    async def _generate_test_time_slots(
        self,
        test_data: Dict[str, Any],
        hospital_config: Dict[str, Any],
        date: str
    ) -> List[datetime]:
        """Generate potential time slots for test bookings."""
        try:
            # Tests usually have more flexible scheduling
            start_time_str = hospital_config['working_hours']['start']
            end_time_str = hospital_config['working_hours']['end']

            # Parse date safely
            date_obj = parse_date_robust(date)
            if not date_obj:
                logger.error(f"Invalid date format for test slots: {date}")
                return []

            # Use safe datetime construction
            start_time = combine_date_time_safe(date_obj, start_time_str)
            end_time = combine_date_time_safe(date_obj, end_time_str)

            if not start_time or not end_time:
                logger.error(f"Failed to parse test working hours: {start_time_str}-{end_time_str}")
                return []

            # Use test duration or default interval
            test_duration = test_data.get('duration', hospital_config['time_slot_interval_minutes'])
            interval = timedelta(minutes=test_duration)

            slots = []
            current_time = start_time

            while current_time < end_time:
                # Check if slot is in the future (for same-day bookings)
                if date == datetime.now().strftime('%Y-%m-%d'):
                    cutoff_hours = hospital_config.get('same_day_booking_cutoff_hours', 1)  # Less restrictive for tests
                    cutoff_time = datetime.now() + timedelta(hours=cutoff_hours)
                    if current_time < cutoff_time:
                        current_time += interval
                        continue

                slots.append(current_time)
                current_time += interval

            return slots

        except Exception as e:
            logger.error(f"Error generating test time slots: {e}")
            return []

    async def _filter_available_slots(
        self,
        hospital_id: str,
        doctor_id: str,
        date: str,
        potential_slots: List[datetime]
    ) -> List[datetime]:
        """Filter out time slots that are already booked with robust error handling."""
        try:
            # Get existing appointments for this doctor on this date
            appointments = await get_appointments(hospital_id, doctor_id, date)

            # Convert appointments to datetime objects
            booked_times = set()
            for appointment in appointments:
                try:
                    if appointment.get('status') != 'cancelled':
                        start_time = appointment.get('start_time')
                        if start_time:
                            # Use robust datetime parsing
                            parsed_time = parse_datetime_robust(start_time)
                            if parsed_time:
                                booked_times.add(parsed_time)
                            else:
                                logger.warning(f"Failed to parse appointment start_time: {start_time}")
                except Exception as parse_error:
                    logger.warning(f"Error parsing appointment time {appointment}: {parse_error}")
                    continue

            # Filter out booked slots
            available_slots = []
            for slot in potential_slots:
                if slot not in booked_times:
                    available_slots.append(slot)

            logger.info(f"Filtered {len(potential_slots)} potential slots to {len(available_slots)} available slots")
            return available_slots

        except Exception as e:
            logger.error(f"Error filtering available slots for doctor {doctor_id}: {e}")
            logger.error("Unable to verify slot availability due to filtering error")
            # Return empty list to prevent double bookings - data integrity over availability
            logger.warning(f"Returning empty slots for doctor {doctor_id} on {date} to prevent potential double bookings")
            return []
            # Alternative: Return limited slots with warning for emergency fallback
            # return potential_slots[:2]  # Return only first 2 slots as emergency fallback

    async def _filter_available_test_slots(
        self,
        hospital_id: str,
        test_id: str,
        date: str,
        potential_slots: List[datetime]
    ) -> List[datetime]:
        """Filter out test time slots that are already booked."""
        try:
            # Get existing test bookings for this test on this date
            bookings = await get_test_bookings(hospital_id, test_id, date)

            # Convert bookings to datetime objects
            booked_times = set()
            for booking in bookings:
                if booking.get('status') != 'cancelled':
                    booking_time = booking.get('booking_time')
                    if booking_time:
                        # Use robust datetime parsing
                        parsed_time = parse_datetime_robust(booking_time)
                        if parsed_time:
                            booked_times.add(parsed_time)
                        else:
                            logger.warning(f"Failed to parse booking_time: {booking_time}")

            # Filter out booked slots
            available_slots = []
            for slot in potential_slots:
                if slot not in booked_times:
                    available_slots.append(slot)

            return available_slots

        except Exception as e:
            logger.error(f"Error filtering available test slots for test {test_id}: {e}")
            logger.error("Unable to verify test slot availability due to filtering error")
            # Return empty list to prevent double bookings - data integrity over availability
            logger.warning(f"Returning empty test slots for test {test_id} on {date} to prevent potential double bookings")
            return []

    def _format_time_slots(self, slots: List[datetime]) -> List[str]:
        """Format datetime slots to user-friendly strings."""
        try:
            formatted_slots = []
            for slot in slots:
                # Use robust time formatting
                formatted_time = format_time_display(slot)
                if formatted_time:
                    formatted_slots.append(formatted_time)
                else:
                    logger.warning(f"Failed to format time slot: {slot}")

            return formatted_slots

        except Exception as e:
            logger.error(f"Error formatting time slots: {e}")
            return []

    async def _get_fallback_time_slots(self, hospital_id: str) -> List[str]:
        """Get fallback time slots when the main system fails - no hardcoded values."""
        try:
            # Get hospital config for fallback slots
            config = await self._get_hospital_scheduling_config(hospital_id)

            # Validate config structure
            if not isinstance(config, dict):
                logger.error(f"Invalid config structure for hospital {hospital_id}")
                return []

            working_hours = config.get('working_hours', {})
            if not isinstance(working_hours, dict):
                logger.error(f"Invalid working_hours structure for hospital {hospital_id}")
                return []

            # Safely extract and validate working hours
            start_time_str = working_hours.get('start', '09:00')
            end_time_str = working_hours.get('end', '17:00')
            interval_minutes = config.get('time_slot_interval_minutes', 60)

            # Validate time format using robust validation
            if not validate_time_format(start_time_str) or not validate_time_format(end_time_str):
                logger.error(f"Invalid time format in fallback config for hospital {hospital_id}")
                return []

            # Validate interval
            if not isinstance(interval_minutes, int) or interval_minutes <= 0 or interval_minutes > 240:
                logger.warning(f"Invalid interval {interval_minutes} for hospital {hospital_id}, using 60 minutes")
                interval_minutes = 60

            # Parse start and end times
            start_hour, start_minute = map(int, start_time_str.split(':'))
            end_hour, end_minute = map(int, end_time_str.split(':'))

            # Convert to total minutes for easier calculation
            start_total_minutes = start_hour * 60 + start_minute
            end_total_minutes = end_hour * 60 + end_minute

            if start_total_minutes >= end_total_minutes:
                logger.error(f"Invalid time range for hospital {hospital_id}: start >= end")
                return []

            fallback_slots = []
            current_minutes = start_total_minutes
            max_fallback_slots = config.get('max_slots_per_day', 8) // 2  # Half of max slots as fallback

            # Get lunch break configuration to avoid scheduling during lunch
            lunch_break = working_hours.get('lunch_break', {})
            lunch_start_str = lunch_break.get('start', '13:00')
            lunch_end_str = lunch_break.get('end', '14:00')

            lunch_start_minutes = None
            lunch_end_minutes = None

            if validate_time_format(lunch_start_str) and validate_time_format(lunch_end_str):
                lunch_start_hour, lunch_start_minute = map(int, lunch_start_str.split(':'))
                lunch_end_hour, lunch_end_minute = map(int, lunch_end_str.split(':'))
                lunch_start_minutes = lunch_start_hour * 60 + lunch_start_minute
                lunch_end_minutes = lunch_end_hour * 60 + lunch_end_minute

            while current_minutes < end_total_minutes and len(fallback_slots) < max_fallback_slots:
                # Skip lunch break if configured
                if (lunch_start_minutes is not None and lunch_end_minutes is not None and
                    lunch_start_minutes <= current_minutes < lunch_end_minutes):
                    current_minutes = lunch_end_minutes
                    continue

                # Convert back to hours and minutes
                hour = current_minutes // 60
                minute = current_minutes % 60

                # Validate time is within working day
                if hour >= 24:
                    break

                # Use robust time formatting
                time_str = f"{hour:02d}:{minute:02d}"
                formatted_time = format_time_display(time_str)
                if formatted_time:
                    fallback_slots.append(formatted_time)
                else:
                    logger.warning(f"Failed to format fallback time: {time_str}")

                current_minutes += interval_minutes

            if fallback_slots:
                logger.warning(f"Using {len(fallback_slots)} fallback time slots for hospital {hospital_id}: {fallback_slots}")
                return fallback_slots
            else:
                logger.error(f"Could not generate any fallback slots for hospital {hospital_id}")
                return []

        except Exception as e:
            logger.error(f"Error generating fallback time slots for hospital {hospital_id}: {e}")
            # Return empty list instead of hardcoded values to maintain data integrity
            return []

    def _should_use_fallback(self, hospital_id: str) -> bool:
        """Check if we should use fallback due to repeated failures (circuit breaker pattern)."""
        try:
            current_time = datetime.now().timestamp()

            # Reset failure count if enough time has passed
            if hospital_id in self.last_failure_time:
                if current_time - self.last_failure_time[hospital_id] > self.failure_reset_time:
                    self.failure_count[hospital_id] = 0
                    logger.info(f"Reset failure count for hospital {hospital_id}")

            # Check if we've exceeded the failure threshold
            failures = self.failure_count.get(hospital_id, 0)
            if failures >= self.failure_threshold:
                logger.warning(f"Using fallback for hospital {hospital_id} due to {failures} failures")
                return True

            return False

        except Exception as e:
            logger.error(f"Error checking fallback status: {e}")
            return False

    def _record_failure(self, hospital_id: str):
        """Record a failure for circuit breaker tracking."""
        try:
            self.failure_count[hospital_id] = self.failure_count.get(hospital_id, 0) + 1
            self.last_failure_time[hospital_id] = datetime.now().timestamp()
            logger.warning(f"Recorded failure for hospital {hospital_id}, count: {self.failure_count[hospital_id]}")
        except Exception as e:
            logger.error(f"Error recording failure: {e}")

    def _record_success(self, hospital_id: str):
        """Record a success, which can reset the failure count."""
        try:
            if hospital_id in self.failure_count and self.failure_count[hospital_id] > 0:
                self.failure_count[hospital_id] = 0
                logger.info(f"Reset failure count for hospital {hospital_id} after successful operation")
        except Exception as e:
            logger.error(f"Error recording success: {e}")

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics for monitoring."""
        try:
            total_requests = self.performance_metrics['total_requests']
            if total_requests > 0:
                success_rate = (self.performance_metrics['successful_requests'] / total_requests) * 100
                failure_rate = (self.performance_metrics['failed_requests'] / total_requests) * 100
                cache_hit_rate = (self.performance_metrics['cache_hits'] / total_requests) * 100
            else:
                success_rate = failure_rate = cache_hit_rate = 0.0

            return {
                'total_requests': total_requests,
                'successful_requests': self.performance_metrics['successful_requests'],
                'failed_requests': self.performance_metrics['failed_requests'],
                'success_rate_percent': round(success_rate, 2),
                'failure_rate_percent': round(failure_rate, 2),
                'cache_hits': self.performance_metrics['cache_hits'],
                'cache_misses': self.performance_metrics['cache_misses'],
                'cache_hit_rate_percent': round(cache_hit_rate, 2),
                'average_response_time_ms': round(self.performance_metrics['average_response_time'] * 1000, 2),
                'last_reset': self.performance_metrics['last_reset'].isoformat(),
                'failure_counts_by_hospital': self.failure_count.copy()
            }
        except Exception as e:
            logger.error(f"Error getting performance metrics: {e}")
            return {}

    def reset_performance_metrics(self):
        """Reset performance metrics for a fresh start."""
        try:
            self.performance_metrics = {
                'total_requests': 0,
                'successful_requests': 0,
                'failed_requests': 0,
                'cache_hits': 0,
                'cache_misses': 0,
                'average_response_time': 0.0,
                'last_reset': datetime.now()
            }
            logger.info("Performance metrics reset")
        except Exception as e:
            logger.error(f"Error resetting performance metrics: {e}")

    def _update_performance_metrics(self, success: bool, cache_hit: bool, response_time: float):
        """Update performance metrics."""
        try:
            self.performance_metrics['total_requests'] += 1

            if success:
                self.performance_metrics['successful_requests'] += 1
            else:
                self.performance_metrics['failed_requests'] += 1

            if cache_hit:
                self.performance_metrics['cache_hits'] += 1
            else:
                self.performance_metrics['cache_misses'] += 1

            # Update average response time
            total_requests = self.performance_metrics['total_requests']
            current_avg = self.performance_metrics['average_response_time']
            self.performance_metrics['average_response_time'] = (
                (current_avg * (total_requests - 1) + response_time) / total_requests
            )

        except Exception as e:
            logger.error(f"Error updating performance metrics: {e}")

    async def validate_database_connectivity(self, hospital_id: str = None) -> Dict[str, Any]:
        """
        Comprehensive database connectivity validation for production readiness.

        Args:
            hospital_id: Optional hospital ID to test specific hospital database

        Returns:
            Dict with connectivity status for all database systems
        """
        validation_results = {
            'overall_status': 'healthy',
            'firebase': {'status': 'unknown', 'error': None, 'response_time': None},
            'postgres': {'status': 'unknown', 'error': None, 'response_time': None, 'hospitals_tested': []},
            'redis': {'status': 'unknown', 'error': None, 'response_time': None},
            'timestamp': datetime.now().isoformat()
        }

        # Test Firebase connectivity
        firebase_result = await self._test_firebase_connectivity()
        validation_results['firebase'] = firebase_result

        # Test PostgreSQL connectivity
        postgres_result = await self._test_postgres_connectivity(hospital_id)
        validation_results['postgres'] = postgres_result

        # Test Redis connectivity
        redis_result = await self._test_redis_connectivity()
        validation_results['redis'] = redis_result

        # Determine overall status
        failed_services = []
        for service, result in validation_results.items():
            if isinstance(result, dict) and result.get('status') == 'failed':
                failed_services.append(service)

        if failed_services:
            validation_results['overall_status'] = 'degraded' if len(failed_services) < 3 else 'failed'
            validation_results['failed_services'] = failed_services

        # Update internal status
        self.db_status['firebase_connected'] = validation_results['firebase']['status'] == 'healthy'
        self.db_status['redis_connected'] = validation_results['redis']['status'] == 'healthy'
        self.db_status['last_health_check'] = datetime.now()

        logger.info(f"Database connectivity validation completed. Overall status: {validation_results['overall_status']}")
        return validation_results

    async def _test_firebase_connectivity(self) -> Dict[str, Any]:
        """Test Firebase Firestore connectivity."""
        start_time = datetime.now()
        try:
            db = get_firestore_db()
            loop = asyncio.get_running_loop()

            # Test basic connectivity with a simple query
            test_ref = db.collection('hospitals').limit(1)
            await loop.run_in_executor(None, test_ref.get)

            response_time = (datetime.now() - start_time).total_seconds()

            return {
                'status': 'healthy',
                'error': None,
                'response_time': response_time,
                'message': 'Firebase Firestore connection successful'
            }

        except Exception as e:
            response_time = (datetime.now() - start_time).total_seconds()
            error_msg = f"Firebase connectivity test failed: {str(e)}"
            logger.error(error_msg)

            return {
                'status': 'failed',
                'error': error_msg,
                'response_time': response_time,
                'message': 'Firebase Firestore connection failed'
            }

    async def _test_postgres_connectivity(self, hospital_id: str = None) -> Dict[str, Any]:
        """Test PostgreSQL connectivity for one or all hospitals."""
        start_time = datetime.now()
        hospitals_to_test = [hospital_id] if hospital_id else ['1', '2', '3']  # Test common hospital IDs

        results = {
            'status': 'healthy',
            'error': None,
            'response_time': None,
            'hospitals_tested': [],
            'failed_hospitals': []
        }

        for test_hospital_id in hospitals_to_test:
            hospital_start = datetime.now()
            try:
                conn = await get_postgres_connection(test_hospital_id)

                # Test basic query
                cursor = conn.cursor()
                cursor.execute('SELECT 1 as test_connection')
                result = cursor.fetchone()
                cursor.close()

                if result and result[0] == 1:
                    hospital_time = (datetime.now() - hospital_start).total_seconds()
                    results['hospitals_tested'].append({
                        'hospital_id': test_hospital_id,
                        'status': 'healthy',
                        'response_time': hospital_time
                    })
                    self.db_status['postgres_connections'][test_hospital_id] = True
                else:
                    raise Exception("Test query returned unexpected result")

            except Exception as e:
                hospital_time = (datetime.now() - hospital_start).total_seconds()
                error_msg = f"PostgreSQL test failed for hospital {test_hospital_id}: {str(e)}"
                logger.error(error_msg)

                results['failed_hospitals'].append({
                    'hospital_id': test_hospital_id,
                    'error': error_msg,
                    'response_time': hospital_time
                })
                self.db_status['postgres_connections'][test_hospital_id] = False

        # Determine overall PostgreSQL status
        total_response_time = (datetime.now() - start_time).total_seconds()
        results['response_time'] = total_response_time

        if results['failed_hospitals']:
            results['status'] = 'failed' if len(results['failed_hospitals']) == len(hospitals_to_test) else 'degraded'
            results['error'] = f"Failed connections: {len(results['failed_hospitals'])}/{len(hospitals_to_test)}"

        return results

    async def _test_redis_connectivity(self) -> Dict[str, Any]:
        """Test Redis connectivity."""
        start_time = datetime.now()
        try:
            adapter = get_python_adapter()
            async_client = adapter.connection_manager.get_async_client()

            if not async_client:
                raise Exception("Failed to get Redis client from pool")

            # Test basic Redis operations
            test_key = f"health_check:{datetime.now().timestamp()}"
            await async_client.set(test_key, "test_value", ex=10)
            result = await async_client.get(test_key)
            await async_client.delete(test_key)

            if result != b"test_value":
                raise Exception("Redis test operation failed")

            response_time = (datetime.now() - start_time).total_seconds()

            return {
                'status': 'healthy',
                'error': None,
                'response_time': response_time,
                'message': 'Redis connection and operations successful'
            }

        except Exception as e:
            response_time = (datetime.now() - start_time).total_seconds()
            error_msg = f"Redis connectivity test failed: {str(e)}"
            logger.error(error_msg)

            return {
                'status': 'failed',
                'error': error_msg,
                'response_time': response_time,
                'message': 'Redis connection failed'
            }

# Global instance
appointment_scheduler = AppointmentScheduler()
