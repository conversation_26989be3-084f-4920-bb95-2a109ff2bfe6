import React, { useState, useEffect } from 'react';
import { db } from '../../lib/firebase';
import { collection, query, where, onSnapshot, orderBy, doc, updateDoc } from 'firebase/firestore';
import { Toast } from './Toast';

/**
 * NotificationCenter component to display and manage notifications
 * Focuses on WhatsApp OCR-related notifications
 */
export default function NotificationCenter({ hospitalId, onNotificationSelect, onCountUpdate }) {
  const [notifications, setNotifications] = useState([]);
  const [newNotification, setNewNotification] = useState(null);
  const [showToast, setShowToast] = useState(false);

  useEffect(() => {
    if (!hospitalId) return;

    // Set up real-time listener for notifications
    const notificationsRef = collection(db, `hospital_${hospitalId}_data/notifications/notifications`);
    const q = query(
      notificationsRef,
      where('status', '==', 'pending'),
      where('viewed', '==', false),
      orderBy('timestamp', 'desc')
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const newNotifications = [];
      let latestNotification = null;

      snapshot.forEach((doc) => {
        const notification = { id: doc.id, ...doc.data() };
        
        // Filter for WhatsApp OCR notifications
        if (notification.source === 'whatsapp' && 
            (notification.type === 'ocr_error' || 
             notification.type === 'ocr_low_confidence' || 
             notification.type === 'manual_review_requested')) {
          newNotifications.push(notification);
          
          // Check if this is a new notification (within the last minute)
          const notificationTime = new Date(notification.timestamp);
          const currentTime = new Date();
          const timeDifference = (currentTime - notificationTime) / 1000; // in seconds
          
          if (timeDifference < 60 && (!latestNotification || notificationTime > new Date(latestNotification.timestamp))) {
            latestNotification = notification;
          }
        }
      });

      setNotifications(newNotifications);
      
      // Update count if callback provided
      if (onCountUpdate) {
        onCountUpdate(newNotifications.length);
      }
      
      // Show toast for new notification
      if (latestNotification) {
        setNewNotification(latestNotification);
        setShowToast(true);
        
        // Auto-hide toast after 5 seconds
        setTimeout(() => {
          setShowToast(false);
        }, 5000);
      }
    });

    return () => unsubscribe();
  }, [hospitalId]);

  const markAsViewed = async (notificationId) => {
    try {
      const notificationRef = doc(db, `hospital_${hospitalId}_data/notifications/notifications/${notificationId}`);
      await updateDoc(notificationRef, {
        viewed: true,
        status: 'completed',
        updated_at: new Date()
      });
    } catch (error) {
      console.error('Error marking notification as viewed:', error);
    }
  };

  const handleNotificationClick = (notification) => {
    if (onNotificationSelect) {
      onNotificationSelect(notification);
    }
    markAsViewed(notification.id);
  };

  const getNotificationTitle = (notification) => {
    switch (notification.type) {
      case 'ocr_error':
        return 'OCR Processing Error';
      case 'ocr_low_confidence':
        return 'Low Confidence OCR Result';
      case 'manual_review_requested':
        return 'Manual Review Needed';
      default:
        return 'New Notification';
    }
  };

  const getNotificationDescription = (notification) => {
    switch (notification.type) {
      case 'ocr_error':
        return `Error processing prescription from ${notification.phoneNumber}`;
      case 'ocr_low_confidence':
        return `Low confidence (${Math.round(notification.confidence * 100)}%) in OCR results from ${notification.phoneNumber}`;
      case 'manual_review_requested':
        return `Manual review requested for prescription from ${notification.phoneNumber}`;
      default:
        return 'New notification requires your attention';
    }
  };

  return (
    <div className="notification-center">
      {/* Toast for new notifications */}
      {showToast && newNotification && (
        <Toast
          title={getNotificationTitle(newNotification)}
          message={getNotificationDescription(newNotification)}
          type={newNotification.type}
          onClose={() => setShowToast(false)}
          onClick={() => handleNotificationClick(newNotification)}
        />
      )}

      {/* Notification list */}
      <div className="notifications-list">
        <h3 className="text-lg font-semibold mb-4">WhatsApp Prescription Notifications</h3>
        
        {notifications.length === 0 ? (
          <p className="text-gray-500">No pending notifications</p>
        ) : (
          notifications.map((notification) => (
            <div
              key={notification.id}
              className="notification-item p-3 mb-2 bg-white rounded-lg shadow-sm border-l-4 border-blue-500 hover:bg-gray-50 cursor-pointer"
              onClick={() => handleNotificationClick(notification)}
            >
              <div className="flex justify-between">
                <h4 className="font-medium">{getNotificationTitle(notification)}</h4>
                <span className="text-xs text-gray-500">
                  {new Date(notification.timestamp).toLocaleTimeString()}
                </span>
              </div>
              <p className="text-sm text-gray-600">{getNotificationDescription(notification)}</p>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
