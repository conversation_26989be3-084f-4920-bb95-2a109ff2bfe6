import { withRole } from '../../../lib/auth';
import { getFirestore } from '../../../lib/firebase';
import redisClient from '../../../lib/redis_client';

// Admin and receptionist users can manage doctor schedules
export default withRole(async (req, res) => {
  // GET - Fetch doctor schedule
  if (req.method === 'GET') {
    try {
      const { doctor_id } = req.query;

      // SECURITY: Use only authenticated user's hospital_id, ignore any client-provided hospital_id
      const hospital_id = req.user.hospital_id;

      // Validate required parameters
      if (!hospital_id) {
        return res.status(401).json({
          success: false,
          message: 'Hospital ID missing from authenticated user'
        });
      }

      if (!doctor_id) {
        return res.status(400).json({
          success: false,
          message: 'Doctor ID is required'
        });
      }
      
      // Get doctor schedule from Firestore
      const db = getFirestore();
      const doctorRef = db.collection(`hospital_${hospital_id}_data`)
                         .doc('doctors')
                         .collection('doctors')
                         .doc(doctor_id);
      
      const doctorDoc = await doctorRef.get();
      
      if (!doctorDoc.exists) {
        return res.status(404).json({
          success: false,
          message: 'Doctor not found'
        });
      }
      
      const doctorData = doctorDoc.data();
      const schedule = doctorData.schedule || {};
      
      // Provide default schedule structure if not set
      const defaultSchedule = {
        monday: [],
        tuesday: [],
        wednesday: [],
        thursday: [],
        friday: [],
        saturday: [],
        sunday: []
      };
      
      const formattedSchedule = { ...defaultSchedule, ...schedule };
      
      return res.status(200).json({
        success: true,
        data: {
          doctor_id: doctor_id,
          doctor_name: doctorData.name,
          schedule: formattedSchedule,
          availability: doctorData.availability || {},
          daily_availability: doctorData.daily_availability || {}
        }
      });
    } catch (error) {
      console.error('Get doctor schedule error:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
  
  // PUT - Update doctor schedule
  if (req.method === 'PUT') {
    try {
      const { doctor_id, schedule, availability, daily_availability } = req.body;

      // SECURITY: Use only authenticated user's hospital_id, ignore any client-provided hospital_id
      const hospital_id = req.user.hospital_id;

      // Validate required parameters
      if (!hospital_id) {
        return res.status(401).json({
          success: false,
          message: 'Hospital ID missing from authenticated user'
        });
      }

      if (!doctor_id || !schedule) {
        return res.status(400).json({
          success: false,
          message: 'Doctor ID and schedule are required'
        });
      }
      
      // Validate schedule structure
      const validationResult = validateSchedule(schedule);
      if (!validationResult.valid) {
        return res.status(400).json({
          success: false,
          message: `Invalid schedule: ${validationResult.error}`
        });
      }
      
      // Update doctor schedule in Firestore
      const db = getFirestore();
      const doctorRef = db.collection(`hospital_${hospital_id}_data`)
                         .doc('doctors')
                         .collection('doctors')
                         .doc(doctor_id);
      
      const updateData = {
        schedule: schedule,
        updated_at: new Date().toISOString()
      };
      
      if (availability !== undefined) {
        updateData.availability = availability;
      }
      
      if (daily_availability !== undefined) {
        updateData.daily_availability = daily_availability;
      }
      
      await doctorRef.update(updateData);
      
      // Clear related cache entries
      try {
        await clearDoctorCache(hospital_id, doctor_id);
        
        // Trigger voice agent refresh
        await redisClient.triggerAvailabilityRefresh(hospital_id);
      } catch (refreshError) {
        console.warn('Failed to trigger cache refresh:', refreshError);
        // Don't fail the request if refresh fails
      }
      
      return res.status(200).json({
        success: true,
        message: 'Doctor schedule updated successfully'
      });
    } catch (error) {
      console.error('Update doctor schedule error:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
  
  // POST - Set daily availability override
  if (req.method === 'POST') {
    try {
      const { doctor_id, date, available, reason } = req.body;

      // SECURITY: Use only authenticated user's hospital_id, ignore any client-provided hospital_id
      const hospital_id = req.user.hospital_id;

      // Validate required parameters
      if (!hospital_id) {
        return res.status(401).json({
          success: false,
          message: 'Hospital ID missing from authenticated user'
        });
      }

      if (!doctor_id || !date || available === undefined) {
        return res.status(400).json({
          success: false,
          message: 'Doctor ID, date, and availability status are required'
        });
      }
      
      // Validate date format and validity using helper function
      const dateValidation = validateDateFormat(date);
      if (!dateValidation.valid) {
        return res.status(400).json({
          success: false,
          message: dateValidation.error
        });
      }
      
      // Update daily availability in Firestore
      const db = getFirestore();
      const doctorRef = db.collection(`hospital_${hospital_id}_data`)
                         .doc('doctors')
                         .collection('doctors')
                         .doc(doctor_id);
      
      const updateData = {
        [`daily_availability.${date}`]: available,
        updated_at: new Date().toISOString()
      };
      
      if (reason) {
        updateData[`daily_availability_reasons.${date}`] = reason;
      }
      
      await doctorRef.update(updateData);
      
      // Update Redis cache for immediate effect
      try {
        await redisClient.setAvailabilityStatus(hospital_id, doctor_id, 'doctor', date, available);
        
        // Trigger voice agent refresh
        await redisClient.triggerAvailabilityRefresh(hospital_id, date);
      } catch (refreshError) {
        console.warn('Failed to update cache:', refreshError);
      }
      
      return res.status(200).json({
        success: true,
        message: `Doctor availability ${available ? 'enabled' : 'disabled'} for ${date}`
      });
    } catch (error) {
      console.error('Set daily availability error:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
  
  // Method not allowed
  return res.status(405).json({
    success: false,
    message: 'Method not allowed'
  });
}, ['admin', 'receptionist']); // Both admin and receptionist can manage schedules

/**
 * Validate doctor schedule structure with time normalization
 * Compatible with voice_agent datetime_utils.py validation patterns
 */
function validateSchedule(schedule) {
  try {
    const validDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

    for (const day of validDays) {
      if (schedule[day] && Array.isArray(schedule[day])) {
        for (let i = 0; i < schedule[day].length; i++) {
          const timeSlot = schedule[day][i];

          // Validate time slot format (e.g., "09:00-12:00")
          if (typeof timeSlot !== 'string' || !timeSlot.includes('-')) {
            return { valid: false, error: `Invalid time slot format for ${day}: ${timeSlot}` };
          }

          const [start, end] = timeSlot.split('-');
          const trimmedStart = start.trim();
          const trimmedEnd = end.trim();

          // Use same regex pattern as voice_agent datetime_utils.py
          const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;

          if (!timeRegex.test(trimmedStart) || !timeRegex.test(trimmedEnd)) {
            return { valid: false, error: `Invalid time format for ${day}: ${timeSlot}` };
          }

          // Additional validation: ensure start time is before end time
          const startMinutes = parseTimeToMinutes(trimmedStart);
          const endMinutes = parseTimeToMinutes(trimmedEnd);

          if (startMinutes >= endMinutes) {
            return { valid: false, error: `Start time must be before end time for ${day}: ${timeSlot}` };
          }

          // Store normalized format (trimmed values)
          const normalizedTimeSlot = `${trimmedStart}-${trimmedEnd}`;
          if (normalizedTimeSlot !== timeSlot) {
            schedule[day][i] = normalizedTimeSlot;
          }
        }
      }
    }

    return { valid: true };
  } catch (error) {
    return { valid: false, error: 'Invalid schedule format' };
  }
}

/**
 * Convert time string to minutes for comparison
 * Helper function for time validation
 */
function parseTimeToMinutes(timeStr) {
  const [hours, minutes] = timeStr.split(':').map(Number);
  return hours * 60 + minutes;
}

/**
 * Validate date format and actual validity
 * Compatible with voice_agent datetime_utils.py validation approach
 */
function validateDateFormat(dateStr) {
  if (!dateStr || typeof dateStr !== 'string') {
    return { valid: false, error: 'Date must be a string' };
  }

  // Check format first
  const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
  if (!dateRegex.test(dateStr)) {
    return { valid: false, error: 'Date must be in YYYY-MM-DD format' };
  }

  // Validate actual date validity
  const parsedDate = new Date(dateStr);
  if (isNaN(parsedDate.getTime())) {
    return { valid: false, error: 'Invalid date provided' };
  }

  // Ensure the date string matches the parsed date
  // This catches cases like "2024-13-45" which would be auto-corrected
  const formattedDate = parsedDate.toISOString().split('T')[0];
  if (formattedDate !== dateStr) {
    return { valid: false, error: 'Invalid date provided' };
  }

  return { valid: true, date: parsedDate };
}

/**
 * Clear doctor-related cache entries
 */
async function clearDoctorCache(hospital_id, doctor_id) {
  try {
    const client = await redisClient.getClient();
    
    // Clear doctor data cache
    await client.del(`doctor_data:${hospital_id}:${doctor_id}`);
    
    // Clear time slots cache for this doctor
    const timeSlotKeys = await redisClient.scanKeys(`time_slots:${hospital_id}:${doctor_id}:*`);
    if (timeSlotKeys.length > 0) {
      await client.del(...timeSlotKeys);
    }
    
    console.log(`Cleared cache for doctor ${doctor_id} in hospital ${hospital_id}`);
  } catch (error) {
    console.error('Error clearing doctor cache:', error);
  }
}
