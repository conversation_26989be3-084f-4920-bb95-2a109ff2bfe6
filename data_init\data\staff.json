{"123": [{"id": "staff_001", "name": "Admin User", "role": "admin", "email": "<EMAIL>", "credentials": {"userid": "admin_gh", "password_hash": "password"}, "contact": {"phone": "+19876543210", "address": "123 Main Street, City, State, 12345"}, "settings": {"theme": "light", "notifications_enabled": true}}, {"id": "staff_002", "name": "Reception Staff", "role": "receptionist", "email": "<EMAIL>", "credentials": {"userid": "reception_gh", "password_hash": "password"}, "contact": {"phone": "+19876543211", "address": "124 Main Street, City, State, 12345"}, "settings": {"theme": "light", "notifications_enabled": true}}, {"id": "doc_001", "name": "Dr. <PERSON>", "role": "doctor", "email": "<EMAIL>", "credentials": {"userid": "jsmith_gh", "password_hash": "password", "medical_license": "ML12345"}, "contact": {"phone": "+19876543212", "address": "125 Main Street, City, State, 12345"}, "settings": {"theme": "dark", "notifications_enabled": true}}, {"id": "staff_003", "name": "Lab Technician", "role": "lab_technician", "email": "<EMAIL>", "credentials": {"userid": "lab_gh", "password_hash": "password"}, "contact": {"phone": "+19876543213", "address": "126 Main Street, City, State, 12345"}, "settings": {"theme": "light", "notifications_enabled": true}}, {"id": "staff_004", "name": "<PERSON>", "role": "nurse", "email": "<EMAIL>", "credentials": {"userid": "nurse_gh", "password_hash": "password", "nursing_license": "NL54321"}, "contact": {"phone": "+19876543214", "address": "127 Main Street, City, State, 12345"}, "settings": {"theme": "light", "notifications_enabled": true}}], "456": [{"id": "staff_101", "name": "City Medical Admin", "role": "admin", "email": "<EMAIL>", "credentials": {"userid": "admin_cm", "password_hash": "password"}, "contact": {"phone": "+18765432109", "address": "456 Health Avenue, Metropolis, State, 67890"}, "settings": {"theme": "dark", "notifications_enabled": true}}, {"id": "staff_102", "name": "Front Desk", "role": "receptionist", "email": "<EMAIL>", "credentials": {"userid": "frontdesk_cm", "password_hash": "password"}, "contact": {"phone": "+18765432110", "address": "457 Health Avenue, Metropolis, State, 67890"}, "settings": {"theme": "light", "notifications_enabled": true}}, {"id": "doc_101", "name": "Dr. <PERSON>", "role": "doctor", "email": "<EMAIL>", "credentials": {"userid": "mchen_cm", "password_hash": "password", "medical_license": "ML67890"}, "contact": {"phone": "+18765432111", "address": "458 Health Avenue, Metropolis, State, 67890"}, "settings": {"theme": "dark", "notifications_enabled": true}}, {"id": "staff_103", "name": "Imaging Technician", "role": "lab_technician", "email": "<EMAIL>", "credentials": {"userid": "imaging_cm", "password_hash": "password"}, "contact": {"phone": "+18765432112", "address": "459 Health Avenue, Metropolis, State, 67890"}, "settings": {"theme": "light", "notifications_enabled": true}}], "789": [{"id": "staff_201", "name": "Community Clinic Admin", "role": "admin", "email": "<EMAIL>", "credentials": {"userid": "admin_cc", "password_hash": "password"}, "contact": {"phone": "+17654321098", "address": "789 Wellness Road, Smalltown, State, 54321"}, "settings": {"theme": "light", "notifications_enabled": true}}, {"id": "staff_202", "name": "Clinic Receptionist", "role": "receptionist", "email": "<EMAIL>", "credentials": {"userid": "reception_cc", "password_hash": "password"}, "contact": {"phone": "+17654321099", "address": "790 Wellness Road, Smalltown, State, 54321"}, "settings": {"theme": "light", "notifications_enabled": true}}, {"id": "doc_201", "name": "Dr. <PERSON>", "role": "doctor", "email": "<EMAIL>", "credentials": {"userid": "rwilliams_cc", "password_hash": "password", "medical_license": "ML54321"}, "contact": {"phone": "+17654321100", "address": "791 Wellness Road, Smalltown, State, 54321"}, "settings": {"theme": "dark", "notifications_enabled": true}}]}