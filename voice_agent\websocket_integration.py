import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from contextlib import asynccontextmanager
from fastapi import Fast<PERSON><PERSON>, HTTPException
from .websocket_server import websocket_server
from .websocket_config import websocket_config, WebSocketMode
from .websocket_metrics import metrics_collector

# Module-local logger
logger = logging.getLogger(__name__)

class WebSocketIntegration:
    """
    Integrates WebSocket functionality with the existing FastAPI application.
    Provides seamless migration from webhook to WebSocket with backward compatibility.
    """
    
    def __init__(self, app: FastAPI):
        """
        Initialize WebSocket integration
        
        Args:
            app: FastAPI application instance
        """
        self.app = app
        self.websocket_server = websocket_server
        self.config = websocket_config
        self.metrics = metrics_collector
        self.is_websocket_running = False
        
        # Add WebSocket-related routes
        self._add_websocket_routes()
        
        # Add monitoring endpoints
        self._add_monitoring_routes()
    
    def _add_websocket_routes(self):
        """Add WebSocket-related HTTP endpoints"""
        
        @self.app.get("/websocket/status")
        async def websocket_status():
            """Get WebSocket server status"""
            return {
                "websocket_enabled": self.config.is_websocket_enabled(),
                "webhook_enabled": self.config.is_webhook_enabled(),
                "mode": self.config.mode.value,
                "server_running": self.is_websocket_running,
                "configuration": {
                    "host": self.config.host,
                    "port": self.config.port,
                    "max_connections": self.config.max_connections,
                    "subprotocol": self.config.jambonz_subprotocol
                }
            }
        
        @self.app.get("/websocket/metrics")
        async def websocket_metrics():
            """Get WebSocket performance metrics"""
            if not self.config.enable_metrics:
                raise HTTPException(status_code=404, detail="Metrics disabled")
            
            return self.metrics.get_current_metrics()
        
        @self.app.get("/websocket/metrics/summary")
        async def websocket_metrics_summary():
            """Get WebSocket performance summary"""
            if not self.config.enable_metrics:
                raise HTTPException(status_code=404, detail="Metrics disabled")
            
            return self.metrics.get_performance_summary()
        
        @self.app.get("/websocket/metrics/history")
        async def websocket_metrics_history(hours: int = 1):
            """Get historical WebSocket metrics"""
            if not self.config.enable_metrics:
                raise HTTPException(status_code=404, detail="Metrics disabled")
            
            if not (1 <= hours <= 24):
                raise HTTPException(status_code=400, detail="Hours must be between 1 and 24")
            
            return {
                "hours": hours,
                "data": self.metrics.get_historical_metrics(hours)
            }
        
        @self.app.post("/websocket/control/start")
        async def start_websocket_server():
            """Start WebSocket server (admin endpoint)"""
            if self.is_websocket_running:
                return {"status": "already_running", "message": "WebSocket server is already running"}
            
            try:
                await self.start_websocket_server()
                return {"status": "started", "message": "WebSocket server started successfully"}
            except Exception as e:
                logger.error(f"Failed to start WebSocket server: {e}")
                raise HTTPException(status_code=500, detail=f"Failed to start WebSocket server: {str(e)}")
        
        @self.app.post("/websocket/control/stop")
        async def stop_websocket_server():
            """Stop WebSocket server (admin endpoint)"""
            if not self.is_websocket_running:
                return {"status": "already_stopped", "message": "WebSocket server is not running"}
            
            try:
                await self.stop_websocket_server()
                return {"status": "stopped", "message": "WebSocket server stopped successfully"}
            except Exception as e:
                logger.error(f"Failed to stop WebSocket server: {e}")
                raise HTTPException(status_code=500, detail=f"Failed to stop WebSocket server: {str(e)}")
        
        @self.app.get("/websocket/connections")
        async def list_websocket_connections():
            """List active WebSocket connections"""
            server_stats = await self.websocket_server.get_server_stats()
            return {
                "active_connections": server_stats["websocket_manager"]["active_connections"],
                "active_calls": server_stats["websocket_manager"]["active_calls"],
                "hospital_stats": server_stats["websocket_manager"]["hospital_stats"],
                "context_manager": server_stats["context_manager"]
            }
    
    def _add_monitoring_routes(self):
        """Add monitoring and health check endpoints"""
        
        @self.app.get("/health/websocket")
        async def websocket_health():
            """WebSocket health check endpoint"""
            health_status = {
                "status": "healthy" if self.is_websocket_running else "stopped",
                "websocket_enabled": self.config.is_websocket_enabled(),
                "server_running": self.is_websocket_running,
                "timestamp": datetime.now().isoformat()
            }
            
            if self.is_websocket_running:
                try:
                    server_stats = await self.websocket_server.get_server_stats()
                    health_status.update({
                        "active_connections": server_stats["websocket_manager"]["active_connections"],
                        "active_calls": server_stats["websocket_manager"]["active_calls"],
                        "total_connections": server_stats["websocket_manager"]["total_connections"]
                    })
                except Exception as e:
                    health_status["status"] = "unhealthy"
                    health_status["error"] = str(e)
            
            return health_status
        
        @self.app.get("/health/hybrid")
        async def hybrid_health():
            """Combined webhook and WebSocket health check"""
            return {
                "webhook_enabled": self.config.is_webhook_enabled(),
                "websocket_enabled": self.config.is_websocket_enabled(),
                "websocket_running": self.is_websocket_running,
                "mode": self.config.mode.value,
                "timestamp": datetime.now().isoformat()
            }
    
    async def start_websocket_server(self):
        """Start WebSocket server if enabled"""
        if not self.config.is_websocket_enabled():
            logger.info("WebSocket mode disabled, skipping WebSocket server startup")
            return
        
        if self.is_websocket_running:
            logger.warning("WebSocket server already running")
            return
        
        try:
            # Start metrics collection if enabled
            if self.config.enable_metrics:
                await self.metrics.start_collection(int(self.config.metrics_interval))
                logger.info("Started WebSocket metrics collection")
            
            # Start WebSocket server
            await self.websocket_server.start()
            self.is_websocket_running = True
            
            logger.info(f"WebSocket server started on {self.config.host}:{self.config.port}")
            logger.info(f"WebSocket mode: {self.config.mode.value}")
            logger.info(f"Max connections: {self.config.max_connections}")
            
        except Exception as e:
            logger.error(f"Failed to start WebSocket server: {e}")
            self.is_websocket_running = False
            raise
    
    async def stop_websocket_server(self):
        """Stop WebSocket server"""
        if not self.is_websocket_running:
            return
        
        try:
            # Stop metrics collection
            if self.config.enable_metrics:
                await self.metrics.stop_collection()
                logger.info("Stopped WebSocket metrics collection")
            
            # Stop WebSocket server
            await self.websocket_server.stop()
            self.is_websocket_running = False
            
            logger.info("WebSocket server stopped")
            
        except Exception as e:
            logger.error(f"Error stopping WebSocket server: {e}")
            raise
    
    @asynccontextmanager
    async def lifespan_manager(self):
        """Context manager for WebSocket server lifecycle"""
        try:
            # Startup
            if self.config.is_websocket_enabled():
                await self.start_websocket_server()
            
            yield
            
        finally:
            # Shutdown
            if self.is_websocket_running:
                await self.stop_websocket_server()
    
    def get_jambonz_application_url(self, hospital_id: str) -> str:
        """
        Get the appropriate Jambonz application URL based on configuration
        
        Args:
            hospital_id: Hospital identifier
            
        Returns:
            URL for Jambonz application configuration
        """
        if self.config.mode == WebSocketMode.WEBSOCKET_ONLY:
            # Return WebSocket URL
            protocol = "wss" if self.config.host != "localhost" else "ws"
            return f"{protocol}://{self.config.host}:{self.config.port}/ws/jambonz/{hospital_id}"
        
        elif self.config.mode == WebSocketMode.WEBHOOK_ONLY:
            # Return HTTP webhook URL
            # This would be the existing webhook endpoint
            return f"http://{self.config.host}:8000/webhook/call"
        
        else:  # HYBRID mode
            # Return WebSocket URL as primary, with webhook as fallback
            protocol = "wss" if self.config.host != "localhost" else "ws"
            return f"{protocol}://{self.config.host}:{self.config.port}/ws/jambonz/{hospital_id}"
    
    def should_use_websocket(self, request_headers: Dict[str, str] = None) -> bool:
        """
        Determine whether to use WebSocket or webhook based on configuration and request
        
        Args:
            request_headers: Optional request headers for decision making
            
        Returns:
            True if WebSocket should be used, False for webhook
        """
        if self.config.mode == WebSocketMode.WEBSOCKET_ONLY:
            return True
        elif self.config.mode == WebSocketMode.WEBHOOK_ONLY:
            return False
        else:  # HYBRID mode
            # In hybrid mode, prefer WebSocket if server is running
            return self.is_websocket_running
    
    def get_integration_status(self) -> Dict[str, Any]:
        """Get comprehensive integration status"""
        return {
            "configuration": {
                "mode": self.config.mode.value,
                "webhook_enabled": self.config.is_webhook_enabled(),
                "websocket_enabled": self.config.is_websocket_enabled(),
                "metrics_enabled": self.config.enable_metrics
            },
            "runtime": {
                "websocket_server_running": self.is_websocket_running,
                "websocket_host": self.config.host,
                "websocket_port": self.config.port,
                "max_connections": self.config.max_connections
            },
            "urls": {
                "websocket_base": f"ws://{self.config.host}:{self.config.port}/ws/jambonz/",
                "webhook_base": "http://localhost:8000/webhook/",
                "metrics_endpoint": "/websocket/metrics",
                "health_endpoint": "/health/websocket"
            }
        }

def create_websocket_integration(app: FastAPI) -> WebSocketIntegration:
    """
    Create and configure WebSocket integration for FastAPI app
    
    Args:
        app: FastAPI application instance
        
    Returns:
        WebSocketIntegration instance
    """
    integration = WebSocketIntegration(app)
    logger.info("WebSocket integration created and configured")
    return integration

# Function to integrate with existing main.py lifespan
async def integrate_websocket_with_lifespan(app: FastAPI):
    """
    Integrate WebSocket server with existing FastAPI lifespan
    This should be called from the main.py lifespan function
    
    Args:
        app: FastAPI application instance
    """
    integration = create_websocket_integration(app)
    
    # Store integration in app state for access from other parts of the application
    app.state.websocket_integration = integration
    
    # Start WebSocket server if enabled
    if websocket_config.is_websocket_enabled():
        await integration.start_websocket_server()
        logger.info("WebSocket server integrated with FastAPI lifespan")
    
    return integration
