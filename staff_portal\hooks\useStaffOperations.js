import { useCrudOperations } from './useCrudOperations';
import { validateEntity, staffValidationSchema } from '../utils/validationSchemas';

/**
 * Staff-specific CRUD operations hook
 * @param {Function} fetchStaffFunction - Function to refresh staff list
 */
export function useStaffOperations(fetchStaffFunction) {
  const staffCrud = useCrudOperations('staff', fetchStaffFunction, {
    successMessages: {
      create: 'Staff member created successfully',
      update: 'Staff member updated successfully',
      delete: 'Staff member deleted successfully'
    },
    errorMessages: {
      create: 'Failed to create staff member',
      update: 'Failed to update staff member',
      delete: 'Failed to delete staff member'
    }
  });

  // Enhanced staff operations with validation
  const createStaffWithValidation = async (staffData, user) => {
    const errors = validateEntity(staffData, staffValidationSchema);
    if (errors.length > 0) {
      return { success: false, error: 'Validation failed', validationErrors: errors };
    }
    return await staffCrud.create(staffData, user);
  };

  const updateStaffWithValidation = async (staffId, staffData, user) => {
    // For updates, password is optional
    const updateSchema = { ...staffValidationSchema };
    if (!staffData.password) {
      delete updateSchema.password;
    }

    const errors = validateEntity(staffData, updateSchema);
    if (errors.length > 0) {
      return { success: false, error: 'Validation failed', validationErrors: errors };
    }
    return await staffCrud.update(staffId, staffData, user);
  };

  return {
    ...staffCrud,
    // Enhanced methods with validation
    createStaff: createStaffWithValidation,
    updateStaff: updateStaffWithValidation,
    deleteStaff: staffCrud.delete,
    fetchStaff: staffCrud.fetch,
    // Original methods for backward compatibility
    create: staffCrud.create,
    update: staffCrud.update,
    delete: staffCrud.delete,
    fetch: staffCrud.fetch,
    // Validation utilities
    validateStaff: (staffData) => validateEntity(staffData, staffValidationSchema)
  };
}
