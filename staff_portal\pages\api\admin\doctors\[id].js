import { withRole } from '../../../../lib/auth';
import { getDoctorById, updateDoctor, deleteDoctor } from '../../../../lib/firebase';

// Only admin users can manage doctors
export default withRole(async (req, res) => {
  const { id } = req.query;
  
  // Validate doctor ID
  if (!id) {
    return res.status(400).json({
      success: false,
      message: 'Doctor ID is required'
    });
  }

  // PUT - Update doctor
  if (req.method === 'PUT') {
    try {
      const { hospitalId, doctorData } = req.body;
      
      // Validate required parameters
      if (!hospitalId || !doctorData) {
        return res.status(400).json({
          success: false,
          message: 'Hospital ID and doctor data are required'
        });
      }
      
      // Verify hospital ID matches the authenticated user's hospital
      if (hospitalId !== req.user.hospital_id) {
        return res.status(403).json({
          success: false,
          message: 'You are not authorized to update doctors for this hospital'
        });
      }
      
      // Get existing doctor to verify ownership
      const existingDoctorResult = await getDoctorById(id);
      if (!existingDoctorResult.success) {
        return res.status(404).json({
          success: false,
          message: 'Doctor not found'
        });
      }
      
      // Verify doctor belongs to the user's hospital
      if (existingDoctorResult.data.hospital_id !== req.user.hospital_id) {
        return res.status(403).json({
          success: false,
          message: 'You are not authorized to update this doctor'
        });
      }
      
      // Prepare doctor data for updating
      const doctorDataToUpdate = {
        ...doctorData,
        hospital_id: hospitalId,
        updated_at: new Date().toISOString()
      };
      
      // Update doctor in Firestore
      const result = await updateDoctor(id, doctorDataToUpdate);
      
      if (result.success) {
        return res.status(200).json({
          success: true,
          message: 'Doctor updated successfully'
        });
      } else {
        return res.status(500).json({
          success: false,
          message: result.error || 'Failed to update doctor'
        });
      }
    } catch (error) {
      console.error('Update doctor error:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
  
  // DELETE - Delete doctor
  if (req.method === 'DELETE') {
    try {
      const { hospitalId } = req.body;
      
      // Validate required parameters
      if (!hospitalId) {
        return res.status(400).json({
          success: false,
          message: 'Hospital ID is required'
        });
      }
      
      // Verify hospital ID matches the authenticated user's hospital
      if (hospitalId !== req.user.hospital_id) {
        return res.status(403).json({
          success: false,
          message: 'You are not authorized to delete doctors for this hospital'
        });
      }
      
      // Get existing doctor to verify ownership
      const existingDoctorResult = await getDoctorById(id);
      if (!existingDoctorResult.success) {
        return res.status(404).json({
          success: false,
          message: 'Doctor not found'
        });
      }
      
      // Verify doctor belongs to the user's hospital
      if (existingDoctorResult.data.hospital_id !== req.user.hospital_id) {
        return res.status(403).json({
          success: false,
          message: 'You are not authorized to delete this doctor'
        });
      }
      
      // Delete doctor from Firestore
      const result = await deleteDoctor(id);
      
      if (result.success) {
        return res.status(200).json({
          success: true,
          message: 'Doctor deleted successfully'
        });
      } else {
        return res.status(500).json({
          success: false,
          message: result.error || 'Failed to delete doctor'
        });
      }
    } catch (error) {
      console.error('Delete doctor error:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
  
  // Method not allowed
  return res.status(405).json({
    success: false,
    message: 'Method not allowed'
  });
}, ['admin']); // Only admin role can access this endpoint
