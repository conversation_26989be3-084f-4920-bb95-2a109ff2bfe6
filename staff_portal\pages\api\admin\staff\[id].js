import { withRole } from '../../../../lib/auth';
import { getStaffByUserId, updateStaffMember, deleteStaffMember } from '../../../../lib/firebase';
import { hashPassword } from '../../../../lib/auth';

// Only admin users can manage staff
export default withRole(async (req, res) => {
  const { id } = req.query;
  
  // Validate staff ID
  if (!id) {
    return res.status(400).json({
      success: false,
      message: 'Staff ID is required'
    });
  }
  
  // PUT - Update staff member
  if (req.method === 'PUT') {
    try {
      const { hospitalId, staffData } = req.body;
      
      // Validate required parameters
      if (!hospitalId || !staffData) {
        return res.status(400).json({
          success: false,
          message: 'Hospital ID and staff data are required'
        });
      }
      
      // Verify hospital ID matches the authenticated user's hospital
      if (hospitalId !== req.user.hospital_id) {
        return res.status(403).json({
          success: false,
          message: 'You are not authorized to update staff for this hospital'
        });
      }
      
      // Get current staff data to check hospital_id
      const currentStaffResult = await getStaffByUserId(id);
      
      if (!currentStaffResult.success) {
        return res.status(404).json({
          success: false,
          message: 'Staff member not found'
        });
      }
      
      // Verify staff belongs to this hospital
      if (currentStaffResult.data.hospital_id !== hospitalId) {
        return res.status(403).json({
          success: false,
          message: 'Staff member does not belong to this hospital'
        });
      }
      
      // Prepare staff data for updating
      let staffDataToUpdate = { ...staffData };
      
      // If password was provided, hash it
      if (staffData.password) {
        const hashedPassword = await hashPassword(staffData.password);
        
        // Remove plain password and add hashed password
        const { password, ...staffDataWithoutPassword } = staffDataToUpdate;
        staffDataToUpdate = {
          ...staffDataWithoutPassword,
          password_hash: hashedPassword
        };
      }
      
      // Update staff member in Firestore
      const result = await updateStaffMember(id, staffDataToUpdate);
      
      if (result.success) {
        return res.status(200).json({
          success: true,
          message: 'Staff member updated successfully'
        });
      } else {
        return res.status(500).json({
          success: false,
          message: result.error || 'Failed to update staff member'
        });
      }
    } catch (error) {
      console.error('Update staff error:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
  
  // DELETE - Delete staff member
  if (req.method === 'DELETE') {
    try {
      const { hospitalId } = req.body;
      
      // Validate required parameters
      if (!hospitalId) {
        return res.status(400).json({
          success: false,
          message: 'Hospital ID is required'
        });
      }
      
      // Verify hospital ID matches the authenticated user's hospital
      if (hospitalId !== req.user.hospital_id) {
        return res.status(403).json({
          success: false,
          message: 'You are not authorized to delete staff for this hospital'
        });
      }
      
      // Get current staff data to check hospital_id
      const currentStaffResult = await getStaffByUserId(id);
      
      if (!currentStaffResult.success) {
        return res.status(404).json({
          success: false,
          message: 'Staff member not found'
        });
      }
      
      // Verify staff belongs to this hospital
      if (currentStaffResult.data.hospital_id !== hospitalId) {
        return res.status(403).json({
          success: false,
          message: 'Staff member does not belong to this hospital'
        });
      }
      
      // Prevent deleting yourself
      if (id === req.user.id) {
        return res.status(400).json({
          success: false,
          message: 'You cannot delete your own account'
        });
      }
      
      // Delete staff member from Firestore
      const result = await deleteStaffMember(id);
      
      if (result.success) {
        return res.status(200).json({
          success: true,
          message: 'Staff member deleted successfully'
        });
      } else {
        return res.status(500).json({
          success: false,
          message: result.error || 'Failed to delete staff member'
        });
      }
    } catch (error) {
      console.error('Delete staff error:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
  
  // Method not allowed
  return res.status(405).json({
    success: false,
    message: 'Method not allowed'
  });
}, ['admin']); // Only admin role can access this endpoint