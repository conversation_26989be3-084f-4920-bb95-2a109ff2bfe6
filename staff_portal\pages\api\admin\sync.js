import { withRole } from '../../../lib/auth';
import dataSyncService from '../../../lib/data_sync';
import { logger } from '../../../lib/logger';
import { withRateLimit } from '../../../lib/rate_limiter';

/**
 * Admin API endpoint for managing data sync operations
 * Allows triggering manual sync operations and checking sync status
 */
async function handler(req, res) {
  // Only allow GET (status) and POST (trigger sync) methods
  if (req.method !== 'GET' && req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }
  
  // Check status (GET)
  if (req.method === 'GET') {
    const status = dataSyncService.getStatus();
    
    return res.status(200).json({
      success: true,
      status
    });
  }
  
  // Trigger sync operation (POST) - Disabled
  if (req.method === 'POST') {
    logger.warn(`[API_ADMIN_SYNC] Attempt to manually trigger sync by user ${req.user?.id}. Manual syncs are disabled.`);
    return res.status(403).json({
      success: false,
      message: 'Manual triggering of data synchronization is disabled. System auto-syncs periodically.'
    });
  }
}

// Apply rate limiting to prevent abuse
const rateLimitedHandler = withRateLimit(handler, { tier: 'admin' });

// Only allow admin access to this endpoint
export default withRole(rateLimitedHandler, ['admin']);