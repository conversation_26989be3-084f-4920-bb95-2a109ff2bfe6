import { useCrudOperations } from './useCrudOperations';
import { validateEntity, doctorValidationSchema } from '../utils/validationSchemas';

/**
 * Doctor-specific CRUD operations hook
 * @param {Function} fetchDoctorsFunction - Function to refresh doctors list
 */
export function useDoctorOperations(fetchDoctorsFunction) {
  const doctorCrud = useCrudOperations('doctors', fetchDoctorsFunction, {
    successMessages: {
      create: 'Doctor created successfully',
      update: 'Doctor updated successfully',
      delete: 'Doctor deleted successfully'
    },
    errorMessages: {
      create: 'Failed to create doctor',
      update: 'Failed to update doctor',
      delete: 'Failed to delete doctor'
    }
  });

  // Enhanced doctor operations with validation
  const createDoctorWithValidation = async (doctorData, user) => {
    const errors = validateEntity(doctorData, doctorValidationSchema);
    if (errors.length > 0) {
      return { success: false, error: 'Validation failed', validationErrors: errors };
    }
    return await doctor<PERSON>rud.create(doctorData, user);
  };

  const updateDoctorWithValidation = async (doctorId, doctorData, user) => {
    const errors = validateEntity(doctorData, doctorValidationSchema);
    if (errors.length > 0) {
      return { success: false, error: 'Validation failed', validationErrors: errors };
    }
    return await doctorCrud.update(doctorId, doctorData, user);
  };

  return {
    ...doctorCrud,
    // Enhanced methods with validation
    createDoctor: createDoctorWithValidation,
    updateDoctor: updateDoctorWithValidation,
    deleteDoctor: doctorCrud.delete,
    fetchDoctors: doctorCrud.fetch,
    // Original methods for backward compatibility
    create: doctorCrud.create,
    update: doctorCrud.update,
    delete: doctorCrud.delete,
    fetch: doctorCrud.fetch,
    // Validation utilities
    validateDoctor: (doctorData) => validateEntity(doctorData, doctorValidationSchema)
  };
}
