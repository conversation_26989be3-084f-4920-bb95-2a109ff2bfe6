"""
Base Redis Operations for Voice Health Portal

Provides core Redis operations with error handling, monitoring, and optimization.
Supports both synchronous and asynchronous operations.
"""

import json
import logging
import time
from typing import Any, Dict, List, Optional, Union, Tuple
import redis
import redis.asyncio as aioredis
from .connection_manager import RedisConnectionManager, get_redis_connection
from .config import get_redis_config

logger = logging.getLogger(__name__)


class RedisOperations:
    """
    Core Redis operations with comprehensive error handling and monitoring.
    Provides both sync and async methods for different use cases.
    """
    
    def __init__(self, connection_manager: Optional[RedisConnectionManager] = None):
        """
        Initialize Redis operations.
        
        Args:
            connection_manager: Optional connection manager. Uses global if not provided.
        """
        self.connection_manager = connection_manager or get_redis_connection()
        self.config = get_redis_config()
        
        # Operation statistics
        self._stats = {
            "operations": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "errors": 0,
            "total_time": 0.0
        }
    
    def _record_operation(self, operation_time: float, hit: Optional[bool] = None, error: bool = False):
        """Record operation statistics."""
        self._stats["operations"] += 1
        self._stats["total_time"] += operation_time
        
        if error:
            self._stats["errors"] += 1
        elif hit is True:
            self._stats["cache_hits"] += 1
        elif hit is False:
            self._stats["cache_misses"] += 1
    
    def _serialize_value(self, value: Any) -> str:
        """Serialize value for Redis storage."""
        if isinstance(value, (str, bytes)):
            return value
        elif isinstance(value, (int, float)):
            return str(value)
        else:
            return json.dumps(value, ensure_ascii=False)
    
    def _deserialize_value(self, value: Union[str, bytes], as_json: bool = True) -> Any:
        """Deserialize value from Redis."""
        if value is None:
            return None
        
        if isinstance(value, bytes):
            value = value.decode('utf-8')
        
        if not as_json:
            return value
        
        try:
            return json.loads(value)
        except (json.JSONDecodeError, TypeError):
            return value
    
    # Synchronous operations
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None, nx: bool = False) -> bool:
        """
        Set a key-value pair in Redis.
        
        Args:
            key: Redis key
            value: Value to store
            ttl: Time to live in seconds (uses default if not provided)
            nx: Only set if key doesn't exist
            
        Returns:
            bool: True if operation successful, False otherwise
        """
        start_time = time.time()
        
        try:
            client = self.connection_manager.get_sync_client()
            if not client:
                self._record_operation(time.time() - start_time, error=True)
                return False
            
            serialized_value = self._serialize_value(value)
            ttl = ttl or self.config.default_ttl
            
            if nx:
                result = client.set(key, serialized_value, ex=ttl, nx=True)
            else:
                result = client.setex(key, ttl, serialized_value)
            
            self._record_operation(time.time() - start_time)
            return bool(result)
            
        except Exception as e:
            logger.error(f"Redis set error for key {key}: {e}")
            self._record_operation(time.time() - start_time, error=True)
            return False
    
    def get(self, key: str, as_json: bool = True) -> Any:
        """
        Get value from Redis.
        
        Args:
            key: Redis key
            as_json: Whether to deserialize as JSON
            
        Returns:
            Value or None if not found
        """
        start_time = time.time()
        
        try:
            client = self.connection_manager.get_sync_client()
            if not client:
                self._record_operation(time.time() - start_time, hit=False, error=True)
                return None
            
            value = client.get(key)
            hit = value is not None
            
            self._record_operation(time.time() - start_time, hit=hit)
            return self._deserialize_value(value, as_json)
            
        except Exception as e:
            logger.error(f"Redis get error for key {key}: {e}")
            self._record_operation(time.time() - start_time, hit=False, error=True)
            return None
    
    def delete(self, *keys: str) -> int:
        """
        Delete one or more keys from Redis.
        
        Args:
            keys: Redis keys to delete
            
        Returns:
            Number of keys deleted
        """
        start_time = time.time()
        
        try:
            client = self.connection_manager.get_sync_client()
            if not client:
                self._record_operation(time.time() - start_time, error=True)
                return 0
            
            result = client.delete(*keys)
            self._record_operation(time.time() - start_time)
            return result
            
        except Exception as e:
            logger.error(f"Redis delete error for keys {keys}: {e}")
            self._record_operation(time.time() - start_time, error=True)
            return 0
    
    def exists(self, *keys: str) -> int:
        """
        Check if keys exist in Redis.
        
        Args:
            keys: Redis keys to check
            
        Returns:
            Number of existing keys
        """
        start_time = time.time()
        
        try:
            client = self.connection_manager.get_sync_client()
            if not client:
                self._record_operation(time.time() - start_time, error=True)
                return 0
            
            result = client.exists(*keys)
            self._record_operation(time.time() - start_time)
            return result
            
        except Exception as e:
            logger.error(f"Redis exists error for keys {keys}: {e}")
            self._record_operation(time.time() - start_time, error=True)
            return 0
    
    def keys(self, pattern: str = "*") -> List[str]:
        """
        Get keys matching pattern.
        
        Args:
            pattern: Redis key pattern
            
        Returns:
            List of matching keys
        """
        start_time = time.time()
        
        try:
            client = self.connection_manager.get_sync_client()
            if not client:
                self._record_operation(time.time() - start_time, error=True)
                return []
            
            keys = client.keys(pattern)
            result = [key.decode('utf-8') if isinstance(key, bytes) else key for key in keys]
            
            self._record_operation(time.time() - start_time)
            return result
            
        except Exception as e:
            logger.error(f"Redis keys error for pattern {pattern}: {e}")
            self._record_operation(time.time() - start_time, error=True)
            return []
    
    def expire(self, key: str, ttl: int) -> bool:
        """
        Set expiration time for a key.
        
        Args:
            key: Redis key
            ttl: Time to live in seconds
            
        Returns:
            bool: True if operation successful, False otherwise
        """
        start_time = time.time()
        
        try:
            client = self.connection_manager.get_sync_client()
            if not client:
                self._record_operation(time.time() - start_time, error=True)
                return False
            
            result = client.expire(key, ttl)
            self._record_operation(time.time() - start_time)
            return bool(result)
            
        except Exception as e:
            logger.error(f"Redis expire error for key {key}: {e}")
            self._record_operation(time.time() - start_time, error=True)
            return False
    
    def ttl(self, key: str) -> int:
        """
        Get time to live for a key.
        
        Args:
            key: Redis key
            
        Returns:
            TTL in seconds (-1 if no expiration, -2 if key doesn't exist)
        """
        start_time = time.time()
        
        try:
            client = self.connection_manager.get_sync_client()
            if not client:
                self._record_operation(time.time() - start_time, error=True)
                return -2
            
            result = client.ttl(key)
            self._record_operation(time.time() - start_time)
            return result
            
        except Exception as e:
            logger.error(f"Redis ttl error for key {key}: {e}")
            self._record_operation(time.time() - start_time, error=True)
            return -2
    
    # Hash operations
    
    def hset(self, key: str, mapping: Dict[str, Any], ttl: Optional[int] = None) -> int:
        """
        Set hash fields.
        
        Args:
            key: Redis hash key
            mapping: Field-value mapping
            ttl: Optional expiration time
            
        Returns:
            Number of fields added
        """
        start_time = time.time()
        
        try:
            client = self.connection_manager.get_sync_client()
            if not client:
                self._record_operation(time.time() - start_time, error=True)
                return 0
            
            # Serialize values
            serialized_mapping = {
                field: self._serialize_value(value) 
                for field, value in mapping.items()
            }
            
            result = client.hset(key, mapping=serialized_mapping)
            
            if ttl:
                client.expire(key, ttl)
            
            self._record_operation(time.time() - start_time)
            return result
            
        except Exception as e:
            logger.error(f"Redis hset error for key {key}: {e}")
            self._record_operation(time.time() - start_time, error=True)
            return 0
    
    def hget(self, key: str, field: str, as_json: bool = True) -> Any:
        """
        Get hash field value.
        
        Args:
            key: Redis hash key
            field: Hash field
            as_json: Whether to deserialize as JSON
            
        Returns:
            Field value or None if not found
        """
        start_time = time.time()
        
        try:
            client = self.connection_manager.get_sync_client()
            if not client:
                self._record_operation(time.time() - start_time, hit=False, error=True)
                return None
            
            value = client.hget(key, field)
            hit = value is not None
            
            self._record_operation(time.time() - start_time, hit=hit)
            return self._deserialize_value(value, as_json)
            
        except Exception as e:
            logger.error(f"Redis hget error for key {key}, field {field}: {e}")
            self._record_operation(time.time() - start_time, hit=False, error=True)
            return None
    
    def hgetall(self, key: str, as_json: bool = True) -> Dict[str, Any]:
        """
        Get all hash fields and values.
        
        Args:
            key: Redis hash key
            as_json: Whether to deserialize values as JSON
            
        Returns:
            Dict of field-value pairs
        """
        start_time = time.time()
        
        try:
            client = self.connection_manager.get_sync_client()
            if not client:
                self._record_operation(time.time() - start_time, hit=False, error=True)
                return {}
            
            hash_data = client.hgetall(key)
            hit = bool(hash_data)
            
            if not hash_data:
                self._record_operation(time.time() - start_time, hit=False)
                return {}
            
            # Deserialize values
            result = {}
            for field, value in hash_data.items():
                if isinstance(field, bytes):
                    field = field.decode('utf-8')
                result[field] = self._deserialize_value(value, as_json)
            
            self._record_operation(time.time() - start_time, hit=hit)
            return result
            
        except Exception as e:
            logger.error(f"Redis hgetall error for key {key}: {e}")
            self._record_operation(time.time() - start_time, hit=False, error=True)
            return {}
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get operation statistics.
        
        Returns:
            Dict with operation statistics
        """
        stats = self._stats.copy()
        
        if stats["operations"] > 0:
            stats["average_time_ms"] = (stats["total_time"] / stats["operations"]) * 1000
            stats["hit_rate"] = stats["cache_hits"] / (stats["cache_hits"] + stats["cache_misses"]) if (stats["cache_hits"] + stats["cache_misses"]) > 0 else 0
            stats["error_rate"] = stats["errors"] / stats["operations"]
        else:
            stats["average_time_ms"] = 0
            stats["hit_rate"] = 0
            stats["error_rate"] = 0
        
        return stats
    
    def reset_stats(self):
        """Reset operation statistics."""
        self._stats = {
            "operations": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "errors": 0,
            "total_time": 0.0
        }

    # Asynchronous operations

    async def set_async(self, key: str, value: Any, ttl: Optional[int] = None, nx: bool = False) -> bool:
        """
        Asynchronously set a key-value pair in Redis.

        Args:
            key: Redis key
            value: Value to store
            ttl: Time to live in seconds (uses default if not provided)
            nx: Only set if key doesn't exist

        Returns:
            bool: True if operation successful, False otherwise
        """
        start_time = time.time()

        try:
            client = self.connection_manager.get_async_client()
            if not client:
                self._record_operation(time.time() - start_time, error=True)
                return False

            serialized_value = self._serialize_value(value)
            ttl = ttl or self.config.default_ttl

            if nx:
                result = await client.set(key, serialized_value, ex=ttl, nx=True)
            else:
                result = await client.setex(key, ttl, serialized_value)

            self._record_operation(time.time() - start_time)
            return bool(result)

        except Exception as e:
            logger.error(f"Redis async set error for key {key}: {e}")
            self._record_operation(time.time() - start_time, error=True)
            return False

    async def get_async(self, key: str, as_json: bool = True) -> Any:
        """
        Asynchronously get value from Redis.

        Args:
            key: Redis key
            as_json: Whether to deserialize as JSON

        Returns:
            Value or None if not found
        """
        start_time = time.time()

        try:
            client = self.connection_manager.get_async_client()
            if not client:
                self._record_operation(time.time() - start_time, hit=False, error=True)
                return None

            value = await client.get(key)
            hit = value is not None

            self._record_operation(time.time() - start_time, hit=hit)
            return self._deserialize_value(value, as_json)

        except Exception as e:
            logger.error(f"Redis async get error for key {key}: {e}")
            self._record_operation(time.time() - start_time, hit=False, error=True)
            return None

    async def delete_async(self, *keys: str) -> int:
        """
        Asynchronously delete one or more keys from Redis.

        Args:
            keys: Redis keys to delete

        Returns:
            Number of keys deleted
        """
        start_time = time.time()

        try:
            client = self.connection_manager.get_async_client()
            if not client:
                self._record_operation(time.time() - start_time, error=True)
                return 0

            result = await client.delete(*keys)
            self._record_operation(time.time() - start_time)
            return result

        except Exception as e:
            logger.error(f"Redis async delete error for keys {keys}: {e}")
            self._record_operation(time.time() - start_time, error=True)
            return 0

    async def exists_async(self, *keys: str) -> int:
        """
        Asynchronously check if keys exist in Redis.

        Args:
            keys: Redis keys to check

        Returns:
            Number of existing keys
        """
        start_time = time.time()

        try:
            client = self.connection_manager.get_async_client()
            if not client:
                self._record_operation(time.time() - start_time, error=True)
                return 0

            result = await client.exists(*keys)
            self._record_operation(time.time() - start_time)
            return result

        except Exception as e:
            logger.error(f"Redis async exists error for keys {keys}: {e}")
            self._record_operation(time.time() - start_time, error=True)
            return 0

    async def keys_async(self, pattern: str = "*") -> List[str]:
        """
        Asynchronously get keys matching pattern.

        Args:
            pattern: Redis key pattern

        Returns:
            List of matching keys
        """
        start_time = time.time()

        try:
            client = self.connection_manager.get_async_client()
            if not client:
                self._record_operation(time.time() - start_time, error=True)
                return []

            keys = await client.keys(pattern)
            result = [key.decode('utf-8') if isinstance(key, bytes) else key for key in keys]

            self._record_operation(time.time() - start_time)
            return result

        except Exception as e:
            logger.error(f"Redis async keys error for pattern {pattern}: {e}")
            self._record_operation(time.time() - start_time, error=True)
            return []

    async def hset_async(self, key: str, mapping: Dict[str, Any], ttl: Optional[int] = None) -> int:
        """
        Asynchronously set hash fields.

        Args:
            key: Redis hash key
            mapping: Field-value mapping
            ttl: Optional expiration time

        Returns:
            Number of fields added
        """
        start_time = time.time()

        try:
            client = self.connection_manager.get_async_client()
            if not client:
                self._record_operation(time.time() - start_time, error=True)
                return 0

            # Serialize values
            serialized_mapping = {
                field: self._serialize_value(value)
                for field, value in mapping.items()
            }

            result = await client.hset(key, mapping=serialized_mapping)

            if ttl:
                await client.expire(key, ttl)

            self._record_operation(time.time() - start_time)
            return result

        except Exception as e:
            logger.error(f"Redis async hset error for key {key}: {e}")
            self._record_operation(time.time() - start_time, error=True)
            return 0

    async def hgetall_async(self, key: str, as_json: bool = True) -> Dict[str, Any]:
        """
        Asynchronously get all hash fields and values.

        Args:
            key: Redis hash key
            as_json: Whether to deserialize values as JSON

        Returns:
            Dict of field-value pairs
        """
        start_time = time.time()

        try:
            client = self.connection_manager.get_async_client()
            if not client:
                self._record_operation(time.time() - start_time, hit=False, error=True)
                return {}

            hash_data = await client.hgetall(key)
            hit = bool(hash_data)

            if not hash_data:
                self._record_operation(time.time() - start_time, hit=False)
                return {}

            # Deserialize values
            result = {}
            for field, value in hash_data.items():
                if isinstance(field, bytes):
                    field = field.decode('utf-8')
                result[field] = self._deserialize_value(value, as_json)

            self._record_operation(time.time() - start_time, hit=hit)
            return result

        except Exception as e:
            logger.error(f"Redis async hgetall error for key {key}: {e}")
            self._record_operation(time.time() - start_time, hit=False, error=True)
            return {}
