import json
import logging
import threading
import re
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from urllib.parse import urlparse
from .language_config import get_speech_code

# Module-local logger
logger = logging.getLogger(__name__)

class JambonzResponseBuilder:
    """
    Builds proper Jambonz WebSocket responses according to the WebSocket API specification.
    Handles ack messages, command messages, and verb construction.
    """
    
    def __init__(self):
        """Initialize response builder"""
        self.message_counter = 0
        self._counter_lock = threading.Lock()

    def _validate_url(self, url: str, param_name: str) -> None:
        """
        Validate URL format and scheme

        Args:
            url: URL to validate
            param_name: Parameter name for error messages

        Raises:
            ValueError: If URL is invalid
        """
        if not isinstance(url, str) or not url.strip():
            raise ValueError(f"{param_name} must be a non-empty string")

        try:
            parsed = urlparse(url.strip())
            if not parsed.scheme or not parsed.netloc:
                raise ValueError(f"{param_name} must be a valid URL with scheme and netloc")
            if parsed.scheme not in ['http', 'https']:
                raise ValueError(f"{param_name} must use http or https scheme")
        except Exception as e:
            raise ValueError(f"Invalid {param_name} format: {str(e)}")

    def _validate_webhook_path(self, path: str, param_name: str) -> None:
        """
        Validate webhook path format

        Args:
            path: Webhook path to validate
            param_name: Parameter name for error messages

        Raises:
            ValueError: If path is invalid
        """
        if not isinstance(path, str) or not path.strip():
            raise ValueError(f"{param_name} must be a non-empty string")

        path = path.strip()
        if not path.startswith('/'):
            raise ValueError(f"{param_name} must start with '/'")

        # Basic path validation - no spaces, valid characters
        if ' ' in path or any(c in path for c in ['<', '>', '"', '|', '?', '*']):
            raise ValueError(f"{param_name} contains invalid characters")
    
    def create_ack_response(self, 
                          message_id: str,
                          verbs: List[Dict[str, Any]] = None,
                          data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Create acknowledgment response for Jambonz messages
        
        Args:
            message_id: Original message ID to acknowledge
            verbs: List of verbs to execute
            data: Additional data to include
            
        Returns:
            Ack response message
        """
        response = {
            "type": "ack",
            "msgid": message_id,
            "timestamp": datetime.now().isoformat()
        }
        
        if verbs:
            response["verbs"] = verbs
        
        if data:
            response.update(data)
        
        return response
    
    def create_command_message(self,
                             verbs: List[Dict[str, Any]],
                             call_sid: Optional[str] = None,
                             data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Create command message for asynchronous operations

        Args:
            verbs: List of verbs to execute
            call_sid: Optional call SID
            data: Additional data to include

        Returns:
            Command message
        """
        with self._counter_lock:
            self.message_counter += 1
            counter = self.message_counter

        response = {
            "type": "command",
            "msgid": f"cmd_{counter}",
            "verbs": verbs,
            "timestamp": datetime.now().isoformat()
        }
        
        if call_sid:
            response["call_sid"] = call_sid
        
        if data:
            response.update(data)
        
        return response
    
    def create_say_verb(self,
                       text: str,
                       language: str = "hi-IN",
                       voice: Optional[str] = None,
                       loop: int = 1) -> Dict[str, Any]:
        """
        Create say verb for text-to-speech

        Args:
            text: Text to speak (max 1000 characters)
            language: Language code (defaults to Hindi)
            voice: Optional voice name
            loop: Number of times to repeat (1-10)

        Returns:
            Say verb configuration

        Raises:
            ValueError: If any parameter validation fails
        """
        # Validate text
        if not isinstance(text, str) or not text.strip():
            raise ValueError("text must be a non-empty string")
        if len(text) > 1000:
            raise ValueError("text must be 1000 characters or less")

        # Validate language
        if not isinstance(language, str) or not language.strip():
            raise ValueError("language must be a non-empty string")

        # Validate loop
        if not isinstance(loop, int) or loop <= 0 or loop > 10:
            raise ValueError("loop must be a positive integer between 1 and 10")

        # Validate voice if provided
        if voice is not None:
            if not isinstance(voice, str) or not voice.strip():
                raise ValueError("voice must be a non-empty string")

        # Convert language code to speech code if needed
        try:
            speech_code = get_speech_code(language)
        except Exception as e:
            raise ValueError(f"Invalid language code '{language}': {str(e)}")

        verb = {
            "verb": "say",
            "text": text.strip(),
            "language": speech_code,
            "loop": loop
        }

        if voice:
            verb["voice"] = voice.strip()

        return verb
    
    def create_gather_verb(self,
                          say_text: Optional[str] = None,
                          play_url: Optional[str] = None,
                          input_types: List[str] = None,
                          timeout: int = 10,
                          bargein: bool = True,
                          finish_on_key: str = "#",
                          num_digits: Optional[int] = None,
                          speech_timeout: int = 2,
                          language: str = "hi-IN",
                          action_hook: str = "/webhook/gather") -> Dict[str, Any]:
        """
        Create gather verb for collecting user input

        Args:
            say_text: Text to speak before gathering
            play_url: Audio URL to play before gathering
            input_types: Types of input to accept ['speech', 'dtmf']
            timeout: Timeout in seconds (1-300)
            bargein: Allow interruption during prompts
            finish_on_key: Key to finish DTMF input (single character)
            num_digits: Number of DTMF digits to collect (1-30)
            speech_timeout: Speech timeout in seconds (1-10)
            language: Language for speech recognition
            action_hook: Webhook URL for results

        Returns:
            Gather verb configuration

        Raises:
            ValueError: If any parameter validation fails
        """
        # Set default input types
        if input_types is None:
            input_types = ["speech", "dtmf"]

        # Validate input types
        valid_input_types = {"speech", "dtmf"}
        if not isinstance(input_types, list) or not input_types:
            raise ValueError("input_types must be a non-empty list")

        if not all(t in valid_input_types for t in input_types):
            raise ValueError(f"Invalid input types. Must be subset of {valid_input_types}")

        # Validate timeout
        if not isinstance(timeout, int) or timeout <= 0 or timeout > 300:
            raise ValueError("timeout must be a positive integer between 1 and 300 seconds")

        # Validate speech_timeout
        if not isinstance(speech_timeout, int) or speech_timeout <= 0 or speech_timeout > 10:
            raise ValueError("speech_timeout must be a positive integer between 1 and 10 seconds")

        # Validate finish_on_key
        if not isinstance(finish_on_key, str) or len(finish_on_key) != 1:
            raise ValueError("finish_on_key must be a single character")

        # Validate num_digits if provided
        if num_digits is not None:
            if not isinstance(num_digits, int) or num_digits <= 0 or num_digits > 30:
                raise ValueError("num_digits must be a positive integer between 1 and 30")

        # Validate say_text if provided
        if say_text is not None:
            if not isinstance(say_text, str) or not say_text.strip():
                raise ValueError("say_text must be a non-empty string")
            if len(say_text) > 1000:  # Reasonable limit for TTS
                raise ValueError("say_text must be 1000 characters or less")

        # Validate play_url if provided
        if play_url is not None:
            self._validate_url(play_url, "play_url")

        # Validate action_hook
        self._validate_webhook_path(action_hook, "action_hook")

        # Validate language
        if not isinstance(language, str) or not language.strip():
            raise ValueError("language must be a non-empty string")

        # Validate bargein
        if not isinstance(bargein, bool):
            raise ValueError("bargein must be a boolean")

        # Validate that at least one input method has required parameters
        if "dtmf" in input_types and num_digits is None and finish_on_key is None:
            logger.warning("DTMF input enabled but no num_digits or finish_on_key specified")

        # Convert language code to speech code
        try:
            speech_code = get_speech_code(language)
        except Exception as e:
            raise ValueError(f"Invalid language code '{language}': {str(e)}")
        
        verb = {
            "verb": "gather",
            "input": input_types,
            "timeout": timeout,
            "bargein": bargein,
            "finishOnKey": finish_on_key,
            "language": speech_code,
            "actionHook": action_hook
        }
        
        if speech_timeout and "speech" in input_types:
            verb["speechTimeout"] = speech_timeout
        
        if num_digits and "dtmf" in input_types:
            verb["numDigits"] = num_digits
        
        if say_text:
            verb["say"] = {
                "text": say_text,
                "language": speech_code
            }
        
        if play_url:
            verb["play"] = {
                "url": play_url
            }
        
        return verb
    
    def create_play_verb(self,
                        url: str,
                        loop: int = 1,
                        action_hook: Optional[str] = None) -> Dict[str, Any]:
        """
        Create play verb for audio playback

        Args:
            url: Audio file URL
            loop: Number of times to loop (1-100)
            action_hook: Optional webhook for completion

        Returns:
            Play verb configuration

        Raises:
            ValueError: If any parameter validation fails
        """
        # Validate URL
        self._validate_url(url, "url")

        # Validate loop
        if not isinstance(loop, int) or loop <= 0 or loop > 100:
            raise ValueError("loop must be a positive integer between 1 and 100")

        # Validate action_hook if provided
        if action_hook is not None:
            self._validate_webhook_path(action_hook, "action_hook")

        verb = {
            "verb": "play",
            "url": url,
            "loop": loop
        }

        if action_hook:
            verb["actionHook"] = action_hook

        return verb
    
    def create_record_verb(self,
                          action_hook: str,
                          beep: bool = True,
                          finish_on_key: str = "#",
                          max_length: int = 60,
                          silence_threshold: int = 500,
                          silence_timeout: int = 5) -> Dict[str, Any]:
        """
        Create record verb for audio recording

        Args:
            action_hook: Webhook URL for recording results
            beep: Play beep before recording
            finish_on_key: Key to stop recording (single character)
            max_length: Maximum recording length in seconds (1-3600)
            silence_threshold: Silence threshold for auto-stop (100-2000ms)
            silence_timeout: Silence timeout in seconds (1-60)

        Returns:
            Record verb configuration

        Raises:
            ValueError: If any parameter validation fails
        """
        # Validate action_hook
        self._validate_webhook_path(action_hook, "action_hook")

        # Validate beep
        if not isinstance(beep, bool):
            raise ValueError("beep must be a boolean")

        # Validate finish_on_key
        if not isinstance(finish_on_key, str) or len(finish_on_key) != 1:
            raise ValueError("finish_on_key must be a single character")

        # Validate max_length
        if not isinstance(max_length, int) or max_length <= 0 or max_length > 3600:
            raise ValueError("max_length must be a positive integer between 1 and 3600 seconds")

        # Validate silence_threshold
        if not isinstance(silence_threshold, int) or silence_threshold < 100 or silence_threshold > 2000:
            raise ValueError("silence_threshold must be an integer between 100 and 2000 milliseconds")

        # Validate silence_timeout
        if not isinstance(silence_timeout, int) or silence_timeout <= 0 or silence_timeout > 60:
            raise ValueError("silence_timeout must be a positive integer between 1 and 60 seconds")

        return {
            "verb": "record",
            "actionHook": action_hook,
            "beep": beep,
            "finishOnKey": finish_on_key,
            "maxLength": max_length,
            "silenceThreshold": silence_threshold,
            "silenceTimeout": silence_timeout
        }
    
    def create_dial_verb(self,
                        target: str,
                        from_number: Optional[str] = None,
                        timeout: int = 30,
                        action_hook: Optional[str] = None,
                        caller_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Create dial verb for making calls
        
        Args:
            target: Destination number or SIP URI
            from_number: Caller ID number
            timeout: Call timeout in seconds
            action_hook: Webhook for dial status
            caller_id: Caller ID to display
            
        Returns:
            Dial verb configuration
        """
        verb = {
            "verb": "dial",
            "target": target,
            "timeout": timeout
        }
        
        if from_number:
            verb["from"] = from_number
        
        if caller_id:
            verb["callerId"] = caller_id
        
        if action_hook:
            verb["actionHook"] = action_hook
        
        return verb
    
    def create_hangup_verb(self, reason: str = "normal") -> Dict[str, Any]:
        """
        Create hangup verb to end call
        
        Args:
            reason: Reason for hangup
            
        Returns:
            Hangup verb configuration
        """
        return {
            "verb": "hangup",
            "reason": reason
        }
    
    def create_pause_verb(self, length: float) -> Dict[str, Any]:
        """
        Create pause verb for silence
        
        Args:
            length: Pause length in seconds
            
        Returns:
            Pause verb configuration
        """
        return {
            "verb": "pause",
            "length": length
        }
    
    def create_redirect_verb(self, action_hook: str) -> Dict[str, Any]:
        """
        Create redirect verb to change call flow
        
        Args:
            action_hook: New webhook URL
            
        Returns:
            Redirect verb configuration
        """
        return {
            "verb": "redirect",
            "actionHook": action_hook
        }
    
    def create_error_response(self,
                            message_id: str,
                            error_message: str,
                            error_code: Optional[str] = None) -> Dict[str, Any]:
        """
        Create error response for failed operations
        
        Args:
            message_id: Original message ID
            error_message: Error description
            error_code: Optional error code
            
        Returns:
            Error response message
        """
        response = {
            "type": "jambonz:error",
            "msgid": message_id,
            "error": error_message,
            "timestamp": datetime.now().isoformat()
        }
        
        if error_code:
            response["error_code"] = error_code
        
        return response
    
    def create_conversation_flow(self,
                               welcome_text: str,
                               language: str = "hi-IN",
                               timeout: int = 10) -> List[Dict[str, Any]]:
        """
        Create a complete conversation flow with welcome and gather
        
        Args:
            welcome_text: Welcome message
            language: Language code
            timeout: Input timeout
            
        Returns:
            List of verbs for conversation flow
        """
        return [
            self.create_say_verb(welcome_text, language),
            self.create_gather_verb(
                input_types=["speech", "dtmf"],
                timeout=timeout,
                language=language,
                bargein=True
            )
        ]
    
    def validate_response(self, response: Dict[str, Any]) -> bool:
        """
        Validate response format according to Jambonz WebSocket API
        
        Args:
            response: Response to validate
            
        Returns:
            True if valid, False otherwise
        """
        try:
            # Check required fields
            if "type" not in response:
                logger.error("Response missing 'type' field")
                return False
            
            response_type = response["type"]
            
            # Validate based on type
            if response_type == "ack":
                if "msgid" not in response:
                    logger.error("Ack response missing 'msgid' field")
                    return False
            
            elif response_type == "command":
                if "verbs" not in response:
                    logger.error("Command response missing 'verbs' field")
                    return False
                
                # Validate verbs
                verbs = response["verbs"]
                if not isinstance(verbs, list):
                    logger.error("Verbs must be a list")
                    return False
                
                for verb in verbs:
                    if not isinstance(verb, dict) or "verb" not in verb:
                        logger.error("Invalid verb structure")
                        return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating response: {e}")
            return False

# Global response builder instance
response_builder = JambonzResponseBuilder()
