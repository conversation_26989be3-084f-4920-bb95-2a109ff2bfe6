import { withAuth } from '../../../lib/auth';
import { sendDoctorLateNotification } from '../../../lib/db';

export default withAuth(async (req, res) => {
  // Only allow POST method
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  try {
    const { doctorId, lateMinutes, hospitalId } = req.body;
    
    // Validate required parameters
    if (!doctorId || !lateMinutes || !hospitalId) {
      return res.status(400).json({
        success: false,
        message: 'Doctor ID, late minutes, and hospital ID are all required'
      });
    }
    
    // Validate late minutes
    const minutesLate = parseInt(lateMinutes);
    if (isNaN(minutesLate) || minutesLate <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Late minutes must be a positive number'
      });
    }
    
    // Verify hospital ID matches the authenticated user's hospital
    if (hospitalId !== req.user.hospital_id) {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to send notifications for this hospital'
      });
    }
    
    // Send notifications to all patients with appointments for this doctor today
    const result = await sendDoctorLateNotification(doctorId, minutesLate, hospitalId);
    
    if (result.success) {
      return res.status(200).json({
        success: true,
        message: 'Late notifications sent successfully',
        data: result.data
      });
    } else {
      return res.status(500).json({
        success: false,
        message: result.error || 'Failed to send late notifications'
      });
    }
  } catch (error) {
    console.error('Doctor late notification error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});