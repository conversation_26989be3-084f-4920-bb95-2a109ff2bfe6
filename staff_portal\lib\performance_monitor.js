import { logger } from './logger';
import Redis from 'ioredis';

// Initialize Redis client
const redisClient = new Redis(process.env.REDIS_URL || 'redis://localhost:6379');

/**
 * Performance monitoring service for tracking API and database performance
 */
class PerformanceMonitor {
  constructor() {
    this.isEnabled = process.env.ENABLE_PERFORMANCE_MONITORING !== 'false';
    this.samplingRate = parseFloat(process.env.PERFORMANCE_SAMPLING_RATE || '0.1'); // Default 10% sampling
    this.retentionDays = parseInt(process.env.PERFORMANCE_RETENTION_DAYS || '7', 10); // Default 7 days retention
  }

  /**
   * Track the performance of a function or operation
   * @param {string} category - Category of the operation (e.g., 'api', 'db', 'cache')
   * @param {string} operation - Name of the operation
   * @param {number} duration - Duration in milliseconds
   * @param {Object} metadata - Additional metadata about the operation
   */
  async trackPerformance(category, operation, duration, metadata = {}) {
    if (!this.isEnabled) {
      return;
    }
    
    // Apply sampling to reduce storage requirements
    if (Math.random() > this.samplingRate) {
      return;
    }
    
    try {
      // Get current date in YYYY-MM-DD format
      const date = new Date().toISOString().split('T')[0];
      
      // Create keys for different aggregation levels
      const minuteKey = `perf:${date}:${category}:${operation}:${getCurrentMinute()}`;
      const hourKey = `perf:${date}:${category}:${operation}:${getCurrentHour()}`;
      const dayKey = `perf:${date}:${category}:${operation}`;
      
      // Store in Redis using pipeline for efficiency
      const pipeline = redisClient.pipeline();
      
      // Store individual metrics with expiry
      const metricId = `${Date.now()}-${Math.floor(Math.random() * 1000000)}`;
      const metricKey = `perf:metric:${metricId}`;
      
      // Only store full metrics for a sample of requests to save space
      if (Math.random() < 0.01) { // Store detailed metrics for 1% of sampled requests
        pipeline.hmset(metricKey, {
          category,
          operation,
          duration: duration.toString(),
          timestamp: Date.now().toString(),
          ...this.flattenMetadata(metadata)
        });
        pipeline.expire(metricKey, 86400); // Keep for 24 hours
      }
      
      // Update aggregated statistics
      this.updateStats(pipeline, minuteKey, duration, 300); // 5 minutes
      this.updateStats(pipeline, hourKey, duration, 3600); // 1 hour
      this.updateStats(pipeline, dayKey, duration, 86400 * this.retentionDays); // retention days
      
      // Execute all operations
      await pipeline.exec();
    } catch (error) {
      logger.error('Error tracking performance:', error);
    }
  }

  /**
   * Update performance statistics in Redis
   * @param {Object} pipeline - Redis pipeline
   * @param {string} key - Redis key for the stats
   * @param {number} duration - Duration to add
   * @param {number} expiry - Expiry time in seconds
   */
  updateStats(pipeline, key, duration, expiry) {
    // Use a Redis hash to store statistics
    pipeline.hincrby(key, 'count', 1);
    pipeline.hincrby(key, 'total', Math.round(duration));
    
    // Update min/max values using Lua script for atomicity
    pipeline.eval(`
      local current = redis.call('HGET', KEYS[1], 'min')
      local new_value = tonumber(ARGV[1])
      if current == false or tonumber(current) > new_value then
        redis.call('HSET', KEYS[1], 'min', new_value)
      end
      
      current = redis.call('HGET', KEYS[1], 'max')
      if current == false or tonumber(current) < new_value then
        redis.call('HSET', KEYS[1], 'max', new_value)
      end
      
      return 1
    `, 1, key, Math.round(duration));
    
    pipeline.expire(key, expiry);
  }

  /**
   * Flatten metadata object for storage in Redis
   * @param {Object} metadata - Metadata object
   * @returns {Object} Flattened metadata
   */
  flattenMetadata(metadata) {
    const result = {};
    
    Object.entries(metadata).forEach(([key, value]) => {
      if (typeof value === 'object' && value !== null) {
        try {
          result[`meta_${key}`] = JSON.stringify(value);
        } catch (e) {
          result[`meta_${key}`] = 'ERROR: Could not stringify';
        }
      } else {
        result[`meta_${key}`] = String(value);
      }
    });
    
    return result;
  }

  /**
   * Create a performance monitoring middleware for Next.js API routes
   * @returns {Function} Next.js API middleware
   */
  createMiddleware() {
    return (handler) => {
      return async (req, res) => {
        if (!this.isEnabled) {
          return handler(req, res);
        }
        
        const start = Date.now();
        
        // Add a hook to track performance after response is sent
        const originalEnd = res.end;
        res.end = (...args) => {
          const duration = Date.now() - start;
          
          // Call original end first to send the response
          originalEnd.apply(res, args);
          
          // Then track performance (don't delay the response)
          this.trackPerformance('api', req.url, duration, {
            method: req.method,
            status: res.statusCode,
            userId: req.user?.id,
            hospitalId: req.user?.hospital_id
          }).catch(err => {
            logger.error('Error in performance tracking:', err);
          });
        };
        
        return handler(req, res);
      };
    };
  }

  /**
   * Create a database query performance tracker
   * @returns {Function} Function wrapper for tracking DB queries
   */
  createDbTracker() {
    return (fn, operation) => {
      return async (...args) => {
        if (!this.isEnabled) {
          return fn(...args);
        }
        
        const start = Date.now();
        
        try {
          const result = await fn(...args);
          const duration = Date.now() - start;
          
          // Track performance asynchronously
          this.trackPerformance('db', operation, duration, {
            args: args.map(arg => 
              typeof arg === 'string' ? arg.substring(0, 100) : 'non-string-arg'
            ).join(',').substring(0, 200)
          }).catch(err => {
            logger.error('Error in DB performance tracking:', err);
          });
          
          return result;
        } catch (error) {
          const duration = Date.now() - start;
          
          // Also track failed operations
          this.trackPerformance('db', `${operation}:error`, duration, {
            error: error.message
          }).catch(err => {
            logger.error('Error in DB error performance tracking:', err);
          });
          
          throw error;
        }
      };
    };
  }

  /**
   * Get performance statistics for a specific period
   * @param {string} category - Category to filter (or 'all')
   * @param {string} period - Time period ('minute', 'hour', 'day')
   * @param {string} date - Date in YYYY-MM-DD format (defaults to today)
   * @returns {Promise<Object>} Performance statistics
   */
  async getStats(category = 'all', period = 'day', date = null) {
    try {
      // Default to today
      const targetDate = date || new Date().toISOString().split('T')[0];
      
      // Determine key pattern based on period and category
      let keyPattern;
      if (category === 'all') {
        keyPattern = `perf:${targetDate}:*`;
        if (period === 'minute') {
          keyPattern += `:${getCurrentMinute()}`;
        } else if (period === 'hour') {
          keyPattern += `:${getCurrentHour()}`;
        }
      } else {
        keyPattern = `perf:${targetDate}:${category}:*`;
        if (period === 'minute') {
          keyPattern += `:${getCurrentMinute()}`;
        } else if (period === 'hour') {
          keyPattern += `:${getCurrentHour()}`;
        }
      }
      
      // Get all matching keys using production-safe SCAN
      const keys = [];
      const stream = redisClient.scanStream({
        match: keyPattern,
        count: 100
      });

      await new Promise((resolve, reject) => {
        stream.on('data', (resultKeys) => {
          keys.push(...resultKeys);
        });
        stream.on('end', resolve);
        stream.on('error', reject);
      });
      
      if (!keys || keys.length === 0) {
        return {
          totalRequests: 0,
          avgDuration: 0,
          minDuration: 0,
          maxDuration: 0,
          categories: {},
          operations: {}
        };
      }
      
      // Get all stats in a pipeline
      const pipeline = redisClient.pipeline();
      keys.forEach(key => {
        pipeline.hgetall(key);
      });
      
      const results = await pipeline.exec();
      
      // Process and aggregate results
      const stats = {
        totalRequests: 0,
        totalDuration: 0,
        minDuration: Number.MAX_SAFE_INTEGER,
        maxDuration: 0,
        categories: {},
        operations: {}
      };
      
      results.forEach(([err, result], index) => {
        if (err || !result) return;
        
        const key = keys[index];
        const keyParts = key.split(':');
        const keyCategory = keyParts[2];
        const keyOperation = keyParts[3];
        
        // Extract stats from Redis hash
        const count = parseInt(result.count || '0', 10);
        const total = parseInt(result.total || '0', 10);
        const min = parseInt(result.min || '0', 10);
        const max = parseInt(result.max || '0', 10);
        
        // Update global stats
        stats.totalRequests += count;
        stats.totalDuration += total;
        stats.minDuration = Math.min(stats.minDuration, min);
        stats.maxDuration = Math.max(stats.maxDuration, max);
        
        // Update category stats
        if (!stats.categories[keyCategory]) {
          stats.categories[keyCategory] = {
            requests: 0,
            totalDuration: 0,
            avgDuration: 0
          };
        }
        stats.categories[keyCategory].requests += count;
        stats.categories[keyCategory].totalDuration += total;
        
        // Update operation stats
        const operationKey = `${keyCategory}:${keyOperation}`;
        if (!stats.operations[operationKey]) {
          stats.operations[operationKey] = {
            requests: 0,
            totalDuration: 0,
            avgDuration: 0,
            minDuration: min,
            maxDuration: max
          };
        }
        stats.operations[operationKey].requests += count;
        stats.operations[operationKey].totalDuration += total;
        stats.operations[operationKey].minDuration = Math.min(
          stats.operations[operationKey].minDuration, 
          min
        );
        stats.operations[operationKey].maxDuration = Math.max(
          stats.operations[operationKey].maxDuration,
          max
        );
      });
      
      // Calculate averages
      stats.avgDuration = stats.totalRequests > 0 
        ? stats.totalDuration / stats.totalRequests 
        : 0;
      
      // Set min to 0 if no data was found
      if (stats.minDuration === Number.MAX_SAFE_INTEGER) {
        stats.minDuration = 0;
      }
      
      // Calculate category and operation averages
      Object.keys(stats.categories).forEach(category => {
        const catStats = stats.categories[category];
        catStats.avgDuration = catStats.requests > 0 
          ? catStats.totalDuration / catStats.requests 
          : 0;
      });
      
      Object.keys(stats.operations).forEach(operation => {
        const opStats = stats.operations[operation];
        opStats.avgDuration = opStats.requests > 0 
          ? opStats.totalDuration / opStats.requests 
          : 0;
      });
      
      return stats;
    } catch (error) {
      logger.error('Error getting performance stats:', error);
      throw error;
    }
  }

  /**
   * Clear performance metrics older than retention period
   */
  async clearOldMetrics() {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.retentionDays);
      const cutoffStr = cutoffDate.toISOString().split('T')[0];
      
      // Find keys older than cutoff using production-safe SCAN
      const keys = [];
      const stream = redisClient.scanStream({
        match: 'perf:*',
        count: 100
      });

      await new Promise((resolve, reject) => {
        stream.on('data', (resultKeys) => {
          keys.push(...resultKeys);
        });
        stream.on('end', resolve);
        stream.on('error', reject);
      });

      const oldKeys = keys.filter(key => {
        const parts = key.split(':');
        return parts[1] < cutoffStr;
      });
      
      if (oldKeys.length > 0) {
        await redisClient.del(...oldKeys);
        logger.info(`Cleared ${oldKeys.length} old performance metrics`);
      }
    } catch (error) {
      logger.error('Error clearing old performance metrics:', error);
    }
  }
}

/**
 * Get current minute for timestamping
 * @returns {string} Current minute in HH:MM format
 */
function getCurrentMinute() {
  const now = new Date();
  return `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;
}

/**
 * Get current hour for timestamping
 * @returns {string} Current hour in HH:00 format
 */
function getCurrentHour() {
  const now = new Date();
  return `${String(now.getHours()).padStart(2, '0')}:00`;
}

// Create singleton instance
const performanceMonitor = new PerformanceMonitor();

// Export monitor and middleware
export const withPerformanceTracking = performanceMonitor.createMiddleware();
export const trackDbQuery = performanceMonitor.createDbTracker();
export default performanceMonitor;