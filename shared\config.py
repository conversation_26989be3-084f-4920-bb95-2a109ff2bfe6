"""
Configuration for shared LLM service

This module contains configuration settings for the unified LLM service
used by both voice_agent and whatsapp_agent.
"""

import os
from typing import Dict, Any

# LLM Configuration
LLM_CONFIG = {
    "model": "gpt-4o-mini",  # GPT-4.1 nano
    "temperature": 0.7,
    "max_tokens": 1000,
    "top_p": 0.95,
    "frequency_penalty": 0,
    "presence_penalty": 0
}

# API Configuration
API_CONFIG = {
    "openai_api_key": os.getenv("OPENAI_API_KEY"),
    "openai_organization": os.getenv("OPENAI_ORGANIZATION_ID"),
    "timeout": 30,
    "max_retries": 3
}

# Cache Configuration
CACHE_CONFIG = {
    "enabled": True,
    "ttl": 3600,  # 1 hour
    "redis_url": os.getenv("REDIS_URL", "redis://localhost:6379"),
    "key_prefix": "llm_cache:"
}

# Language Configuration
LANGUAGE_CONFIG = {
    "supported_languages": ["en", "hi", "bn"],
    "default_language": "en",
    "language_names": {
        "en": "English",
        "hi": "Hindi",
        "bn": "Bengali"
    }
}

# Function Configuration
FUNCTION_CONFIG = {
    "max_function_calls": 5,
    "function_timeout": 30,
    "enable_parallel_calls": True
}

# Usage Tracking Configuration
USAGE_CONFIG = {
    "track_usage": True,
    "log_function_calls": True,
    "cost_tracking": True,
    "token_costs": {
        "gpt-4o-mini": {
            "input": 0.00015,  # per 1K tokens
            "output": 0.0006   # per 1K tokens
        }
    }
}

def get_config() -> Dict[str, Any]:
    """Get complete configuration dictionary."""
    return {
        "llm": LLM_CONFIG,
        "api": API_CONFIG,
        "cache": CACHE_CONFIG,
        "language": LANGUAGE_CONFIG,
        "functions": FUNCTION_CONFIG,
        "usage": USAGE_CONFIG
    }

def validate_config() -> Dict[str, Any]:
    """Validate configuration and return status."""
    issues = []
    
    # Check required environment variables
    if not API_CONFIG["openai_api_key"]:
        issues.append("OPENAI_API_KEY environment variable not set")
    
    # Check Redis configuration if caching is enabled
    if CACHE_CONFIG["enabled"] and not CACHE_CONFIG["redis_url"]:
        issues.append("Redis URL not configured but caching is enabled")
    
    return {
        "valid": len(issues) == 0,
        "issues": issues
    }
