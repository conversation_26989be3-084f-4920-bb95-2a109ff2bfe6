import { Save, AlertTriangle } from 'react-feather';
import { useState, useEffect } from 'react';

export default function SchedulingConfig({
  schedulingConfig,
  setSchedulingConfig,
  onSave,
  loading
}) {
  const [validationErrors, setValidationErrors] = useState({});
  const [isValid, setIsValid] = useState(true);

  // Helper function to convert time string to minutes with robust validation
  const timeToMinutes = (timeStr) => {
    if (!timeStr || typeof timeStr !== 'string') return 0;

    // Validate time format (HH:MM) - supports both 24-hour format
    if (!/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(timeStr)) {
      console.warn(`Invalid time format: ${timeStr}. Expected format: HH:MM (24-hour)`);
      return 0;
    }

    const [hours, minutes] = timeStr.split(':').map(Number);
    if (isNaN(hours) || isNaN(minutes)) {
      console.warn(`Invalid time values: ${timeStr}. Hours and minutes must be numeric.`);
      return 0;
    }

    return hours * 60 + minutes;
  };

  // Validate configuration in real-time
  useEffect(() => {
    const errors = {};

    // Validate working hours
    if (schedulingConfig.working_hours?.start && schedulingConfig.working_hours?.end) {
      const startMinutes = timeToMinutes(schedulingConfig.working_hours.start);
      const endMinutes = timeToMinutes(schedulingConfig.working_hours.end);

      if (endMinutes <= startMinutes) {
        errors.workingHours = 'End time must be after start time';
      }

      // Validate lunch break
      if (schedulingConfig.working_hours.lunch_break?.start && schedulingConfig.working_hours.lunch_break?.end) {
        const lunchStartMinutes = timeToMinutes(schedulingConfig.working_hours.lunch_break.start);
        const lunchEndMinutes = timeToMinutes(schedulingConfig.working_hours.lunch_break.end);

        if (lunchEndMinutes <= lunchStartMinutes) {
          errors.lunchBreak = 'Lunch break end time must be after start time';
        } else if (lunchStartMinutes < startMinutes || lunchEndMinutes > endMinutes) {
          errors.lunchBreak = 'Lunch break must be within working hours';
        }
      }

      // Validate appointment duration vs time slot interval
      if (schedulingConfig.appointment_duration_minutes && schedulingConfig.time_slot_interval_minutes) {
        if (schedulingConfig.appointment_duration_minutes % schedulingConfig.time_slot_interval_minutes !== 0) {
          errors.appointmentDuration = 'Appointment duration must be a multiple of time slot interval';
        }
      }

      // Validate max slots vs theoretical maximum
      if (schedulingConfig.max_slots_per_day && schedulingConfig.time_slot_interval_minutes && !errors.workingHours) {
        let workingMinutes = endMinutes - startMinutes;
        if (schedulingConfig.working_hours.lunch_break?.start && schedulingConfig.working_hours.lunch_break?.end && !errors.lunchBreak) {
          const lunchStartMinutes = timeToMinutes(schedulingConfig.working_hours.lunch_break.start);
          const lunchEndMinutes = timeToMinutes(schedulingConfig.working_hours.lunch_break.end);
          workingMinutes -= (lunchEndMinutes - lunchStartMinutes);
        }

        const theoreticalMaxSlots = Math.floor(workingMinutes / schedulingConfig.time_slot_interval_minutes);
        if (schedulingConfig.max_slots_per_day > theoreticalMaxSlots) {
          errors.maxSlots = `Max slots (${schedulingConfig.max_slots_per_day}) exceeds theoretical maximum (${theoreticalMaxSlots}) based on working hours`;
        }
      }
    }

    setValidationErrors(errors);
    setIsValid(Object.keys(errors).length === 0);
  }, [schedulingConfig]);
  return (
    <div className="bg-white rounded-lg shadow overflow-hidden">
      <div className="px-6 py-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900">Voice Agent Scheduling Configuration</h2>
        <p className="text-sm text-gray-600 mt-1">
          Configure how the voice agent handles appointment scheduling. Changes are automatically synced to the voice agent system.
        </p>
      </div>

      <div className="px-6 py-4">
        <div className="space-y-6">
          {/* Basic Appointment Settings */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-md font-medium text-gray-900 mb-4">Basic Appointment Settings</h3>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
              <div>
                <label htmlFor="appointment-duration" className="block text-sm font-medium text-gray-700">
                  Default Appointment Duration (minutes)
                </label>
                <input
                  type="number"
                  id="appointment-duration"
                  min="5"
                  max="180"
                  value={schedulingConfig.appointment_duration_minutes}
                  onChange={(e) => setSchedulingConfig({
                    ...schedulingConfig,
                    appointment_duration_minutes: parseInt(e.target.value)
                  })}
                  className={`mt-1 p-2 block w-full border rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 sm:text-sm ${
                    validationErrors.appointmentDuration ? 'border-red-300' : 'border-gray-300'
                  }`}
                />
                {validationErrors.appointmentDuration && (
                  <p className="mt-1 text-xs text-red-600 flex items-center">
                    <AlertTriangle className="h-3 w-3 mr-1" />
                    {validationErrors.appointmentDuration}
                  </p>
                )}
              </div>

              <div>
                <label htmlFor="time-slot-interval" className="block text-sm font-medium text-gray-700">
                  Time Slot Interval (minutes)
                </label>
                <input
                  type="number"
                  id="time-slot-interval"
                  min="5"
                  max="120"
                  value={schedulingConfig.time_slot_interval_minutes}
                  onChange={(e) => setSchedulingConfig({
                    ...schedulingConfig,
                    time_slot_interval_minutes: parseInt(e.target.value)
                  })}
                  className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                />
              </div>

              <div>
                <label htmlFor="max-slots-per-day" className="block text-sm font-medium text-gray-700">
                  Max Slots Per Day
                </label>
                <input
                  type="number"
                  id="max-slots-per-day"
                  min="1"
                  max="200"
                  value={schedulingConfig.max_slots_per_day}
                  onChange={(e) => setSchedulingConfig({
                    ...schedulingConfig,
                    max_slots_per_day: parseInt(e.target.value)
                  })}
                  className={`mt-1 p-2 block w-full border rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 sm:text-sm ${
                    validationErrors.maxSlots ? 'border-red-300' : 'border-gray-300'
                  }`}
                />
                {validationErrors.maxSlots && (
                  <p className="mt-1 text-xs text-red-600 flex items-center">
                    <AlertTriangle className="h-3 w-3 mr-1" />
                    {validationErrors.maxSlots}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Working Hours */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-md font-medium text-gray-900 mb-4">Default Working Hours</h3>
            {validationErrors.workingHours && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-600 flex items-center">
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  {validationErrors.workingHours}
                </p>
              </div>
            )}
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
              <div>
                <label htmlFor="work-start" className="block text-sm font-medium text-gray-700">
                  Start Time
                </label>
                <input
                  type="time"
                  id="work-start"
                  value={schedulingConfig.working_hours.start}
                  onChange={(e) => setSchedulingConfig({
                    ...schedulingConfig,
                    working_hours: {
                      ...schedulingConfig.working_hours,
                      start: e.target.value
                    }
                  })}
                  className={`mt-1 p-2 block w-full border rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 sm:text-sm ${
                    validationErrors.workingHours ? 'border-red-300' : 'border-gray-300'
                  }`}
                />
              </div>

              <div>
                <label htmlFor="work-end" className="block text-sm font-medium text-gray-700">
                  End Time
                </label>
                <input
                  type="time"
                  id="work-end"
                  value={schedulingConfig.working_hours.end}
                  onChange={(e) => setSchedulingConfig({
                    ...schedulingConfig,
                    working_hours: {
                      ...schedulingConfig.working_hours,
                      end: e.target.value
                    }
                  })}
                  className={`mt-1 p-2 block w-full border rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 sm:text-sm ${
                    validationErrors.workingHours ? 'border-red-300' : 'border-gray-300'
                  }`}
                />
              </div>

              <div>
                <label htmlFor="lunch-start" className="block text-sm font-medium text-gray-700">
                  Lunch Break Start
                </label>
                <input
                  type="time"
                  id="lunch-start"
                  value={schedulingConfig.working_hours.lunch_break?.start || ''}
                  onChange={(e) => setSchedulingConfig({
                    ...schedulingConfig,
                    working_hours: {
                      ...schedulingConfig.working_hours,
                      lunch_break: {
                        ...schedulingConfig.working_hours.lunch_break,
                        start: e.target.value
                      }
                    }
                  })}
                  className={`mt-1 p-2 block w-full border rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 sm:text-sm ${
                    validationErrors.lunchBreak ? 'border-red-300' : 'border-gray-300'
                  }`}
                />
              </div>

              <div>
                <label htmlFor="lunch-end" className="block text-sm font-medium text-gray-700">
                  Lunch Break End
                </label>
                <input
                  type="time"
                  id="lunch-end"
                  value={schedulingConfig.working_hours.lunch_break?.end || ''}
                  onChange={(e) => setSchedulingConfig({
                    ...schedulingConfig,
                    working_hours: {
                      ...schedulingConfig.working_hours,
                      lunch_break: {
                        ...schedulingConfig.working_hours.lunch_break,
                        end: e.target.value
                      }
                    }
                  })}
                  className={`mt-1 p-2 block w-full border rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 sm:text-sm ${
                    validationErrors.lunchBreak ? 'border-red-300' : 'border-gray-300'
                  }`}
                />
              </div>
            </div>
            {validationErrors.lunchBreak && (
              <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-600 flex items-center">
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  {validationErrors.lunchBreak}
                </p>
              </div>
            )}
          </div>

          {/* Booking Rules */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-md font-medium text-gray-900 mb-4">Booking Rules</h3>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <label htmlFor="advance-booking-days" className="block text-sm font-medium text-gray-700">
                  Advance Booking Days
                </label>
                <input
                  type="number"
                  id="advance-booking-days"
                  min="1"
                  max="365"
                  value={schedulingConfig.advance_booking_days}
                  onChange={(e) => setSchedulingConfig({
                    ...schedulingConfig,
                    advance_booking_days: parseInt(e.target.value)
                  })}
                  className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                />
                <p className="mt-1 text-xs text-gray-500">How many days in advance patients can book appointments</p>
              </div>

              <div>
                <label htmlFor="same-day-cutoff" className="block text-sm font-medium text-gray-700">
                  Same Day Booking Cutoff (hours)
                </label>
                <input
                  type="number"
                  id="same-day-cutoff"
                  min="0"
                  max="24"
                  value={schedulingConfig.same_day_booking_cutoff_hours}
                  onChange={(e) => setSchedulingConfig({
                    ...schedulingConfig,
                    same_day_booking_cutoff_hours: parseInt(e.target.value)
                  })}
                  className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                />
                <p className="mt-1 text-xs text-gray-500">Minimum hours before appointment for same-day booking</p>
              </div>
            </div>
          </div>

          {/* Test Booking Settings */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-md font-medium text-gray-900 mb-4">Test Booking Settings</h3>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
              <div>
                <label htmlFor="test-time-slot-interval" className="block text-sm font-medium text-gray-700">
                  Test Time Slot Interval (minutes)
                </label>
                <input
                  type="number"
                  id="test-time-slot-interval"
                  min="5"
                  max="120"
                  value={schedulingConfig.test_booking?.time_slot_interval_minutes || 30}
                  onChange={(e) => setSchedulingConfig({
                    ...schedulingConfig,
                    test_booking: {
                      ...schedulingConfig.test_booking,
                      time_slot_interval_minutes: parseInt(e.target.value)
                    }
                  })}
                  className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                />
              </div>

              <div>
                <label htmlFor="test-same-day-cutoff" className="block text-sm font-medium text-gray-700">
                  Test Same Day Cutoff (hours)
                </label>
                <input
                  type="number"
                  id="test-same-day-cutoff"
                  min="0"
                  max="24"
                  value={schedulingConfig.test_booking?.same_day_booking_cutoff_hours || 1}
                  onChange={(e) => setSchedulingConfig({
                    ...schedulingConfig,
                    test_booking: {
                      ...schedulingConfig.test_booking,
                      same_day_booking_cutoff_hours: parseInt(e.target.value)
                    }
                  })}
                  className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                />
              </div>

              <div>
                <label htmlFor="test-max-slots" className="block text-sm font-medium text-gray-700">
                  Max Test Slots Per Day
                </label>
                <input
                  type="number"
                  id="test-max-slots"
                  min="1"
                  max="200"
                  value={schedulingConfig.test_booking?.max_slots_per_day || 32}
                  onChange={(e) => setSchedulingConfig({
                    ...schedulingConfig,
                    test_booking: {
                      ...schedulingConfig.test_booking,
                      max_slots_per_day: parseInt(e.target.value)
                    }
                  })}
                  className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                />
              </div>
            </div>
          </div>

          {/* Configuration Summary */}
          {schedulingConfig.working_hours?.start && schedulingConfig.working_hours?.end && schedulingConfig.time_slot_interval_minutes && !validationErrors.workingHours && (
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="text-md font-medium text-blue-900 mb-2">Configuration Summary</h3>
              <div className="text-sm text-blue-700 space-y-1">
                {(() => {
                  const startMinutes = timeToMinutes(schedulingConfig.working_hours.start);
                  const endMinutes = timeToMinutes(schedulingConfig.working_hours.end);
                  let workingMinutes = endMinutes - startMinutes;

                  if (schedulingConfig.working_hours.lunch_break?.start && schedulingConfig.working_hours.lunch_break?.end && !validationErrors.lunchBreak) {
                    const lunchStartMinutes = timeToMinutes(schedulingConfig.working_hours.lunch_break.start);
                    const lunchEndMinutes = timeToMinutes(schedulingConfig.working_hours.lunch_break.end);
                    workingMinutes -= (lunchEndMinutes - lunchStartMinutes);
                  }

                  const theoreticalMaxSlots = Math.floor(workingMinutes / schedulingConfig.time_slot_interval_minutes);
                  const workingHours = Math.floor(workingMinutes / 60);
                  const workingMins = workingMinutes % 60;

                  return (
                    <>
                      <p>• Total working time: {workingHours}h {workingMins}m ({workingMinutes} minutes)</p>
                      <p>• Theoretical maximum slots: {theoreticalMaxSlots} slots</p>
                      <p>• Current max slots setting: {schedulingConfig.max_slots_per_day || 0} slots</p>
                      {schedulingConfig.max_slots_per_day && (
                        <p>• Utilization: {Math.round((schedulingConfig.max_slots_per_day / theoreticalMaxSlots) * 100)}%</p>
                      )}
                    </>
                  );
                })()}
              </div>
            </div>
          )}

          {/* Save Button */}
          <div className="flex justify-end">
            {!isValid && (
              <div className="mr-4 flex items-center text-red-600">
                <AlertTriangle className="h-4 w-4 mr-2" />
                <span className="text-sm">Please fix validation errors before saving</span>
              </div>
            )}
            <button
              onClick={onSave}
              disabled={loading || !isValid}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Save className="h-4 w-4 mr-2" />
              {loading ? 'Saving...' : 'Save Configuration'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
