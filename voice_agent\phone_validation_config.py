"""
Phone Number Validation Configuration for Voice Agent
Provides production-ready phone number validation patterns for different regions.
"""

import re
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass

@dataclass
class PhoneValidationRule:
    """Configuration for phone number validation rules"""
    pattern: str
    min_length: int
    max_length: int
    country_code: str
    description: str
    example: str

class PhoneValidationConfig:
    """
    Production-ready phone number validation configuration.
    Supports multiple regions and can be easily extended.
    """
    
    # Default validation rules for different regions
    VALIDATION_RULES = {
        "IN": PhoneValidationRule(
            pattern=r'^\+?91[6-9]\d{9}$',
            min_length=10,
            max_length=13,  # +91 + 10 digits
            country_code="+91",
            description="Indian mobile number (starts with 6, 7, 8, or 9)",
            example="+************"
        ),
        "US": PhoneValidationRule(
            pattern=r'^\+?1[2-9]\d{9}$',
            min_length=10,
            max_length=12,  # +1 + 10 digits
            country_code="+1",
            description="US phone number",
            example="+12345678901"
        ),
        "UK": PhoneValidationRule(
            pattern=r'^\+?44[1-9]\d{8,9}$',
            min_length=10,
            max_length=13,  # +44 + up to 10 digits
            country_code="+44",
            description="UK phone number",
            example="+************"
        ),
        "INTERNATIONAL": PhoneValidationRule(
            pattern=r'^\+?[1-9]\d{9,14}$',
            min_length=10,
            max_length=15,
            country_code="",
            description="International phone number (E.164 format)",
            example="+1234567890123"
        )
    }
    
    @classmethod
    def get_validation_rule(cls, region: str = "IN") -> PhoneValidationRule:
        """
        Get validation rule for a specific region.
        
        Args:
            region: Region code (IN, US, UK, INTERNATIONAL)
            
        Returns:
            PhoneValidationRule for the specified region
        """
        return cls.VALIDATION_RULES.get(region, cls.VALIDATION_RULES["INTERNATIONAL"])
    
    @classmethod
    def validate_phone_number(cls, phone: str, region: str = "IN") -> Dict[str, Any]:
        """
        Validate phone number according to region-specific rules.
        
        Args:
            phone: Phone number to validate
            region: Region code for validation rules
            
        Returns:
            Dictionary with validation result and standardized number
        """
        if not phone:
            return {"valid": False, "error": "Phone number is required"}
        
        # Get validation rule for region
        rule = cls.get_validation_rule(region)
        
        # Clean phone number (remove spaces, dashes, parentheses)
        cleaned_phone = re.sub(r'[\s\-\(\)]', '', phone)
        
        # Basic format validation
        if not re.match(rule.pattern, cleaned_phone):
            return {
                "valid": False, 
                "error": f"Invalid phone number format for {region}. {rule.description}",
                "example": rule.example
            }
        
        # Length validation
        digits_only = re.sub(r'[^\d]', '', cleaned_phone)
        if len(digits_only) < rule.min_length or len(digits_only) > rule.max_length:
            return {
                "valid": False,
                "error": f"Phone number must be between {rule.min_length} and {rule.max_length} digits for {region}",
                "example": rule.example
            }
        
        # Standardize the phone number
        standardized = cls._standardize_phone_number(cleaned_phone, rule)
        
        return {
            "valid": True,
            "standardized": standardized,
            "region": region,
            "rule_used": rule.description
        }
    
    @classmethod
    def _standardize_phone_number(cls, phone: str, rule: PhoneValidationRule) -> str:
        """
        Standardize phone number format.
        
        Args:
            phone: Cleaned phone number
            rule: Validation rule to apply
            
        Returns:
            Standardized phone number with country code
        """
        # Remove any existing + or country code
        digits_only = re.sub(r'[^\d]', '', phone)
        
        # Add country code if not present and rule has one
        if rule.country_code and not phone.startswith(rule.country_code):
            # Remove country code digits if they're at the start
            country_digits = re.sub(r'[^\d]', '', rule.country_code)
            if digits_only.startswith(country_digits):
                digits_only = digits_only[len(country_digits):]
            
            return f"{rule.country_code}{digits_only}"
        
        # Return with + prefix if not present
        if not phone.startswith('+'):
            return f"+{digits_only}"
        
        return phone
    
    @classmethod
    def get_supported_regions(cls) -> List[str]:
        """Get list of supported regions for phone validation."""
        return list(cls.VALIDATION_RULES.keys())
    
    @classmethod
    def get_region_info(cls, region: str) -> Optional[Dict[str, str]]:
        """
        Get information about a specific region's validation rules.
        
        Args:
            region: Region code
            
        Returns:
            Dictionary with region information or None if not found
        """
        rule = cls.VALIDATION_RULES.get(region)
        if not rule:
            return None
        
        return {
            "region": region,
            "description": rule.description,
            "example": rule.example,
            "country_code": rule.country_code,
            "min_length": str(rule.min_length),
            "max_length": str(rule.max_length)
        }

# Convenience function for backward compatibility
def validate_phone_number(phone: str, region: str = "IN") -> Dict[str, Any]:
    """
    Validate phone number using production-ready validation.
    
    Args:
        phone: Phone number to validate
        region: Region code (defaults to India)
        
    Returns:
        Validation result dictionary
    """
    return PhoneValidationConfig.validate_phone_number(phone, region)

# Export for easy importing
__all__ = ['PhoneValidationConfig', 'PhoneValidationRule', 'validate_phone_number']
