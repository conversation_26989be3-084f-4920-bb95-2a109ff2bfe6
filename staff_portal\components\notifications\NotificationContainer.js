import React from 'react';
import { ErrorToast } from './ErrorToast';

/**
 * Container component for managing multiple notifications
 * Renders notifications in a stack at the bottom-right of the screen
 */
export function NotificationContainer({ notifications, onRemove }) {
  if (!notifications || notifications.length === 0) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 space-y-2">
      {notifications.map((notification, index) => (
        <div
          key={notification.id}
          style={{
            transform: `translateY(-${index * 8}px)`,
            zIndex: 50 - index,
          }}
        >
          <ErrorToast
            id={notification.id}
            type={notification.type}
            title={notification.title}
            message={notification.message}
            duration={notification.duration}
            dismissible={notification.dismissible}
            onClose={onRemove}
            onClick={notification.onClick}
          />
        </div>
      ))}
    </div>
  );
}

export default NotificationContainer;
