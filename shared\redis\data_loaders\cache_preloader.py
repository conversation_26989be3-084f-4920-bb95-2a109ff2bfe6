"""
Enhanced cache preloader for shared Redis implementation.
Orchestrates data loading from Firebase and configuration files with improved caching integration.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional
from .firebase_loader import FirebaseDataLoader
from .config_loader import ConfigDataLoader
from .query_generator import QueryGenerator

logger = logging.getLogger(__name__)


class CachePreloader:
    """
    Production-ready cache preloader that orchestrates data loading.
    Enhanced version with better integration with shared Redis implementation.
    """

    def __init__(self, redis_adapter=None):
        """
        Initialize cache preloader with data loaders.
        
        Args:
            redis_adapter: Optional Redis adapter instance for caching
        """
        self.firebase_loader = FirebaseDataLoader()
        self.config_loader = ConfigDataLoader()
        self.query_generator = QueryGenerator(self.config_loader)
        self.redis_adapter = redis_adapter
        
        # Statistics tracking
        self.stats = {
            "hospitals_processed": 0,
            "queries_generated": 0,
            "queries_cached": 0,
            "errors": 0
        }

    def set_redis_adapter(self, redis_adapter):
        """Set Redis adapter for caching operations."""
        self.redis_adapter = redis_adapter
        logger.info("Redis adapter set for cache preloader")

    async def preload_hospital_data_async(self, hospital_id: str, 
                                        hospital_data: Dict[str, Any] = None) -> bool:
        """
        Async preload hospital data into semantic cache with enhanced error handling.

        Args:
            hospital_id: Hospital identifier
            hospital_data: Optional pre-loaded hospital data

        Returns:
            bool: Success or failure
        """
        try:
            logger.info(f"Starting cache preload for hospital {hospital_id}")
            
            # Use provided data or load from sources
            if hospital_data:
                logger.info(f"Using provided hospital data for {hospital_id}")
                data = hospital_data
            else:
                data = await self._load_hospital_data(hospital_id)

            if not data or (not data.get('doctors') and not data.get('tests')):
                logger.warning(f"No hospital data found for {hospital_id}")
                return False

            # Generate and cache queries
            success = await self._cache_generated_queries(hospital_id, data)
            
            if success:
                self.stats["hospitals_processed"] += 1
                logger.info(f"Successfully preloaded cache for hospital {hospital_id}")
            else:
                self.stats["errors"] += 1
                logger.error(f"Failed to preload cache for hospital {hospital_id}")
            
            return success

        except Exception as e:
            self.stats["errors"] += 1
            logger.error(f"Error in async preload for hospital {hospital_id}: {e}")
            return False

    def preload_hospital_data_sync(self, hospital_id: str, 
                                 hospital_data: Dict[str, Any] = None) -> bool:
        """
        Synchronous preload hospital data into semantic cache.

        Args:
            hospital_id: Hospital identifier
            hospital_data: Optional pre-loaded hospital data

        Returns:
            bool: Success or failure
        """
        try:
            # Handle event loop scenarios
            try:
                loop = asyncio.get_running_loop()
                # Use run_coroutine_threadsafe for running async code from sync context within existing loop
                future = asyncio.run_coroutine_threadsafe(
                    self.preload_hospital_data_async(hospital_id, hospital_data), loop
                )
                return future.result()
            except RuntimeError:
                # Fallback for pure sync contexts (no running loop)
                return asyncio.run(self.preload_hospital_data_async(hospital_id, hospital_data))
        except Exception as e:
            self.stats["errors"] += 1
            logger.error(f"Error in sync preload for hospital {hospital_id}: {e}")
            return False

    async def _load_hospital_data(self, hospital_id: str) -> Dict[str, Any]:
        """
        Load hospital data with Firebase priority and config fallback.

        Args:
            hospital_id: Hospital identifier

        Returns:
            Dict containing hospital data
        """
        try:
            # Try Firebase first
            firebase_data = await self.firebase_loader.load_hospital_data(hospital_id)
            
            if firebase_data and (firebase_data.get('doctors') or firebase_data.get('tests')):
                logger.info(f"Loaded hospital data from Firebase for {hospital_id}")
                return firebase_data
            
            # Fall back to configuration templates
            logger.info(f"Firebase data not available, trying config templates for {hospital_id}")
            config_data = self._load_config_data(hospital_id)
            
            if config_data:
                logger.info(f"Loaded hospital data from config templates for {hospital_id}")
                return config_data
            
            logger.warning(f"No data source available for hospital {hospital_id}")
            return {}

        except Exception as e:
            logger.error(f"Error loading hospital data for {hospital_id}: {e}")
            return {}

    def _load_config_data(self, hospital_id: str) -> Dict[str, Any]:
        """Load hospital data from configuration templates."""
        try:
            hospital_template = self.config_loader.load_hospital_template(hospital_id)
            
            if not hospital_template:
                return {}
            
            # Convert template format to expected data format
            config_data = {
                "hospital_id": hospital_id,
                "doctors": hospital_template.get('doctors', []),
                "tests": hospital_template.get('tests', []),
                "hospital_info": hospital_template.get('hospital_info', {}),
                "languages": hospital_template.get('languages', ["en", "hi", "bn"]),
                "metadata": {
                    "source": "config_template",
                    "template_version": hospital_template.get('metadata', {}).get('template_version', '1.0')
                }
            }
            
            return config_data

        except Exception as e:
            logger.error(f"Error loading config data for {hospital_id}: {e}")
            return {}

    async def _cache_generated_queries(self, hospital_id: str, data: Dict[str, Any]) -> bool:
        """
        Generate and cache semantic queries for hospital data.

        Args:
            hospital_id: Hospital identifier
            data: Hospital data dictionary

        Returns:
            bool: Success or failure
        """
        try:
            if not self.redis_adapter:
                logger.warning("No Redis adapter available for caching")
                return False

            languages = data.get('languages', ["en", "hi", "bn"])
            doctors = data.get('doctors', [])
            tests = data.get('tests', [])
            
            total_queries = 0
            cached_queries = 0

            # Generate and cache doctor queries
            if doctors:
                doctor_queries = self.query_generator.generate_doctor_queries(doctors, languages)
                total_queries += len(doctor_queries)
                
                for query, response_data in doctor_queries:
                    try:
                        success = await self.redis_adapter.cache_semantic_response_async(
                            query, response_data, hospital_id, "doctor_info"
                        )
                        if success:
                            cached_queries += 1
                    except Exception as e:
                        logger.debug(f"Error caching doctor query '{query}': {e}")
                        continue

            # Generate and cache test queries
            if tests:
                test_queries = self.query_generator.generate_test_queries(tests, languages)
                total_queries += len(test_queries)
                
                for query, response_data in test_queries:
                    try:
                        success = await self.redis_adapter.cache_semantic_response_async(
                            query, response_data, hospital_id, "test_info"
                        )
                        if success:
                            cached_queries += 1
                    except Exception as e:
                        logger.debug(f"Error caching test query '{query}': {e}")
                        continue

            # Generate and cache common queries
            common_queries = self.query_generator.generate_common_queries(hospital_id, languages)
            total_queries += len(common_queries)
            
            for query, response_data in common_queries:
                try:
                    success = await self.redis_adapter.cache_semantic_response_async(
                        query, response_data, hospital_id, "common"
                    )
                    if success:
                        cached_queries += 1
                except Exception as e:
                    logger.debug(f"Error caching common query '{query}': {e}")
                    continue

            # Update statistics
            self.stats["queries_generated"] += total_queries
            self.stats["queries_cached"] += cached_queries

            success_rate = (cached_queries / total_queries * 100) if total_queries > 0 else 0
            logger.info(f"Cached {cached_queries}/{total_queries} queries for {hospital_id} "
                       f"(success rate: {success_rate:.1f}%)")

            return cached_queries > 0

        except Exception as e:
            logger.error(f"Error caching generated queries for {hospital_id}: {e}")
            return False

    async def preload_all_hospitals(self, hospital_ids: List[str] = None) -> Dict[str, bool]:
        """
        Preload data for multiple hospitals.

        Args:
            hospital_ids: List of hospital IDs to preload (optional)

        Returns:
            Dict mapping hospital_id to success status
        """
        if hospital_ids is None:
            # Get hospital IDs from config and Firebase
            config_hospitals = self.config_loader.get_supported_hospitals()
            firebase_hospitals = self.firebase_loader.get_supported_hospitals()
            hospital_ids = list(set(config_hospitals + firebase_hospitals))

        if not hospital_ids:
            logger.warning("No hospitals found to preload")
            return {}

        logger.info(f"Starting preload for {len(hospital_ids)} hospitals")
        results = {}

        # Process hospitals concurrently with limited concurrency
        semaphore = asyncio.Semaphore(5)  # Limit to 5 concurrent operations
        
        async def preload_single(hospital_id: str) -> bool:
            async with semaphore:
                return await self.preload_hospital_data_async(hospital_id)

        tasks = [preload_single(hospital_id) for hospital_id in hospital_ids]
        completed_results = await asyncio.gather(*tasks, return_exceptions=True)

        for hospital_id, result in zip(hospital_ids, completed_results):
            if isinstance(result, Exception):
                logger.error(f"Exception preloading {hospital_id}: {result}")
                results[hospital_id] = False
            else:
                results[hospital_id] = result

        successful = sum(1 for success in results.values() if success)
        logger.info(f"Preload completed: {successful}/{len(hospital_ids)} hospitals successful")

        return results

    def get_statistics(self) -> Dict[str, Any]:
        """
        Get preloader statistics.

        Returns:
            Dictionary with statistics
        """
        query_stats = self.query_generator.get_query_statistics()
        
        return {
            "preloader_stats": self.stats.copy(),
            "query_generator_stats": query_stats,
            "config_stats": {
                "supported_languages": self.config_loader.get_supported_languages(),
                "supported_hospitals": self.config_loader.get_supported_hospitals()
            }
        }

    def reset_statistics(self):
        """Reset all statistics counters."""
        self.stats = {
            "hospitals_processed": 0,
            "queries_generated": 0,
            "queries_cached": 0,
            "errors": 0
        }
        logger.info("Statistics reset")

    async def validate_hospital_data(self, hospital_id: str) -> Dict[str, Any]:
        """
        Validate hospital data from all sources.

        Args:
            hospital_id: Hospital identifier

        Returns:
            Validation results
        """
        validation_results = {
            "hospital_id": hospital_id,
            "firebase_available": False,
            "config_available": False,
            "data_quality": {},
            "recommendations": []
        }

        try:
            # Check Firebase data
            firebase_data = await self.firebase_loader.load_hospital_data(hospital_id)
            if firebase_data and (firebase_data.get('doctors') or firebase_data.get('tests')):
                validation_results["firebase_available"] = True
                validation_results["data_quality"]["firebase"] = {
                    "doctors_count": len(firebase_data.get('doctors', [])),
                    "tests_count": len(firebase_data.get('tests', [])),
                    "has_hospital_info": bool(firebase_data.get('hospital_info'))
                }

            # Check config data
            config_data = self._load_config_data(hospital_id)
            if config_data and (config_data.get('doctors') or config_data.get('tests')):
                validation_results["config_available"] = True
                validation_results["data_quality"]["config"] = {
                    "doctors_count": len(config_data.get('doctors', [])),
                    "tests_count": len(config_data.get('tests', [])),
                    "has_hospital_info": bool(config_data.get('hospital_info'))
                }

            # Generate recommendations
            if not validation_results["firebase_available"] and not validation_results["config_available"]:
                validation_results["recommendations"].append("No data sources available - create config template")
            elif not validation_results["firebase_available"]:
                validation_results["recommendations"].append("Firebase data not available - consider adding Firebase data")
            elif not validation_results["config_available"]:
                validation_results["recommendations"].append("Config template not available - consider creating fallback template")

        except Exception as e:
            logger.error(f"Error validating hospital data for {hospital_id}: {e}")
            validation_results["error"] = str(e)

        return validation_results
