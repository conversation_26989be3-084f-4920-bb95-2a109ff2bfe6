import { db as firestoreDb } from './firebase';
import { getHospitalDbPool } from './pg_utils';
import Redis from 'ioredis';
import { logger } from './logger';

// Initialize Redis client
const redisClient = new Redis(process.env.REDIS_URL || 'redis://localhost:6379');

/**
 * Data Synchronization service for keeping Firestore, PostgreSQL, and Redis in sync
 */
class DataSyncService {
  constructor() {
    this.syncInProgress = false;
    this.lastSyncTime = null;
    this.syncQueue = [];
    this.syncInterval = parseInt(process.env.DATA_SYNC_INTERVAL || '3600000', 10); // Default: 1 hour (3,600,000 ms)
    this.syncJitterMs = 10000; // 10 seconds of jitter to prevent sync storms
  }

  /**
   * Start the sync service
   */
  start() {
    // Schedule periodic sync
    this.scheduleSyncJob();
    
    // Listen for Firestore changes
    this.setupFirestoreListeners();
    
    logger.info('Data sync service started');
  }

  /**
   * Schedule the next sync job
   */
  scheduleSyncJob() {
    const jitter = Math.floor(Math.random() * this.syncJitterMs);
    const nextSyncTime = this.syncInterval + jitter;
    
    setTimeout(() => {
      this.fullSync()
        .then(() => {
          this.scheduleSyncJob();
        })
        .catch(error => {
          logger.error('Full sync error:', error);
          this.scheduleSyncJob();
        });
    }, nextSyncTime);
    
    logger.debug(`Next sync scheduled in ${nextSyncTime / 1000} seconds`);
  }

  /**
   * Set up Firestore listeners for real-time updates
   */
  setupFirestoreListeners() {
    // Listen for changes to hospital settings
    firestoreDb.collection('hospitals').onSnapshot(snapshot => {
      snapshot.docChanges().forEach(change => {
        const hospitalDocId = change.doc.id;
        const data = change.doc.data();
        
        if (change.type === 'added' || change.type === 'modified') {
          this.syncHospitalToPostgres(hospitalDocId, data);
          this.invalidateCache(`hospital:${hospitalDocId}`);
        } else if (change.type === 'removed') {
          // We don't delete hospitals from PostgreSQL
          this.invalidateCache(`hospital:${hospitalDocId}`);
        }
      });
    }, error => {
      logger.error('Firestore hospitals listener error:', error);
    });
    
    // Listen for changes to staff
    /* Disable incorrect top-level listener for staff
    firestoreDb.collection('staff').onSnapshot(snapshot => {
      snapshot.docChanges().forEach(change => {
        const staffId = change.doc.id;
        const data = change.doc.data();
        
        if (change.type === 'added' || change.type === 'modified') {
          this.syncStaffToPostgres(staffId, data);
          this.invalidateCache(`staff:${staffId}`);
        } else if (change.type === 'removed') {
          // We don't delete staff from PostgreSQL
          this.invalidateCache(`staff:${staffId}`);
        }
      });
    }, error => {
      logger.error('Firestore staff listener error:', error);
    });
    */
    
    // Listen for changes to appointments
    /* Disable incorrect top-level listener for appointments
    firestoreDb.collection('appointments').onSnapshot(snapshot => {
      snapshot.docChanges().forEach(change => {
        const appointmentId = change.doc.id;
        const data = change.doc.data();
        
        if (change.type === 'added' || change.type === 'modified') {
          this.syncAppointmentToPostgres(appointmentId, data);
          this.invalidateCache(`appointment:${appointmentId}`);
        } else if (change.type === 'removed') {
          // We don't delete appointments from PostgreSQL
          this.invalidateCache(`appointment:${appointmentId}`);
        }
      });
    }, error => {
      logger.error('Firestore appointments listener error:', error);
    });
    */
  }

  /**
   * Perform a full sync between Firestore and PostgreSQL
   */
  async fullSync() {
    if (this.syncInProgress) {
      logger.info('[SYNC_SERVICE] Full sync already in progress, skipping.');
      return;
    }
    
    this.syncInProgress = true;
    this.lastSyncTime = new Date();
    logger.info('[SYNC_SERVICE] Starting full data sync...');

    try {
      // 1. Get all hospital configurations from the 'hospitals' collection
      const hospitalsSnapshot = await firestoreDb.collection('hospitals').get();
      if (hospitalsSnapshot.empty) {
        logger.warn('[SYNC_SERVICE] No hospitals found in Firestore \'hospitals\' collection to sync.');
        this.syncInProgress = false;
        return;
      }

      // Process each hospital sequentially to avoid overwhelming resources
      for (const hospitalDoc of hospitalsSnapshot.docs) {
        const hospitalDocId = hospitalDoc.id; // e.g., 'hospital_123_data'
        const hospitalData = hospitalDoc.data();
        const numericHospitalId = hospitalDocId.replace('hospital_', '').replace('_data', '');

        if (!numericHospitalId) {
          logger.error(`[SYNC_SERVICE] Could not extract numeric hospital ID from ${hospitalDocId}. Skipping this hospital.`);
          continue;
        }

        logger.info(`[SYNC_SERVICE] Starting sync for hospital ${numericHospitalId} (doc: ${hospitalDocId})`);

        // A. Sync hospital's own metadata (if not already handled by listener)
        // This ensures the hospital record itself is in its PG DB.
        // The listener on 'hospitals' collection should already handle this, but good for belt-and-suspenders during full sync.
        await this.syncHospitalToPostgres(hospitalDocId, hospitalData);

        // B. Sync nested collections for this hospital
        const collectionsToSync = [
          { name: 'doctors', path: `hospital_${numericHospitalId}_data/doctors/doctors`, syncItemMethod: this.syncDoctorToPostgres.bind(this) },
          { name: 'staff', path: `hospital_${numericHospitalId}_data/staff/staff`, syncItemMethod: this.syncStaffToPostgres.bind(this) }, // Assuming 'staff' doc then 'staff' subcollection
          { name: 'appointments', path: `hospital_${numericHospitalId}_data/appointments/appointments`, syncItemMethod: this.syncAppointmentToPostgres.bind(this) }, // Assuming 'appointments' doc then 'appointments' subcollection
          { name: 'test_bookings', path: `hospital_${numericHospitalId}_data/test_info/test_bookings`, syncItemMethod: this.syncTestBookingToPostgres.bind(this) } // Assuming 'test_info' doc then 'test_bookings' subcollection
        ];

        for (const coll of collectionsToSync) {
          try {
            logger.info(`[SYNC_SERVICE] Syncing ${coll.name} for hospital ${numericHospitalId} from path ${coll.path}`);
            const itemsSnapshot = await firestoreDb.collection(coll.path).get();
            if (itemsSnapshot.empty) {
              logger.info(`[SYNC_SERVICE] No items found in ${coll.path} for hospital ${numericHospitalId}.`);
              continue;
            }
            for (const itemDoc of itemsSnapshot.docs) {
              // Pass numericHospitalId so the item sync method knows which DB pool to use
              await coll.syncItemMethod(numericHospitalId, itemDoc.id, itemDoc.data());
            }
            logger.info(`[SYNC_SERVICE] Finished syncing ${itemsSnapshot.size} ${coll.name} items for hospital ${numericHospitalId}.`);
          } catch (error) {
            logger.error(`[SYNC_SERVICE] Error syncing collection ${coll.name} (path: ${coll.path}) for hospital ${numericHospitalId}:`, error);
            // Continue to next collection even if one fails for a hospital
          }
        }
        logger.info(`[SYNC_SERVICE] Finished sync for hospital ${numericHospitalId}`);
      }

      logger.info('[SYNC_SERVICE] Full data sync completed.');

    } catch (error) {
      logger.error('[SYNC_SERVICE] Critical error during full sync process:', error);
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * Sync a Firestore collection to a PostgreSQL table
   * @param {string} collection - Firestore collection name
   * @param {string} table - PostgreSQL table name
   */
  async syncCollectionToPostgres(collection, table) {
    logger.info(`Syncing collection ${collection} to table ${table}`);
    
    try {
      // Get all documents from Firestore
      const snapshot = await firestoreDb.collection(collection).get();
      
      // Process in batches to avoid excessive memory usage
      const batchSize = 50;
      const batches = [];
      
      for (let i = 0; i < snapshot.docs.length; i += batchSize) {
        const batch = snapshot.docs.slice(i, i + batchSize);
        batches.push(batch);
      }
      
      // Process each batch
      for (const batch of batches) {
        await Promise.all(batch.map(doc => {
          const id = doc.id;
          const data = doc.data();
          
          if (table === 'hospitals') {
            return this.syncHospitalToPostgres(id, data);
          } else if (table === 'staff') {
            return this.syncStaffToPostgres(id, data);
          } else if (table === 'appointments') {
            return this.syncAppointmentToPostgres(id, data);
          }
        }));
      }
      
      logger.info(`Synced ${snapshot.docs.length} documents from ${collection} to ${table}`);
    } catch (error) {
      logger.error(`Error syncing ${collection} to ${table}:`, error);
      throw error;
    }
  }

  /**
   * Sync a hospital document to PostgreSQL
   * @param {string} hospitalDocId - Hospital document ID
   * @param {object} data - Hospital data
   */
  async syncHospitalToPostgres(hospitalDocId, data) {
    // Extract numeric hospital ID from document ID like 'hospital_123_data'
    const numericHospitalId = hospitalDocId.replace('hospital_', '').replace('_data', '');
    
    if (!numericHospitalId) {
      logger.error(`[SYNC_SERVICE] Could not extract numeric hospital ID from ${hospitalDocId}`);
      return false;
    }

    logger.info(`[SYNC_SERVICE] Syncing hospital ${numericHospitalId} (doc: ${hospitalDocId}) to PostgreSQL.`);

    try {
      const pool = await getHospitalDbPool(numericHospitalId); // Use numeric ID for pool
      const client = await pool.connect();

      try {
        const pgData = this.prepareHospitalData(numericHospitalId, data); // Pass numeric ID

        // Upsert logic for hospital data
        const upsertQuery = `
          INSERT INTO hospitals (
            id,
            name,
            address,
            phone,
            email,
            db_connection_string,
            settings,
            created_at,
            updated_at
          ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
          )
          ON CONFLICT (id) DO UPDATE SET
            name = EXCLUDED.name,
            address = EXCLUDED.address,
            phone = EXCLUDED.phone,
            email = EXCLUDED.email,
            db_connection_string = EXCLUDED.db_connection_string,
            settings = EXCLUDED.settings,
            updated_at = CURRENT_TIMESTAMP;
        `;

        await client.query(upsertQuery, [
          pgData.id, // This should be the numericHospitalId
          pgData.name,
          pgData.address,
          pgData.phone,
          pgData.email,
          pgData.db_connection_string,
          pgData.settings
        ]);
        
        logger.debug(`Synced hospital ${numericHospitalId} to PostgreSQL`);
      } finally {
        client.release();
      }
      
      // Invalidate cache
      this.invalidateCache(`hospital:${hospitalDocId}`);
      
      return true;
    } catch (error) {
      logger.error(`Error syncing hospital ${numericHospitalId} to PostgreSQL:`, error);
      return false;
    }
  }

  /**
   * Sync a staff document to PostgreSQL
   * @param {string} staffId - Staff ID
   * @param {object} data - Staff data
   */
  async syncStaffToPostgres(staffId, data) {
    // This method might be called by fullSync (now with numericHospitalId) or by manual sync.
    // If called by fullSync: staffId is the Firestore doc ID, data is its content.
    // The 'hospital_id' field within 'data' should be used to get the PG pool.
    let numericHospitalIdForPool = data.hospital_id;
    
    // If the first argument is the numericHospitalId (from new fullSync structure), use that.
    if (typeof arguments[0] === 'string' && arguments.length === 3) {
      numericHospitalIdForPool = arguments[0];
      staffId = arguments[1];
      data = arguments[2];
      logger.info(`[SYNC_SERVICE] syncStaffToPostgres called from fullSync for hospital ${numericHospitalIdForPool}, staff ${staffId}`);
    } else {
      logger.info(`[SYNC_SERVICE] syncStaffToPostgres called (legacy or manual) for staff ${staffId}`);
    }

    if (!numericHospitalIdForPool) {
      logger.error(`[SYNC_SERVICE] syncStaffToPostgres: hospital_id not found in staff data for ${staffId} and not passed as argument. Cannot determine DB pool.`);
      return false;
    }

    logger.info(`[SYNC_SERVICE] Syncing staff ${staffId} to PostgreSQL for hospital ${numericHospitalIdForPool}.`);

    try {
      const pool = await getHospitalDbPool(numericHospitalIdForPool);
      const client = await pool.connect();

      try {
        const pgData = this.prepareStaffData(staffId, data);
        if (!pgData.hospital_id) {
          logger.error(`Cannot sync staff ${staffId}: hospital_id is missing from prepared data.`);
          return false;
        }
        const upsertQuery = `
          INSERT INTO staff (
            id, name, email, role, hospital_id, settings, created_at, updated_at
          ) VALUES (
            $1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
          )
          ON CONFLICT (id) DO UPDATE SET
            name = EXCLUDED.name,
            email = EXCLUDED.email,
            role = EXCLUDED.role,
            hospital_id = EXCLUDED.hospital_id,
            settings = EXCLUDED.settings,
            updated_at = CURRENT_TIMESTAMP;
        `;
        
        await client.query(upsertQuery, [
          pgData.id,
          pgData.name,
          pgData.email,
          pgData.role,
          pgData.hospital_id,
          pgData.settings
        ]);
        
        logger.debug(`Synced staff ${staffId} to PostgreSQL`);
      } finally {
        client.release();
      }
      
      // Invalidate cache
      this.invalidateCache(`staff:${staffId}`);
      this.invalidateCache(`hospital:staff:${pgData.hospital_id}`);
      
      return true;
    } catch (error) {
      logger.error(`Error syncing staff ${staffId} to PostgreSQL:`, error);
      return false;
    }
  }

  /**
   * Sync an appointment document to PostgreSQL
   * @param {string} appointmentId - Appointment ID
   * @param {object} data - Appointment data
   */
  async syncAppointmentToPostgres(appointmentId, data) {
    // Similar adaptation for hospital_id as in syncStaffToPostgres
    let numericHospitalIdForPool = data.hospital_id;

    if (typeof arguments[0] === 'string' && arguments.length === 3) {
      numericHospitalIdForPool = arguments[0];
      appointmentId = arguments[1];
      data = arguments[2];
      logger.info(`[SYNC_SERVICE] syncAppointmentToPostgres called from fullSync for hospital ${numericHospitalIdForPool}, appt ${appointmentId}`);
    } else {
      logger.info(`[SYNC_SERVICE] syncAppointmentToPostgres called (legacy or manual) for appt ${appointmentId}`);
    }

    if (!numericHospitalIdForPool) {
      logger.error(`[SYNC_SERVICE] syncAppointmentToPostgres: hospital_id not found in appointment data for ${appointmentId} and not passed as argument. Cannot determine DB pool.`);
      return false;
    }
    
    logger.info(`[SYNC_SERVICE] Syncing appointment ${appointmentId} to PostgreSQL for hospital ${numericHospitalIdForPool}.`);

    try {
      const pool = await getHospitalDbPool(numericHospitalIdForPool);
      const client = await pool.connect();

      try {
        const pgData = this.prepareAppointmentData(appointmentId, data);
        if (!pgData.hospital_id) {
          logger.error(`Cannot sync appointment ${appointmentId}: hospital_id is missing from prepared data.`);
          return false;
        }
        const upsertQuery = `
          INSERT INTO appointments (
            id, patient_id, doctor_id, hospital_id, start_time, end_time, status, notes, created_at, updated_at
          ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, $8, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
          )
          ON CONFLICT (id) DO UPDATE SET
            patient_id = EXCLUDED.patient_id,
            doctor_id = EXCLUDED.doctor_id,
            hospital_id = EXCLUDED.hospital_id,
            start_time = EXCLUDED.start_time,
            end_time = EXCLUDED.end_time,
            status = EXCLUDED.status,
            notes = EXCLUDED.notes,
            updated_at = CURRENT_TIMESTAMP;
        `;
        
        await client.query(upsertQuery, [
          pgData.id,
          pgData.patient_id,
          pgData.doctor_id,
          pgData.hospital_id,
          pgData.start_time,
          pgData.end_time,
          pgData.status,
          pgData.notes
        ]);
        
        logger.debug(`Synced appointment ${appointmentId} to PostgreSQL`);
      } finally {
        client.release();
      }
      
      // Invalidate cache
      this.invalidateCache(`appointment:${appointmentId}`);
      this.invalidateCache(`hospital:appointments:${pgData.hospital_id}`);
      this.invalidateCache(`doctor:appointments:${pgData.doctor_id}`);
      this.invalidateCache(`patient:appointments:${pgData.patient_id}`);
      
      return true;
    } catch (error) {
      logger.error(`Error syncing appointment ${appointmentId} to PostgreSQL:`, error);
      return false;
    }
  }

  /**
   * Sync a doctor document to PostgreSQL
   * @param {string} numericHospitalId - Hospital ID
   * @param {string} doctorId - Doctor ID
   * @param {object} data - Doctor data
   */
  async syncDoctorToPostgres(numericHospitalId, doctorId, data) {
    logger.info(`[SYNC_SERVICE] Syncing doctor ${doctorId} to PostgreSQL for hospital ${numericHospitalId}.`);
    try {
      const pool = await getHospitalDbPool(numericHospitalId);
      const client = await pool.connect();
      try {
        const pgData = this.prepareDoctorData(doctorId, data); // Ensure hospital_id is in pgData if needed by table
        // Add hospital_id to pgData if it's part of the doctors table schema in PG
        pgData.hospital_id = numericHospitalId; 

        const upsertQuery = `
          INSERT INTO doctors (id, name, email, specialty, availability, phone, hospital_id, created_at, updated_at)
          VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
          ON CONFLICT (id) DO UPDATE SET
            name = EXCLUDED.name,
            email = EXCLUDED.email,
            specialty = EXCLUDED.specialty,
            availability = EXCLUDED.availability,
            phone = EXCLUDED.phone,
            hospital_id = EXCLUDED.hospital_id,
            updated_at = CURRENT_TIMESTAMP;
        `;
        await client.query(upsertQuery, [
          pgData.id,
          pgData.name,
          pgData.email,
          pgData.specialty,
          pgData.availability, // Ensure this is JSON or appropriate type
          pgData.phone,
          pgData.hospital_id
        ]);
        logger.debug(`[SYNC_SERVICE] Synced doctor ${doctorId} for hospital ${numericHospitalId}`);
      } finally {
        client.release();
      }
      this.invalidateCache(`doctor:${doctorId}_hospital:${numericHospitalId}`);
      return true;
    } catch (error) {
      logger.error(`[SYNC_SERVICE] Error syncing doctor ${doctorId} for hospital ${numericHospitalId}:`, error);
      return false;
    }
  }

  /**
   * Sync a test booking document to PostgreSQL
   * @param {string} numericHospitalId - Hospital ID
   * @param {string} testBookingId - Test booking ID
   * @param {object} data - Test booking data
   */
  async syncTestBookingToPostgres(numericHospitalId, testBookingId, data) {
    logger.info(`[SYNC_SERVICE] Syncing test_booking ${testBookingId} to PostgreSQL for hospital ${numericHospitalId}.`);
    try {
      const pool = await getHospitalDbPool(numericHospitalId);
      const client = await pool.connect();
      try {
        const pgData = this.prepareTestBookingData(testBookingId, data);
        // Add hospital_id to pgData if it's part of the test_bookings table schema in PG
        pgData.hospital_id = numericHospitalId; 

        const upsertQuery = `
          INSERT INTO test_bookings (id, patient_id, test_type, booking_time, status, notes, hospital_id, created_at, updated_at)
          VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
          ON CONFLICT (id) DO UPDATE SET
            patient_id = EXCLUDED.patient_id,
            test_type = EXCLUDED.test_type,
            booking_time = EXCLUDED.booking_time,
            status = EXCLUDED.status,
            notes = EXCLUDED.notes,
            hospital_id = EXCLUDED.hospital_id,
            updated_at = CURRENT_TIMESTAMP;
        `;
        await client.query(upsertQuery, [
          pgData.id,
          pgData.patient_id,
          pgData.test_type,
          pgData.booking_time,
          pgData.status,
          pgData.notes,
          pgData.hospital_id
        ]);
        logger.debug(`[SYNC_SERVICE] Synced test_booking ${testBookingId} for hospital ${numericHospitalId}`);
      } finally {
        client.release();
      }
      this.invalidateCache(`test_booking:${testBookingId}_hospital:${numericHospitalId}`);
      return true;
    } catch (error) {
      logger.error(`[SYNC_SERVICE] Error syncing test_booking ${testBookingId} for hospital ${numericHospitalId}:`, error);
      return false;
    }
  }

  /**
   * Prepare hospital data for PostgreSQL
   * @param {string} hospitalId - Hospital ID
   * @param {object} data - Firestore hospital data
   * @returns {object} PostgreSQL-compatible data
   */
  prepareHospitalData(hospitalId, data) {
    return {
      id: hospitalId, // Expects numeric ID here
      name: data.name || '',
      address: data.address || '',
      phone: data.phone || '',
      email: data.email || '',
      // Include db_connection_string from the Firestore document if it exists and is relevant for this table
      db_connection_string: data.db_connection_string || null, 
      settings: JSON.stringify(data.settings || {})
    };
  }

  /**
   * Prepare staff data for PostgreSQL
   * @param {string} staffId - Staff ID
   * @param {object} data - Firestore staff data
   * @returns {object} PostgreSQL-compatible data
   */
  prepareStaffData(staffId, data) {
    return {
      id: staffId,
      name: data.name || '',
      email: data.email || '',
      role: data.role || 'staff',
      hospital_id: data.hospital_id || '',
      settings: JSON.stringify(data.settings || {})
    };
  }

  /**
   * Prepare appointment data for PostgreSQL
   * @param {string} appointmentId - Appointment ID
   * @param {object} data - Firestore appointment data
   * @returns {object} PostgreSQL-compatible data
   */
  prepareAppointmentData(appointmentId, data) {
    return {
      id: appointmentId,
      patient_id: data.patient_id || '',
      doctor_id: data.doctor_id || '',
      hospital_id: data.hospital_id || '',
      start_time: data.start_time ? new Date(data.start_time) : null,
      end_time: data.end_time ? new Date(data.end_time) : null,
      status: data.status || 'scheduled',
      notes: data.notes || ''
    };
  }

  /**
   * Prepare doctor data for PostgreSQL
   * @param {string} doctorId - Doctor ID
   * @param {object} data - Firestore doctor data
   * @returns {object} PostgreSQL-compatible data
   */
  prepareDoctorData(doctorId, data) {
    return {
      id: doctorId,
      name: data.name || '',
      email: data.email || '',
      specialty: data.specialty || '',
      availability: data.availability ? JSON.stringify(data.availability) : JSON.stringify({}), // Ensure availability is stringified if it's an object/array
      phone: data.phone || ''
      // hospital_id will be added by the syncDoctorToPostgres method
    };
  }

  /**
   * Prepare test booking data for PostgreSQL
   * @param {string} testBookingId - Test booking ID
   * @param {object} data - Firestore test booking data
   * @returns {object} PostgreSQL-compatible data
   */
  prepareTestBookingData(testBookingId, data) {
    return {
      id: testBookingId,
      patient_id: data.patient_id || '',
      test_type: data.test_type || '',
      booking_time: data.booking_time ? new Date(data.booking_time) : null, // Assuming booking_time is a timestamp
      status: data.status || 'scheduled',
      notes: data.notes || ''
      // hospital_id will be added by the syncTestBookingToPostgres method
    };
  }

  /**
   * Invalidate a Redis cache key
   * @param {string} keyPattern - Cache key pattern to invalidate
   */
  async invalidateCache(keyPattern) {
    try {
      // Use production-safe SCAN instead of KEYS
      const keys = [];
      const stream = redisClient.scanStream({
        match: `*${keyPattern}*`,
        count: 100
      });

      await new Promise((resolve, reject) => {
        stream.on('data', (resultKeys) => {
          keys.push(...resultKeys);
        });
        stream.on('end', resolve);
        stream.on('error', reject);
      });

      if (keys.length > 0) {
        await redisClient.del(...keys);
        logger.debug(`Invalidated ${keys.length} Redis cache keys matching ${keyPattern}`);
      }
    } catch (error) {
      logger.error(`Error invalidating Redis cache for ${keyPattern}:`, error);
    }
  }

  /**
   * Add a manual sync job to the queue
   * @param {string} type - Type of data to sync (hospitals, staff, appointments)
   * @param {string} id - ID of the specific item to sync
   */
  async queueManualSync(type, id) {
    this.syncQueue.push({ type, id });
    
    // Process the queue if not already syncing
    if (!this.syncInProgress) {
      await this.processQueue();
    }
  }

  /**
   * Process the manual sync queue
   */
  async processQueue() {
    if (this.syncQueue.length === 0 || this.syncInProgress) {
      return;
    }
    
    this.syncInProgress = true;
    
    try {
      while (this.syncQueue.length > 0) {
        const job = this.syncQueue.shift();
        
        if (job.type === 'hospitals') {
          const doc = await firestoreDb.collection('hospitals').doc(job.id).get();
          if (doc.exists) {
            await this.syncHospitalToPostgres(job.id, doc.data());
          }
        } else if (job.type === 'staff') {
          const doc = await firestoreDb.collection('staff').doc(job.id).get();
          if (doc.exists) {
            await this.syncStaffToPostgres(job.id, doc.data());
          }
        } else if (job.type === 'appointments') {
          const doc = await firestoreDb.collection('appointments').doc(job.id).get();
          if (doc.exists) {
            await this.syncAppointmentToPostgres(job.id, doc.data());
          }
        }
      }
    } catch (error) {
      logger.error('Error processing sync queue:', error);
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * Get data sync status
   * @returns {object} Sync status information
   */
  getStatus() {
    return {
      syncInProgress: this.syncInProgress,
      lastSyncTime: this.lastSyncTime,
      queueLength: this.syncQueue.length,
      syncInterval: this.syncInterval,
    };
  }
}

// Create singleton instance
const dataSyncService = new DataSyncService();

// Automatically start the sync service when this module is loaded.
// IMPORTANT: For true production reliability of background tasks in a Next.js
// application (especially in serverless environments), consider using a custom server
// and initializing this service there, or using a dedicated worker process
// or an external scheduler (e.g., cron job) to trigger syncs via a secure endpoint.
// Calling start() here is a simpler approach but might have limitations
// depending on the deployment environment and how often modules are reloaded.
if (process.env.NODE_ENV !== 'test') { // Avoid starting during unit tests if any
    dataSyncService.start();
}

export default dataSyncService;