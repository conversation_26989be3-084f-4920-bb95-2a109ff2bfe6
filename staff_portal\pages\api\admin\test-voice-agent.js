import { withRole } from '../../../lib/auth';
import redisClient from '../../../lib/redis_client';

// Only admin users can test voice agent integration
export default withRole(async (req, res) => {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  try {
    const { hospital_id, test_type } = req.body;
    
    // Use authenticated user's hospital if not provided
    const hospitalId = hospital_id || req.user.hospital_id;
    
    // Verify hospital ID matches the authenticated user's hospital
    if (hospitalId !== req.user.hospital_id) {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to test voice agent for this hospital'
      });
    }

    const results = {};

    switch (test_type) {
      case 'connectivity':
        results.connectivity = await testVoiceAgentConnectivity();
        break;
        
      case 'configuration':
        results.configuration = await testConfigurationSync(hospitalId);
        break;
        
      case 'scheduling':
        results.scheduling = await testSchedulingSystem(hospitalId);
        break;
        
      case 'performance':
        results.performance = await testPerformanceMetrics();
        break;
        
      case 'all':
        results.connectivity = await testVoiceAgentConnectivity();
        results.configuration = await testConfigurationSync(hospitalId);
        results.scheduling = await testSchedulingSystem(hospitalId);
        results.performance = await testPerformanceMetrics();
        break;
        
      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid test type. Use: connectivity, configuration, scheduling, performance, or all'
        });
    }

    // Calculate overall status
    const allTests = Object.values(results);
    const overallSuccess = allTests.every(test => test.success);
    
    return res.status(200).json({
      success: true,
      overall_status: overallSuccess ? 'healthy' : 'issues_detected',
      hospital_id: hospitalId,
      test_type: test_type,
      timestamp: new Date().toISOString(),
      results: results
    });

  } catch (error) {
    console.error('Voice agent test error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
}, ['admin']);

/**
 * Test basic connectivity to voice agent
 */
async function testVoiceAgentConnectivity() {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 10000);

  try {
    const voiceAgentUrl = process.env.VOICE_AGENT_URL;
    if (!voiceAgentUrl) {
      throw new Error('VOICE_AGENT_URL environment variable is required');
    }
    const healthUrl = `${voiceAgentUrl}/api/health`;

    const response = await fetch(healthUrl, {
      method: 'GET',
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (response.ok) {
      const data = await response.json();
      return {
        success: true,
        status: 'connected',
        response_time: Date.now(),
        voice_agent_status: data.status || 'unknown',
        details: data
      };
    } else {
      return {
        success: false,
        status: 'connection_failed',
        error: `HTTP ${response.status}: ${response.statusText}`
      };
    }

  } catch (error) {
    clearTimeout(timeoutId);
    
    if (error.name === 'AbortError') {
      return {
        success: false,
        status: 'timeout',
        error: 'Connection timed out after 10 seconds'
      };
    }

    return {
      success: false,
      status: 'error',
      error: error.message
    };
  }
}

/**
 * Test configuration synchronization
 */
async function testConfigurationSync(hospitalId) {
  try {
    // Test scheduling config refresh
    const configRefresh = await redisClient.triggerSchedulingConfigRefresh(hospitalId);
    
    // Test availability refresh
    const availabilityRefresh = await redisClient.triggerAvailabilityRefresh(hospitalId);

    const success = configRefresh.success && availabilityRefresh.success;

    return {
      success: success,
      status: success ? 'sync_working' : 'sync_failed',
      config_refresh: configRefresh,
      availability_refresh: availabilityRefresh
    };

  } catch (error) {
    return {
      success: false,
      status: 'sync_error',
      error: error.message
    };
  }
}

/**
 * Test scheduling system functionality
 */
async function testSchedulingSystem(hospitalId) {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 15000);

  try {
    const voiceAgentUrl = process.env.VOICE_AGENT_URL;
    if (!voiceAgentUrl) {
      throw new Error('VOICE_AGENT_URL environment variable is required');
    }
    const metricsUrl = `${voiceAgentUrl}/api/scheduling/performance-metrics`;

    const internalApiKey = process.env.INTERNAL_API_KEY;
    if (!internalApiKey) {
      throw new Error('INTERNAL_API_KEY environment variable is required');
    }

    const headers = {
      'Authorization': `Bearer ${internalApiKey}`,
      'Content-Type': 'application/json'
    };

    const response = await fetch(metricsUrl, {
      method: 'GET',
      headers: headers,
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (response.ok) {
      const data = await response.json();
      
      if (data.success) {
        const metrics = data.data;
        
        return {
          success: true,
          status: 'scheduling_operational',
          metrics: {
            total_requests: metrics.total_requests || 0,
            success_rate: metrics.success_rate_percent || 0,
            cache_hit_rate: metrics.cache_hit_rate_percent || 0,
            average_response_time: metrics.average_response_time_ms || 0
          },
          performance_assessment: assessSchedulingPerformance(metrics)
        };
      } else {
        return {
          success: false,
          status: 'scheduling_error',
          error: 'Failed to get scheduling metrics'
        };
      }
    } else {
      return {
        success: false,
        status: 'scheduling_unavailable',
        error: `HTTP ${response.status}: ${response.statusText}`
      };
    }

  } catch (error) {
    clearTimeout(timeoutId);
    
    return {
      success: false,
      status: 'scheduling_test_failed',
      error: error.message
    };
  }
}

/**
 * Test performance metrics
 */
async function testPerformanceMetrics() {
  try {
    // Test Redis connectivity
    const redisConnected = await redisClient.isRedisConnected();
    
    if (!redisConnected) {
      return {
        success: false,
        status: 'redis_disconnected',
        error: 'Redis connection failed'
      };
    }

    // Get basic Redis info
    const client = await redisClient.getClient();
    const redisInfo = await client.info('memory');
    
    return {
      success: true,
      status: 'performance_good',
      redis_connected: true,
      redis_memory_info: parseRedisMemoryInfo(redisInfo)
    };

  } catch (error) {
    return {
      success: false,
      status: 'performance_test_failed',
      error: error.message
    };
  }
}

/**
 * Assess scheduling system performance
 */
function assessSchedulingPerformance(metrics) {
  const assessments = [];
  
  if (metrics.success_rate_percent < 95) {
    assessments.push('Low success rate - investigate errors');
  }
  
  if (metrics.cache_hit_rate_percent < 70) {
    assessments.push('Low cache hit rate - consider cache optimization');
  }
  
  if (metrics.average_response_time_ms > 1000) {
    assessments.push('High response time - performance optimization needed');
  }
  
  if (assessments.length === 0) {
    assessments.push('Performance is within acceptable ranges');
  }
  
  return assessments;
}

/**
 * Parse Redis memory info
 * Handles both Windows (\r\n) and Unix (\n) line endings
 */
function parseRedisMemoryInfo(info) {
  const lines = info.split(/\r?\n/);
  const memoryInfo = {};

  lines.forEach(line => {
    if (line.includes(':')) {
      const [key, value] = line.split(':');
      if (key.includes('memory')) {
        memoryInfo[key] = value;
      }
    }
  });

  return memoryInfo;
}
