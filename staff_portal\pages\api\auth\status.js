import { withAuth } from '../../../lib/auth';

export default withAuth(async (req, res) => {
  // Only allow GET method
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  try {
    // Return user status from <PERSON>W<PERSON> token (added by withAuth middleware)
    return res.status(200).json({
      success: true,
      user: {
        id: req.user.id,
        name: req.user.name,
        role: req.user.role,
        email: req.user.email,
        hospital_id: req.user.hospital_id
      }
    });
  } catch (error) {
    console.error('User status error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});