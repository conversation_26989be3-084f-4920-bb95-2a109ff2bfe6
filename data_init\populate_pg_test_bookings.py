"""
<PERSON><PERSON><PERSON> to populate PostgreSQL test_bookings table with test data for today for each hospital.
"""
import os
import json
import logging
from datetime import datetime, time
from pathlib import Path
import uuid # Though not used for test_booking id, might be useful for other dummy data if needed

import firebase_admin
from firebase_admin import credentials, firestore
import psycopg2

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
)
logger = logging.getLogger(__name__)

DATA_DIR = Path(__file__).parent / "data"

# Global dictionary to store PostgreSQL connections
postgres_connections = {}

def init_firebase():
    """Initialize Firebase Admin SDK if not already initialized."""
    # Check if already initialized
    if firebase_admin._apps:
        logger.info("Firebase app already initialized.")
        return True
        
    # List of potential service account key paths to try
    potential_paths = [
        os.environ.get('GOOGLE_APPLICATION_CREDENTIALS'),
        os.path.join(os.path.expanduser('~'), 'Downloads', 'VoiceHealthPortal', 'healthrepl-64fc8d6938f3.json'),
        os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'staff_portal', '.firebase-service-account.json'),
        # Add the path from the .env file
        r'C:\Users\<USER>\Downloads\VoiceHealthPortal\healthrepl-64fc8d6938f3.json'
    ]
    
    # Try each path in order
    for path in potential_paths:
        if path and os.path.exists(path):
            try:
                cred = credentials.Certificate(path)
                firebase_admin.initialize_app(cred)
                logger.info(f"Firebase initialized successfully with service account key from: {path}")
                return True
            except Exception as e:
                logger.warning(f"Failed to initialize with key at {path}: {e}")
                # Continue to next path
    
    # If we get here, all paths failed, try ADC as last resort
    try:
        logger.warning("All service account paths failed. Attempting to use Application Default Credentials.")
        firebase_admin.initialize_app()
        logger.info("Firebase initialized successfully with default credentials (ADC).")
        return True
    except Exception as e:
        logger.error(f"All Firebase initialization methods failed. Last error: {e}")
        return False

def get_postgres_connection(db_url):
    """Establishes a PostgreSQL connection using the provided URL."""
    conn = None
    try:
        conn = psycopg2.connect(db_url)
        logger.info(f"PostgreSQL connection established to {db_url.split('@')[-1] if '@' in db_url else 'database'}.")
    except psycopg2.Error as e:
        logger.error(f"Error connecting to PostgreSQL database at {db_url.split('@')[-1] if '@' in db_url else 'database'}: {e}")
    return conn

def close_all_postgres_connections():
    """Close all active PostgreSQL connections."""
    for hospital_id, conn_info in list(postgres_connections.items()):
        if conn_info['connection']:
            try:
                conn_info['connection'].close()
                logger.info(f"Closed PostgreSQL connection for hospital {hospital_id}.")
            except psycopg2.Error as e:
                logger.error(f"Error closing PostgreSQL connection for hospital {hospital_id}: {e}")
        del postgres_connections[hospital_id]

def load_tests_data_from_json():
    """Loads test data from tests.json."""
    tests_file_path = DATA_DIR / "tests.json"
    try:
        with open(tests_file_path, 'r') as f:
            data = json.load(f)
            logger.info(f"Successfully loaded test data from {tests_file_path}")
            return data
    except FileNotFoundError:
        logger.error(f"Error: The file {tests_file_path} was not found.")
    except json.JSONDecodeError:
        logger.error(f"Error: Could not decode JSON from {tests_file_path}.")
    except Exception as e:
        logger.error(f"An unexpected error occurred while loading {tests_file_path}: {e}")
    return None

def insert_test_bookings_for_hospital(pg_conn, hospital_numeric_id, tests_for_hospital):
    """Inserts test booking data for a specific hospital for today."""
    inserted_count = 0
    today_date = datetime.now().date()
    
    # Define a few time slots for today's test bookings
    booking_time_slots = [
        time(10, 0),  # 10:00 AM
        time(14, 30)  # 02:30 PM
    ]

    dummy_patient_details = [
        {"name": "Test Patient TB Alpha", "phone": "555-0101"},
        {"name": "Test Patient TB Beta", "phone": "555-0102"}
    ]

    if not tests_for_hospital:
        logger.info(f"No tests found for hospital {hospital_numeric_id} in tests.json. Skipping test booking insertion.")
        return 0

    # First, check the table structure to understand if id is SERIAL or needs to be provided
    with pg_conn.cursor() as check_cur:
        try:
            # Check if the table exists and get its structure
            check_cur.execute("""
                SELECT column_name, column_default, is_nullable, data_type 
                FROM information_schema.columns 
                WHERE table_name = 'test_bookings' AND column_name = 'id'
            """)
            id_column_info = check_cur.fetchone()
            
            # If id column has a default value (like nextval for SERIAL), we don't need to provide it
            has_serial_id = id_column_info and 'nextval' in str(id_column_info[1]) if id_column_info else False
            logger.info(f"ID column info: {id_column_info}, Using SERIAL: {has_serial_id}")
        except Exception as e:
            logger.warning(f"Could not check table structure: {e}")
            has_serial_id = False  # Default to providing ID
    
    with pg_conn.cursor() as cur:
        for test_info in tests_for_hospital:
            test_type_id = test_info.get('id')
            if not test_type_id:
                logger.warning(f"Skipping a test in hospital {hospital_numeric_id} due to missing 'id' (test_type_id).")
                continue

            for i, slot_time in enumerate(booking_time_slots):
                patient_detail = dummy_patient_details[i % len(dummy_patient_details)] # Cycle through patients
                booking_datetime = datetime.combine(today_date, slot_time)
                
                # Generate a unique ID for each booking if needed
                booking_id = str(uuid.uuid4())
                
                if has_serial_id:
                    # Let PostgreSQL generate the ID with SERIAL
                    sql = """
                    INSERT INTO test_bookings (patient_name, phone, test_type_id, hospital_id,
                                              booking_time, status, notes, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
                    RETURNING id;
                    """
                    params = (
                        patient_detail['name'],
                        patient_detail['phone'],
                        test_type_id,
                        str(hospital_numeric_id),
                        booking_datetime,
                        'scheduled',
                        f'Test booking for {test_info.get("name", "Unknown Test")} generated by script.'
                    )
                else:
                    # Provide our own ID
                    sql = """
                    INSERT INTO test_bookings (id, patient_name, phone, test_type_id, hospital_id,
                                              booking_time, status, notes, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
                    RETURNING id;
                    """
                    params = (
                        booking_id,
                        patient_detail['name'],
                        patient_detail['phone'],
                        test_type_id,
                        str(hospital_numeric_id),
                        booking_datetime,
                        'scheduled',
                        f'Test booking for {test_info.get("name", "Unknown Test")} generated by script.'
                    )
                
                try:
                    cur.execute(sql, params)
                    result = cur.fetchone()
                    if result:
                        inserted_count += 1
                        logger.info(f"Inserted test booking (ID: {result[0]}) for test '{test_type_id}' at {booking_datetime} in hospital {hospital_numeric_id}.")
                except psycopg2.Error as e:
                    logger.error(f"Error inserting test booking for test '{test_type_id}' in hospital {hospital_numeric_id}: {e}")
                    pg_conn.rollback() # Rollback this transaction on error
                except Exception as ex:
                    logger.error(f"An unexpected error occurred during test booking insertion for {test_type_id}: {ex}")
                    pg_conn.rollback()
        pg_conn.commit() # Commit all successful insertions for this hospital
    return inserted_count

def main():
    logger.info("--- Starting Phase: Populate PostgreSQL with Test Booking Data for Today ---")
    if not init_firebase():
        logger.error("Firebase initialization failed. Exiting script.")
        return

    db = firestore.client()
    hospitals_ref = db.collection('hospitals')
    total_test_bookings_inserted_all_hospitals = 0

    all_tests_data = load_tests_data_from_json()
    if not all_tests_data:
        logger.error("Failed to load tests.json. Cannot proceed with test booking population.")
        return

    try:
        hospital_docs = hospitals_ref.stream()
        hospitals_list = [doc.to_dict() for doc in hospital_docs]
        if not hospitals_list:
            logger.warning("No hospitals found in Firestore. Nothing to process.")
            return

        for hospital_data in hospitals_list:
            hospital_name = hospital_data.get('name', 'Unknown Hospital')
            hospital_numeric_id = hospital_data.get('id') # This is the numeric string ID like '123'
            db_connection_string = hospital_data.get('db_connection_string')

            if not hospital_numeric_id:
                logger.warning(f"Skipping hospital '{hospital_name}' due to missing 'id'.")
                continue
            if not db_connection_string:
                logger.warning(f"Skipping hospital '{hospital_name}' (ID: {hospital_numeric_id}) due to missing 'db_connection_string'.")
                continue

            logger.info(f"--- Processing hospital: {hospital_name} (ID: {hospital_numeric_id}) ---")
            
            pg_conn = postgres_connections.get(hospital_numeric_id, {}).get('connection')
            if not pg_conn or pg_conn.closed:
                pg_conn = get_postgres_connection(db_connection_string)
                if pg_conn:
                    postgres_connections[hospital_numeric_id] = {'connection': pg_conn, 'db_url': db_connection_string}
                else:
                    logger.error(f"Failed to establish PostgreSQL connection for hospital {hospital_numeric_id}. Skipping.")
                    continue
            
            tests_for_this_hospital = all_tests_data.get(str(hospital_numeric_id)) # Ensure key is string
            if not tests_for_this_hospital:
                logger.info(f"No specific test configurations found for hospital {hospital_numeric_id} in tests.json. Skipping test booking population for this hospital.")
                continue
            
            inserted_this_hospital = insert_test_bookings_for_hospital(pg_conn, hospital_numeric_id, tests_for_this_hospital)
            logger.info(f"Inserted {inserted_this_hospital} test bookings for hospital {hospital_numeric_id}.")
            total_test_bookings_inserted_all_hospitals += inserted_this_hospital

    except Exception as e:
        logger.error(f"An error occurred during the main processing loop: {e}", exc_info=True)
    finally:
        close_all_postgres_connections()
        logger.info(f"--- Phase Finished: Total test bookings inserted across all hospitals: {total_test_bookings_inserted_all_hospitals} ---")

if __name__ == "__main__":
    main()
