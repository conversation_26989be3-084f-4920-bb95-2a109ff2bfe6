import { withRole } from '../../../lib/auth';
import dataSyncService from '../../../lib/data_sync';

// Only admin users can sync databases
export default withRole(async (req, res) => {
  // Only allow POST method
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  try {
    const { hospitalId } = req.body;
    
    // Validate required parameters
    if (!hospitalId) {
      return res.status(400).json({
        success: false,
        message: 'Hospital ID is required'
      });
    }
    
    // Verify hospital ID matches the authenticated user's hospital
    if (hospitalId !== req.user.hospital_id) {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to sync databases for this hospital'
      });
    }
    
    // Sync databases
    await dataSyncService.queueManualSync('hospitals', hospitalId);
    
    // Since queueManualSync adds to a queue and processes asynchronously,
    // we'll assume successful queuing means the process has started.
    // For a more detailed status, a separate status endpoint or different sync method might be needed.
    return res.status(200).json({
      success: true,
      message: `Database sync for hospital ${hospitalId} has been queued.`
    });
  } catch (error) {
    console.error('Sync databases error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
}, ['admin']); // Only admin role can access this endpoint