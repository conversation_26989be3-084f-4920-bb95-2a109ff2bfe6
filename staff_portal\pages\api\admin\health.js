import { withRole } from '../../../lib/auth';
import { withRateLimit } from '../../../lib/rate_limiter';
import { logger } from '../../../lib/logger';
import performanceMonitor from '../../../lib/performance_monitor';
import dataSyncService from '../../../lib/data_sync';
import { pool as pgPool } from '../../../server/db';
import { db as firestoreDb } from '../../../lib/firebase';
import Redis from 'ioredis';

// Initialize Redis client
const redisClient = new Redis(process.env.REDIS_URL || 'redis://localhost:6379');

/**
 * API endpoint for checking system health and component status
 * Provides comprehensive health metrics for all system components
 */
async function handler(req, res) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  try {
    // Start timing the health check
    const startTime = Date.now();
    
    // Track whether we're doing a deep check
    const deepCheck = req.query.deep === 'true';
    
    // Run all checks in parallel
    const [
      pgStatus,
      redisStatus,
      firestoreStatus,
      dataSyncStatus,
      performanceStats
    ] = await Promise.all([
      checkPostgres(deepCheck),
      checkRedis(deepCheck),
      checkFirestore(deepCheck),
      getDataSyncStatus(),
      deepCheck ? performanceMonitor.getStats('all', 'hour') : null
    ]);
    
    // Determine overall system health
    const overallHealth = determineOverallHealth(
      pgStatus, 
      redisStatus, 
      firestoreStatus
    );
    
    // Calculate response time
    const responseTime = Date.now() - startTime;
    
    // Construct the response
    const healthStatus = {
      status: overallHealth.status,
      message: overallHealth.message,
      timestamp: new Date().toISOString(),
      responseTime: `${responseTime}ms`,
      components: {
        postgres: pgStatus,
        redis: redisStatus,
        firestore: firestoreStatus,
        dataSync: dataSyncStatus
      }
    };
    
    // Add performance stats if deep check was requested
    if (deepCheck && performanceStats) {
      healthStatus.performance = performanceStats;
    }
    
    // Add environment info
    healthStatus.environment = {
      nodeEnv: process.env.NODE_ENV || 'development',
      nodeVersion: process.version,
      uptime: Math.floor(process.uptime()),
      memoryUsage: process.memoryUsage().rss / (1024 * 1024) // Convert to MB
    };
    
    // Log the health check
    logger.info('Health check completed', {
      status: overallHealth.status,
      responseTime
    });
    
    // Return appropriate status code based on health
    const statusCode = overallHealth.status === 'healthy' ? 200 : 
                      overallHealth.status === 'degraded' ? 200 : 503;
    
    return res.status(statusCode).json({
      success: true,
      health: healthStatus
    });
    
  } catch (error) {
    logger.error('Health check error:', error);
    
    return res.status(500).json({
      success: false,
      message: 'Health check failed',
      error: error.message
    });
  }
}

/**
 * Check PostgreSQL database health
 * @param {boolean} deep - Whether to perform a deep check
 * @returns {Promise<Object>} Database health status
 */
async function checkPostgres(deep = false) {
  try {
    // Basic connection check
    const startTime = Date.now();
    const basicResult = await pgPool.query('SELECT 1 as connection_test');
    const responseTime = Date.now() - startTime;
    
    if (!basicResult || !basicResult.rows || basicResult.rows.length === 0) {
      return {
        status: 'unhealthy',
        message: 'PostgreSQL connection failed',
        responseTime: `${responseTime}ms`
      };
    }
    
    // For deep checks, get additional database information
    let dbInfo = {};
    
    if (deep) {
      try {
        const infoResult = await pgPool.query(`
          SELECT 
            current_database() as database,
            current_user as user,
            version() as version,
            pg_database_size(current_database())/1024/1024 as size_mb,
            (SELECT count(*) FROM pg_stat_activity) as connections
        `);
        
        if (infoResult && infoResult.rows && infoResult.rows.length > 0) {
          dbInfo = infoResult.rows[0];
        }
      } catch (error) {
        logger.warn('Error getting PostgreSQL detailed info:', error);
        dbInfo = { error: error.message };
      }
    }
    
    return {
      status: 'healthy',
      message: 'PostgreSQL connection successful',
      responseTime: `${responseTime}ms`,
      ...(deep ? { info: dbInfo } : {})
    };
    
  } catch (error) {
    logger.error('PostgreSQL health check error:', error);
    
    return {
      status: 'unhealthy',
      message: `PostgreSQL connection error: ${error.message}`,
      error: error.message
    };
  }
}

/**
 * Check Redis health
 * @param {boolean} deep - Whether to perform a deep check
 * @returns {Promise<Object>} Redis health status
 */
async function checkRedis(deep = false) {
  try {
    // Basic connection check
    const startTime = Date.now();
    const pingResult = await redisClient.ping();
    const responseTime = Date.now() - startTime;
    
    if (pingResult !== 'PONG') {
      return {
        status: 'unhealthy',
        message: 'Redis ping failed',
        responseTime: `${responseTime}ms`
      };
    }
    
    // For deep checks, get additional Redis information
    let redisInfo = {};
    
    if (deep) {
      try {
        const info = await redisClient.info();
        const infoLines = info.split('\r\n');
        
        // Parse the info response
        infoLines.forEach(line => {
          if (line && !line.startsWith('#') && line.includes(':')) {
            const [key, value] = line.split(':');
            redisInfo[key] = value;
          }
        });
      } catch (error) {
        logger.warn('Error getting Redis detailed info:', error);
        redisInfo = { error: error.message };
      }
    }
    
    return {
      status: 'healthy',
      message: 'Redis connection successful',
      responseTime: `${responseTime}ms`,
      ...(deep ? { 
        info: {
          version: redisInfo.redis_version,
          uptime: redisInfo.uptime_in_seconds,
          memory: redisInfo.used_memory_human,
          clients: redisInfo.connected_clients
        } 
      } : {})
    };
    
  } catch (error) {
    logger.error('Redis health check error:', error);
    
    return {
      status: 'unhealthy',
      message: `Redis connection error: ${error.message}`,
      error: error.message
    };
  }
}

/**
 * Check Firestore health
 * @param {boolean} deep - Whether to perform a deep check
 * @returns {Promise<Object>} Firestore health status
 */
async function checkFirestore(deep = false) {
  try {
    // Basic connection check
    const startTime = Date.now();
    const healthCollection = firestoreDb.collection('health_checks');
    const healthDoc = healthCollection.doc('status');
    
    // Write test data
    await healthDoc.set({
      timestamp: new Date().toISOString(),
      message: 'Health check',
      serverTime: firestoreDb.FieldValue.serverTimestamp()
    });
    
    // Read test data
    const docSnapshot = await healthDoc.get();
    const responseTime = Date.now() - startTime;
    
    if (!docSnapshot.exists) {
      return {
        status: 'unhealthy',
        message: 'Firestore read/write test failed',
        responseTime: `${responseTime}ms`
      };
    }
    
    // For deep checks, get additional collection information
    let collectionsInfo = {};
    
    if (deep) {
      try {
        // Get basic info about key collections
        const collections = ['hospitals', 'staff', 'appointments'];
        
        for (const collectionName of collections) {
          const query = firestoreDb.collection(collectionName).limit(1);
          const snapshot = await query.get();
          collectionsInfo[collectionName] = {
            exists: !snapshot.empty,
            documentsFound: snapshot.size > 0
          };
        }
      } catch (error) {
        logger.warn('Error getting Firestore detailed info:', error);
        collectionsInfo = { error: error.message };
      }
    }
    
    return {
      status: 'healthy',
      message: 'Firestore connection successful',
      responseTime: `${responseTime}ms`,
      ...(deep ? { collections: collectionsInfo } : {})
    };
    
  } catch (error) {
    logger.error('Firestore health check error:', error);
    
    return {
      status: 'unhealthy',
      message: `Firestore connection error: ${error.message}`,
      error: error.message
    };
  }
}

/**
 * Get data sync service status
 * @returns {Promise<Object>} Data sync status
 */
async function getDataSyncStatus() {
  try {
    const status = dataSyncService.getStatus();
    
    return {
      status: 'healthy',
      message: 'Data sync service operational',
      lastSyncTime: status.lastSyncTime,
      syncInProgress: status.syncInProgress,
      queueLength: status.queueLength
    };
  } catch (error) {
    logger.error('Data sync status check error:', error);
    
    return {
      status: 'degraded',
      message: `Data sync service error: ${error.message}`,
      error: error.message
    };
  }
}

/**
 * Determine overall system health based on component statuses
 * @param {Object} pg - PostgreSQL status
 * @param {Object} redis - Redis status
 * @param {Object} firestore - Firestore status
 * @returns {Object} Overall health status
 */
function determineOverallHealth(pg, redis, firestore) {
  // Count unhealthy and degraded components
  const statuses = [pg.status, redis.status, firestore.status];
  const unhealthyCount = statuses.filter(s => s === 'unhealthy').length;
  const degradedCount = statuses.filter(s => s === 'degraded').length;
  
  if (unhealthyCount > 0) {
    return {
      status: 'unhealthy',
      message: `System is unhealthy with ${unhealthyCount} critical component(s) down`
    };
  }
  
  if (degradedCount > 0) {
    return {
      status: 'degraded',
      message: `System performance is degraded with ${degradedCount} component(s) experiencing issues`
    };
  }
  
  return {
    status: 'healthy',
    message: 'All systems operational'
  };
}

// Apply rate limiting
const rateLimitedHandler = withRateLimit(handler, { tier: 'admin' });

// Only allow access to users with admin role
export default withRole(rateLimitedHandler, ['admin', 'super_admin']);