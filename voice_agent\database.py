import os
import json
import logging
import asyncio
import time
from typing import Optional, Dict, Any

import psycopg2
from psycopg2.extras import RealDictCursor
import firebase_admin
from firebase_admin import credentials, firestore

# Module-local logger – configuration stays with the host app
logger = logging.getLogger(__name__)

# Initialize Firebase Admin SDK
try:
    # Check if already initialized
    firebase_admin.get_app()
except ValueError:
    # If not, initialize with credentials
    if os.environ.get('FIREBASE_CREDENTIALS'):
        # Use credentials from environment variable if available
        cred_dict = json.loads(os.environ.get('FIREBASE_CREDENTIALS'))
        cred = credentials.Certificate(cred_dict)
    else:
        # Use default application credentials
        cred = credentials.ApplicationDefault()
    
    firebase_admin.initialize_app(cred)

# Use shared Redis implementation for all Redis operations
from shared.redis.adapters.python_adapter import get_python_adapter

# Database connection cache
postgres_connections = {}

def get_firestore_db():
    """
    Get a Firestore database instance
    """
    return firestore.client()

async def get_postgres_connection(hospital_id: str):
    """
    Get a PostgreSQL connection for the specified hospital, using SSH tunnel if needed
    Returns a connection object
    """
    try:
        # Check if we already have a connection for this hospital
        if hospital_id in postgres_connections:
            # Check if connection is still valid
            conn = postgres_connections[hospital_id]
            try:
                cursor = conn.cursor()
                cursor.execute('SELECT 1')
                cursor.close()
                return conn
            except Exception:
                # Connection is dead, close it and create a new one
                try:
                    conn.close()
                except Exception:
                    pass
                del postgres_connections[hospital_id]
        
        # Get hospital configuration from Firestore
        db = get_firestore_db()
        # Updated path to fetch hospital configuration for DB connection
        loop = asyncio.get_running_loop()
        # Off-load the blocking Firestore call to the default executor
        hospital_doc = await loop.run_in_executor(None, db.collection('hospitals').document(f'hospital_{hospital_id}_data').get)
        
        if not hospital_doc.exists:
            raise Exception(f"Hospital configuration document not found at hospitals/hospital_{hospital_id}_data")
        
        hospital_data = hospital_doc.to_dict()
        db_connection_string = hospital_data.get('db_connection_string')
        
        # If SSH tunnel is used, connect to local port
        if 'ssh_tunnel' in hospital_data:
            # Get the allocated port from the port manager
            from .utils import port_manager
            local_port = port_manager.get_allocated_port(hospital_id, "ssh_tunnel")

            if local_port is None:
                logger.error(f"No SSH tunnel port allocated for hospital {hospital_id}. Ensure SSH tunnel is established first.")
                raise Exception(f"SSH tunnel not available for hospital {hospital_id}")

            conn_string = f"postgresql://user:pass@localhost:{local_port}/hospital_{hospital_id}"
        else:
            # Use the connection string from Firestore
            conn_string = db_connection_string
        
        # Create a PostgreSQL connection
        conn = psycopg2.connect(conn_string)
        conn.autocommit = False
        
        # Cache the connection
        postgres_connections[hospital_id] = conn
        
        return conn
    except Exception as e:
        logger.error(f"Error getting PostgreSQL connection for hospital {hospital_id}: {e}")
        raise

async def get_cached_data(key: str) -> Optional[Dict[str, Any]]:
    """
    Get data from Redis cache using centralized pool
    """
    try:
        adapter = get_python_adapter()
        async_client = adapter.connection_manager.get_async_client()
        if not async_client:
            logger.error("Failed to get async Redis client from shared implementation")
            return None

        data_str = await async_client.get(key)
        if data_str:
            return json.loads(data_str)
        return None
    except Exception as e:
        logger.error(f"Error getting data from cache: {e}")
        return None

async def set_cached_data(key: str, data: Dict[str, Any], ttl: int = 300):
    """
    Set data in Redis cache with TTL in seconds (default 5 minutes) using centralized pool
    """
    try:
        adapter = get_python_adapter()
        async_client = adapter.connection_manager.get_async_client()
        if not async_client:
            logger.error("Failed to get async Redis client from shared implementation")
            return

        await async_client.setex(key, ttl, json.dumps(data))
    except Exception as e:
        logger.error(f"Error setting data in cache: {e}")

async def delete_cached_data(key: str):
    """
    Delete data from Redis cache using centralized pool
    """
    try:
        adapter = get_python_adapter()
        async_client = adapter.connection_manager.get_async_client()
        if not async_client:
            logger.error("Failed to get async Redis client from shared implementation")
            return

        await async_client.delete(key)
    except Exception as e:
        logger.error(f"Error deleting data from cache: {e}")

async def get_doctors(hospital_id: str, force_refresh: bool = False) -> list:
    """
    Get doctors for the hospital, using cache if available
    """
    cache_key = f"hospital:{hospital_id}:doctors"
    
    if not force_refresh:
        cached_data = await get_cached_data(cache_key)
        if cached_data:
            return cached_data
    
    # If not in cache or force refresh, get from Firestore
    try:
        db = get_firestore_db()
        doctors = []
        
        # Query the doctors subcollection
        loop = asyncio.get_running_loop()
        # Off-load the blocking Firestore call to the default executor
        docs = await loop.run_in_executor(None, db.collection(f'hospital_{hospital_id}_data').document('doctors').collection('doctors').get)
        
        for doc in docs:
            doctor_data = doc.to_dict()
            doctor_data['id'] = doc.id
            doctors.append(doctor_data)
        
        # Cache the results
        await set_cached_data(cache_key, doctors)
        
        return doctors
    except Exception as e:
        logger.error(f"Error getting doctors from Firestore: {e}")
        return []

async def get_tests(hospital_id: str, force_refresh: bool = False) -> list:
    """
    Get tests for the hospital, using cache if available
    """
    cache_key = f"hospital:{hospital_id}:tests"
    
    if not force_refresh:
        cached_data = await get_cached_data(cache_key)
        if cached_data:
            return cached_data
    
    # If not in cache or force refresh, get from Firestore
    try:
        db = get_firestore_db()
        tests = []
        
        # Query the test_info subcollection
        loop = asyncio.get_running_loop()
        # Off-load the blocking Firestore call to the default executor
        docs = await loop.run_in_executor(None, db.collection(f'hospital_{hospital_id}_data').document('test_info').collection('tests').get)
        
        for doc in docs:
            test_data = doc.to_dict()
            test_data['id'] = doc.id
            tests.append(test_data)
        
        # Cache the results
        await set_cached_data(cache_key, tests)
        
        return tests
    except Exception as e:
        logger.error(f"Error getting tests from Firestore: {e}")
        return []

async def get_appointments(hospital_id: str, doctor_id: str = None, date: str = None) -> list:
    """
    Get appointments from PostgreSQL database for the specified hospital.
    Note: Appointments are stored ONLY in PostgreSQL (not in Firebase).
    Uses correct column name 'start_time' for consistency and optimized date queries.
    """
    try:
        conn = await get_postgres_connection(hospital_id)
        cursor = conn.cursor(cursor_factory=RealDictCursor)

        query = "SELECT * FROM appointments WHERE hospital_id = %s"
        params = [hospital_id]

        if doctor_id:
            query += " AND doctor_id = %s"
            params.append(doctor_id)

        if date:
            # Use range-based query instead of DATE() function for better performance
            # This allows the database to use indexes on start_time column
            start_of_day = f"{date} 00:00:00"
            end_of_day = f"{date} 23:59:59"
            query += " AND start_time >= %s AND start_time <= %s"
            params.extend([start_of_day, end_of_day])

        cursor.execute(query, params)
        appointments = cursor.fetchall()

        cursor.close()

        return appointments
    except Exception as e:
        logger.error(f"Error getting appointments: {e}")
        return []

async def get_test_bookings(hospital_id: str, test_type_id: str = None, date: str = None) -> list:
    """
    Get test bookings from PostgreSQL with proper hospital filtering and optimized date queries
    """
    try:
        conn = await get_postgres_connection(hospital_id)
        cursor = conn.cursor(cursor_factory=RealDictCursor)

        query = "SELECT * FROM test_bookings WHERE hospital_id = %s"
        params = [hospital_id]

        if test_type_id:
            query += " AND test_type_id = %s"
            params.append(test_type_id)

        if date:
            # Use range-based query instead of DATE() function for better performance
            # This allows the database to use indexes on booking_time column
            start_of_day = f"{date} 00:00:00"
            end_of_day = f"{date} 23:59:59"
            query += " AND booking_time >= %s AND booking_time <= %s"
            params.extend([start_of_day, end_of_day])

        cursor.execute(query, params)
        bookings = cursor.fetchall()

        cursor.close()

        return bookings
    except Exception as e:
        logger.error(f"Error getting test bookings: {e}")
        return []

async def save_call_recording(hospital_id: str, caller_number: str, recording_data: bytes, duration: int):
    """
    Save call recording to PostgreSQL
    """
    try:
        conn = await get_postgres_connection(hospital_id)
        cursor = conn.cursor()
        
        query = """
        INSERT INTO calls (caller_number, timestamp, duration, recording, hospital_id)
        VALUES (%s, CURRENT_TIMESTAMP, %s, %s, %s)
        """
        cursor.execute(query, (caller_number, duration, recording_data, hospital_id))
        
        conn.commit()
        cursor.close()
        
        return True
    except Exception as e:
        logger.error(f"Error saving call recording: {e}")
        return False

async def create_appointment(hospital_id: str, patient_name: str, phone: str, doctor_id: str, appointment_time: str):
    """
    Create a new appointment in PostgreSQL with atomic booking count management.

    This function:
    1. Validates booking availability before creating appointment
    2. Creates appointment in PostgreSQL using correct column names
    3. Atomically increments booking count in Firebase using transactions
    4. Updates Redis cache for real-time tracking
    5. Provides proper rollback on failures
    """
    try:
        # Extract date from appointment_time for booking count tracking
        if isinstance(appointment_time, str):
            # Parse appointment time to get date
            if ' ' in appointment_time:
                date_part = appointment_time.split(' ')[0]
            else:
                date_part = appointment_time
        else:
            # For datetime objects, format to string using datetime_utils
            from .datetime_utils import format_for_postgres
            formatted_date = format_for_postgres(appointment_time)
            if formatted_date:
                date_part = formatted_date.split(' ')[0]  # Extract date part
            else:
                # Fallback to manual formatting
                date_part = appointment_time.strftime('%Y-%m-%d')

        # Validate booking limit with distributed locking to prevent race conditions
        validation = await validate_booking_limit_with_lock(hospital_id, doctor_id, date_part)

        if not validation['valid']:
            error_msg = f"Booking not available: {validation.get('reason', 'unknown')}"
            if validation['reason'] == 'limit_exceeded':
                next_date = await get_next_available_date(hospital_id, doctor_id, date_part)
                if next_date:
                    error_msg += f" Next available date: {next_date}"
            raise Exception(error_msg)

        # Store lock key for cleanup
        lock_key = validation.get('lock_key')

        # Start database transaction using idiomatic psycopg2 transaction handling
        conn = await get_postgres_connection(hospital_id)
        cursor = None
        appointment_id = None

        try:
            cursor = conn.cursor()

            # Insert appointment using correct column name (start_time)
            # psycopg2 automatically starts a transaction when autocommit=False
            query = """
            INSERT INTO appointments (patient_name, phone, doctor_id, hospital_id, start_time, status, source, created_at)
            VALUES (%s, %s, %s, %s, %s, 'confirmed', 'voice', CURRENT_TIMESTAMP)
            RETURNING id
            """
            cursor.execute(query, (patient_name, phone, doctor_id, hospital_id, appointment_time))

            appointment_id = cursor.fetchone()[0]

            # Commit database transaction using connection's commit method
            conn.commit()

            # After successful database commit, atomically increment booking count
            await atomic_increment_booking_count(hospital_id, doctor_id, date_part)

            logger.info(f"Successfully created appointment {appointment_id} for doctor {doctor_id} on {date_part}")

        except Exception as db_error:
            # Rollback database transaction using connection's rollback method
            if conn:
                conn.rollback()

            logger.error(f"Database error creating appointment: {db_error}")
            raise db_error

        finally:
            # Always close cursor and release lock in finally block
            if cursor:
                cursor.close()

            # Release the distributed lock
            if lock_key:
                await release_booking_lock(lock_key)

        return appointment_id

    except Exception as e:
        logger.error(f"Error creating appointment: {e}")
        raise

async def create_test_booking(hospital_id: str, patient_name: str, phone: str, test_type_id: str, booking_time: str):
    """
    Create a new test booking in PostgreSQL with proper column names and transaction handling
    """
    conn = None
    cursor = None
    try:
        conn = await get_postgres_connection(hospital_id)
        cursor = conn.cursor()

        query = """
        INSERT INTO test_bookings (patient_name, phone, test_type_id, hospital_id, booking_time, status, source, created_at)
        VALUES (%s, %s, %s, %s, %s, 'scheduled', 'voice', CURRENT_TIMESTAMP)
        RETURNING id
        """
        cursor.execute(query, (patient_name, phone, test_type_id, hospital_id, booking_time))

        booking_id = cursor.fetchone()[0]

        conn.commit()
        return booking_id
    except Exception as e:
        if conn:
            conn.rollback()
        logger.error(f"Error creating test booking: {e}")
        raise
    finally:
        if cursor:
            cursor.close()

async def get_hospital_settings(hospital_id: str) -> Dict[str, Any]:
    """
    Get hospital settings from Firestore
    """
    cache_key = f"hospital:{hospital_id}:settings"
    
    # Try to get from cache first
    cached_data = await get_cached_data(cache_key)
    if cached_data:
        return cached_data
    
    try:
        db = get_firestore_db()
        loop = asyncio.get_running_loop()
        # Off-load the blocking Firestore call to the default executor
        settings_doc = await loop.run_in_executor(None, db.collection(f'hospital_{hospital_id}_data').document('settings').get)
        
        if settings_doc.exists:
            settings = settings_doc.to_dict()
            # Cache settings for future use
            await set_cached_data(cache_key, settings)
            return settings
        
        return {}
    except Exception as e:
        logger.error(f"Error getting hospital settings: {e}")
        return {}

async def update_hospital_settings(hospital_id: str, settings: Dict[str, Any]):
    """
    Update hospital settings in Firestore
    """
    try:
        db = get_firestore_db()
        db.collection(f'hospital_{hospital_id}_data').document('settings').set(settings, merge=True)
        
        # Update cache
        cache_key = f"hospital:{hospital_id}:settings"
        await set_cached_data(cache_key, settings)
        
        return True
    except Exception as e:
        logger.error(f"Error updating hospital settings: {e}")
        return False

async def get_staff_member(hospital_id: str, userid: str) -> Optional[Dict[str, Any]]:
    """
    Get staff member from Firestore by userid
    """
    try:
        db = get_firestore_db()
        staff_ref = db.collection(f'hospital_{hospital_id}_data').document('staff').collection('staff')
        
        # Query staff by userid
        query = staff_ref.where('credentials.userid', '==', userid).limit(1)
        loop = asyncio.get_running_loop()
        # Off-load the blocking Firestore call to the default executor
        docs = await loop.run_in_executor(None, query.get)
        
        for doc in docs:
            staff_data = doc.to_dict()
            staff_data['id'] = doc.id
            return staff_data
        
        return None
    except Exception as e:
        logger.error(f"Error getting staff member: {e}")
        return None

async def get_doctor_booking_limit(hospital_id: str, doctor_id: str, date: str) -> int:
    """
    Get daily booking limit for a specific doctor on a specific date.

    Args:
        hospital_id: Hospital identifier
        doctor_id: Doctor identifier
        date: Date in YYYY-MM-DD format

    Returns:
        Daily booking limit (default 10 if not set)
    """
    try:
        db = get_firestore_db()
        loop = asyncio.get_running_loop()

        # Get doctor document from Firebase
        doctor_ref = db.collection(f'hospital_{hospital_id}_data').document('doctors').collection('doctors').document(doctor_id)
        doctor_doc = await loop.run_in_executor(None, doctor_ref.get)

        if doctor_doc.exists:
            doctor_data = doctor_doc.to_dict()
            daily_limits = doctor_data.get('daily_booking_limits', {})

            # Get day of week from date using datetime_utils
            from .datetime_utils import get_day_name_from_date
            day_name = get_day_name_from_date(date)
            if not day_name:
                logger.error(f"Failed to parse date {date} for booking limit check")
                return 10  # Default fallback

            # Return limit for that day, default to 10
            return daily_limits.get(day_name, 10)

        logger.warning(f"Doctor {doctor_id} not found in hospital {hospital_id}")
        return 10  # Default limit

    except Exception as e:
        logger.error(f"Error getting doctor booking limit: {e}")
        return 10  # Default limit on error

async def get_doctor_booking_count(hospital_id: str, doctor_id: str, date: str) -> int:
    """
    Get current booking count for a specific doctor on a specific date.

    Args:
        hospital_id: Hospital identifier
        doctor_id: Doctor identifier
        date: Date in YYYY-MM-DD format

    Returns:
        Current booking count
    """
    try:
        # First try to get from Firebase cache
        db = get_firestore_db()
        loop = asyncio.get_running_loop()

        doctor_ref = db.collection(f'hospital_{hospital_id}_data').document('doctors').collection('doctors').document(doctor_id)
        doctor_doc = await loop.run_in_executor(None, doctor_ref.get)

        if doctor_doc.exists:
            doctor_data = doctor_doc.to_dict()
            current_bookings = doctor_data.get('current_bookings', {})

            # If we have cached count, use it
            if date in current_bookings:
                logger.info(f"Using cached booking count for doctor {doctor_id} on {date}: {current_bookings[date]}")
                return current_bookings[date]

        # If not cached, query PostgreSQL
        conn = await get_postgres_connection(hospital_id)
        cursor = conn.cursor()

        # Convert date to start and end of day timestamps
        start_of_day = f"{date} 00:00:00"
        end_of_day = f"{date} 23:59:59"

        query = """
        SELECT COUNT(*) as booking_count
        FROM appointments
        WHERE doctor_id = %s
          AND start_time >= %s
          AND start_time <= %s
          AND status != 'cancelled'
        """

        cursor.execute(query, (doctor_id, start_of_day, end_of_day))
        result = cursor.fetchone()
        count = result[0] if result else 0

        cursor.close()

        # Update Firebase cache asynchronously
        await update_doctor_booking_count_in_firebase(hospital_id, doctor_id, date, count)

        return count

    except Exception as e:
        logger.error(f"Error getting doctor booking count: {e}")
        return 0

async def check_booking_availability(hospital_id: str, doctor_id: str, date: str) -> Dict[str, Any]:
    """
    Check if booking is available for a doctor on a specific date.

    Args:
        hospital_id: Hospital identifier
        doctor_id: Doctor identifier
        date: Date in YYYY-MM-DD format

    Returns:
        Dict with availability info: {
            'is_available': bool,
            'current_count': int,
            'limit': int,
            'next_available_date': str or None
        }
    """
    try:
        # Get current count and limit
        current_count = await get_doctor_booking_count(hospital_id, doctor_id, date)
        limit = await get_doctor_booking_limit(hospital_id, doctor_id, date)

        is_available = current_count < limit
        next_available_date = None

        # If not available, find next available date
        if not is_available:
            next_available_date = await get_next_available_date(hospital_id, doctor_id, date)

        return {
            'is_available': is_available,
            'current_count': current_count,
            'limit': limit,
            'next_available_date': next_available_date
        }

    except Exception as e:
        logger.error(f"Error checking booking availability: {e}")
        # Return available on error to avoid blocking bookings
        return {
            'is_available': True,
            'current_count': 0,
            'limit': 10,
            'next_available_date': None
        }

async def update_doctor_booking_count_in_firebase(hospital_id: str, doctor_id: str, date: str, count: int):
    """
    Update doctor's booking count in Firebase for caching.

    Args:
        hospital_id: Hospital identifier
        doctor_id: Doctor identifier
        date: Date in YYYY-MM-DD format
        count: Current booking count
    """
    try:
        db = get_firestore_db()
        loop = asyncio.get_running_loop()

        doctor_ref = db.collection(f'hospital_{hospital_id}_data').document('doctors').collection('doctors').document(doctor_id)

        # Update current_bookings field
        await loop.run_in_executor(None, doctor_ref.update, {
            f'current_bookings.{date}': count,
            'updated_at': firestore.SERVER_TIMESTAMP
        })

        logger.info(f"Updated booking count in Firebase for doctor {doctor_id} on {date}: {count}")

    except Exception as e:
        logger.error(f"Error updating booking count in Firebase: {e}")

async def atomic_increment_booking_count(hospital_id: str, doctor_id: str, date: str):
    """
    Atomically increment booking count using Firestore transactions to prevent race conditions.

    This function uses Firestore's atomic increment operation to ensure that concurrent
    booking requests don't lose count updates due to race conditions.

    Args:
        hospital_id: Hospital identifier
        doctor_id: Doctor identifier
        date: Date in YYYY-MM-DD format
    """
    try:
        db = get_firestore_db()
        loop = asyncio.get_running_loop()

        # Use Firestore transaction for atomic increment
        def transaction_update(transaction, doctor_ref):
            # Get current doctor document
            doctor_doc = doctor_ref.get(transaction=transaction)

            if doctor_doc.exists:
                doctor_data = doctor_doc.to_dict()
                current_bookings = doctor_data.get('current_bookings', {})

                # Atomically increment the count
                current_count = current_bookings.get(date, 0)
                new_count = current_count + 1

                # Update the document with new count
                transaction.update(doctor_ref, {
                    f'current_bookings.{date}': new_count,
                    'updated_at': firestore.SERVER_TIMESTAMP
                })

                return new_count
            else:
                # If doctor document doesn't exist, create it with initial count
                transaction.set(doctor_ref, {
                    'current_bookings': {date: 1},
                    'created_at': firestore.SERVER_TIMESTAMP,
                    'updated_at': firestore.SERVER_TIMESTAMP
                })
                return 1

        # Execute transaction
        doctor_ref = db.collection(f'hospital_{hospital_id}_data').document('doctors').collection('doctors').document(doctor_id)

        # Run transaction in executor to avoid blocking
        new_count = await loop.run_in_executor(
            None,
            lambda: db.run_transaction(transaction_update, doctor_ref)
        )

        # Update Redis cache asynchronously for real-time access
        await update_redis_booking_count(hospital_id, doctor_id, date, new_count)

        logger.info(f"Atomically incremented booking count for doctor {doctor_id} on {date} to {new_count}")

        return new_count

    except Exception as e:
        logger.error(f"Error atomically incrementing booking count: {e}")
        # Don't raise exception to avoid breaking appointment creation
        # The count will be corrected on next cache refresh

async def update_redis_booking_count(hospital_id: str, doctor_id: str, date: str, count: int):
    """
    Update Redis cache with current booking count for real-time access.

    Uses Redis pipeline for atomic operations and proper error handling.

    Args:
        hospital_id: Hospital identifier
        doctor_id: Doctor identifier
        date: Date in YYYY-MM-DD format
        count: Current booking count
    """
    try:
        pool = RedisConnectionPool()
        async_client = pool.get_async_client()
        if not async_client:
            logger.warning("Redis client not available for booking count update")
            return

        # Use Redis pipeline for atomic operations
        pipe = async_client.pipeline()

        # Set booking count with expiration (24 hours)
        booking_key = f"hospital:{hospital_id}:doctor:{doctor_id}:bookings:{date}"
        pipe.setex(booking_key, 86400, str(count))  # 24 hours TTL

        # Also update availability status cache
        limit = await get_doctor_booking_limit(hospital_id, doctor_id, date)
        is_available = count < limit

        availability_key = f"hospital:{hospital_id}:doctor:{doctor_id}:available:{date}"
        availability_data = {
            'is_available': is_available,
            'current_count': count,
            'limit': limit,
            'updated_at': time.time()
        }
        pipe.setex(availability_key, 86400, json.dumps(availability_data))

        # Execute pipeline atomically
        await pipe.execute()

        logger.info(f"Updated Redis booking count for doctor {doctor_id} on {date}: {count}/{limit}")

    except Exception as e:
        logger.error(f"Error updating Redis booking count: {e}")
        # Don't raise exception to avoid breaking the main flow

async def validate_booking_limit_with_lock(hospital_id: str, doctor_id: str, date: str) -> Dict[str, Any]:
    """
    Validate booking limit with distributed locking to prevent race conditions.

    This function checks if a booking can be made by:
    1. Acquiring a distributed lock for the doctor-date combination
    2. Getting the current count from the most authoritative source
    3. Checking against the limit
    4. Returning validation result

    Args:
        hospital_id: Hospital identifier
        doctor_id: Doctor identifier
        date: Date in YYYY-MM-DD format

    Returns:
        Dict with validation result and lock information
    """
    try:
        pool = RedisConnectionPool()
        async_client = pool.get_async_client()

        # Create distributed lock key
        lock_key = f"booking_lock:{hospital_id}:{doctor_id}:{date}"
        lock_timeout = 10  # 10 seconds timeout

        # Try to acquire lock
        lock_acquired = await async_client.set(lock_key, "locked", nx=True, ex=lock_timeout)

        if not lock_acquired:
            return {
                'valid': False,
                'reason': 'booking_in_progress',
                'message': 'Another booking is in progress for this doctor and date',
                'lock_acquired': False
            }

        try:
            # Get current count and limit
            current_count = await get_doctor_booking_count(hospital_id, doctor_id, date)
            limit = await get_doctor_booking_limit(hospital_id, doctor_id, date)

            is_valid = current_count < limit

            return {
                'valid': is_valid,
                'current_count': current_count,
                'limit': limit,
                'lock_acquired': True,
                'lock_key': lock_key,
                'reason': 'limit_exceeded' if not is_valid else 'valid'
            }

        except Exception as validation_error:
            # Release lock on validation error
            await async_client.delete(lock_key)
            raise validation_error

    except Exception as e:
        logger.error(f"Error validating booking limit with lock: {e}")
        return {
            'valid': False,
            'reason': 'validation_error',
            'message': str(e),
            'lock_acquired': False
        }

async def release_booking_lock(lock_key: str):
    """
    Release distributed booking lock.

    Args:
        lock_key: Redis lock key to release
    """
    try:
        pool = RedisConnectionPool()
        async_client = pool.get_async_client()
        if async_client:
            await async_client.delete(lock_key)
            logger.debug(f"Released booking lock: {lock_key}")
    except Exception as e:
        logger.error(f"Error releasing booking lock {lock_key}: {e}")

async def get_next_available_date(hospital_id: str, doctor_id: str, start_date: str) -> str:
    """
    Find the next available date for a doctor starting from the given date.

    Args:
        hospital_id: Hospital identifier
        doctor_id: Doctor identifier
        start_date: Starting date in YYYY-MM-DD format

    Returns:
        Next available date in YYYY-MM-DD format or None if not found within 30 days
    """
    try:
        from datetime import timedelta

        # Get doctor's schedule from Firebase
        db = get_firestore_db()
        loop = asyncio.get_running_loop()

        doctor_ref = db.collection(f'hospital_{hospital_id}_data').document('doctors').collection('doctors').document(doctor_id)
        doctor_doc = await loop.run_in_executor(None, doctor_ref.get)

        if not doctor_doc.exists:
            logger.warning(f"Doctor {doctor_id} not found")
            return None

        doctor_data = doctor_doc.to_dict()
        schedule = doctor_data.get('schedule', {})
        daily_limits = doctor_data.get('daily_booking_limits', {})

        # Start checking from the next day using datetime_utils
        from .datetime_utils import parse_date_robust, get_day_name_from_date

        start_date_obj = parse_date_robust(start_date)
        if not start_date_obj:
            logger.error(f"Failed to parse start_date {start_date}")
            return None

        current_date = start_date_obj + timedelta(days=1)
        max_check_date = current_date + timedelta(days=30)  # Check up to 30 days ahead

        while current_date <= max_check_date:
            date_str = current_date.strftime('%Y-%m-%d')
            day_name = get_day_name_from_date(current_date)

            if not day_name:
                current_date += timedelta(days=1)
                continue

            # Check if doctor works on this day
            day_schedule = schedule.get(day_name, [])
            if day_schedule:  # Doctor works on this day
                # Get booking limit for this day
                limit = daily_limits.get(day_name, 10)

                if limit > 0:  # Doctor accepts bookings on this day
                    # Check current booking count
                    current_count = await get_doctor_booking_count(hospital_id, doctor_id, date_str)

                    if current_count < limit:
                        logger.info(f"Found next available date for doctor {doctor_id}: {date_str}")
                        return date_str

            current_date += timedelta(days=1)

        logger.warning(f"No available dates found for doctor {doctor_id} within 30 days from {start_date}")
        return None

    except Exception as e:
        logger.error(f"Error finding next available date: {e}")
        return None

async def close_all_connections():
    """
    Close all PostgreSQL connections
    """
    for hospital_id, conn in postgres_connections.items():
        try:
            conn.close()
            logger.info(f"Closed PostgreSQL connection for hospital {hospital_id}")
        except Exception as e:
            logger.error(f"Error closing PostgreSQL connection for hospital {hospital_id}: {e}")

    postgres_connections.clear()
