# Voice Agent Requirements
# Note: Semantic processing, IndicBERT, and Redis functionality moved to shared/redis/
# Voice agent now uses shared Redis implementation for all caching and semantic operations

# Core web framework - Updated for Python 3.13 compatibility
fastapi==0.115.13
uvicorn==0.34.3
pydantic==2.11.7

# Firebase and database
firebase-admin==6.2.0
psycopg2-binary==2.9.10

# Voice processing
google-cloud-texttospeech==2.26.0

# Network and SSH
sshtunnel==0.4.0
paramiko==3.5.1
requests==2.32.4  # For HTTP calls to shared LLM service

# LLM and AI
openai==1.88.0  # For OpenAI API integration
python-dotenv==1.1.0  # For environment variable management

# jambonz-node-client-sdk==0.1.0

# Fast fuzzy matching (voice-agent specific)
rapidfuzz==3.13.0

# Text processing - Basic NLP for voice agent
nltk==3.9.1
spacy==3.8.7

# Performance monitoring
psutil==7.0.0

# Development and testing - Updated for Python 3.13 compatibility
pytest==8.4.0
pytest-asyncio==1.0.0

# Note: The following dependencies have been moved to shared/redis/requirements.txt:
# - sentence-transformers (semantic processing)
# - transformers (IndicBERT support)
# - sentencepiece (IndicBERT tokenization)
# - torch (ML framework)
# - indic-nlp-library (Indian language processing)
# - redis (Redis client)
# - numpy (scientific computing)
# - scipy (scientific computing)
#
# Install shared dependencies with: pip install -r shared/redis/requirements.txt