import { withAuth } from '../../../lib/auth';
import { getDoctor } from '../../../lib/firebase';
import { logger } from '../../../lib/logger';

export default withAuth(async (req, res) => {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({ success: false, message: `Method ${req.method} Not Allowed` });
  }

  const hospitalId = req.user?.hospital_id;
  if (!hospitalId) {
    return res.status(401).json({ success: false, message: 'User not authenticated or hospital ID missing.' });
  }

  const { doctorId } = req.query;
  if (!doctorId) {
    return res.status(400).json({ success: false, message: 'Doctor ID is required.' });
  }

  try {
    logger.info(`[API_GET_DOCTOR] Fetching doctor ${doctorId} for hospital ${hospitalId}`);
    const doctorResult = await getDoctor(hospitalId, doctorId);

    if (doctorResult.success) {
      return res.status(200).json({ success: true, data: doctorResult.data });
    } else {
      logger.warn(`[API_GET_DOCTOR] Doctor ${doctorId} not found for hospital ${hospitalId}: ${doctorResult.error}`);
      return res.status(404).json({ success: false, message: doctorResult.error || 'Doctor not found' });
    }
  } catch (error) {
    logger.error(`[API_GET_DOCTOR] Error fetching doctor ${doctorId} for hospital ${hospitalId}:`, error);
    return res.status(500).json({ success: false, message: `Internal server error: ${error.message}` });
  }
});
