import jwt from 'jsonwebtoken';
import { serialize, parse } from 'cookie';
import bcrypt from 'bcryptjs';
import { getStaffByUserId } from './firebase';

// IMPORTANT: JWT_SECRET must be set as a strong, unique environment variable in production.
// The application will not start without it. For development, you can set it in your .env file.
// Example: JWT_SECRET=your-super-strong-randomly-generated-secret-key
const JWT_SECRET = process.env.JWT_SECRET;

if (!JWT_SECRET) {
  console.error("FATAL ERROR: JWT_SECRET environment variable is not set.");
  console.error("Application cannot start without a valid JWT_SECRET.");
  console.error("Please set it in your environment variables (e.g., .env file for development).");
  throw new Error("JWT_SECRET environment variable is not set. Application cannot securely operate.");
}

// JWT expiration time (8 hours)
const JWT_EXPIRY = 60 * 60 * 8;

/**
 * Hash a password with bcrypt
 * @param {string} password - The plain password to hash
 * @returns {Promise<string>} - Hashed password
 */
export const hashPassword = async (password) => {
  const saltRounds = 10;
  return await bcrypt.hash(password, saltRounds);
};

/**
 * Compare a password with a hash
 * @param {string} password - The plain password to check
 * @param {string} hash - The hash to compare against
 * @returns {Promise<boolean>} - True if password matches
 */
export const comparePassword = async (password, hash) => {
  return await bcrypt.compare(password, hash);
};

/**
 * Create a JWT token for a user
 * @param {object} payload - The data to encode in the token
 * @returns {string} - JWT token
 */
export const createToken = (payload) => {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRY });
};

/**
 * Verify a JWT token
 * @param {string} token - The token to verify
 * @returns {object|null} - Decoded token payload or null if invalid
 */
export const verifyToken = (token) => {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return null;
  }
};

/**
 * Set JWT token in HTTP-only cookie
 * @param {object} res - Next.js response object
 * @param {string} token - JWT token
 */
export const setTokenCookie = (res, token) => {
  const cookie = serialize('token', token, {
    httpOnly: true,
    secure: process.env.NODE_ENV !== 'development',
    sameSite: 'strict',
    maxAge: JWT_EXPIRY,
    path: '/',
    domain: process.env.COOKIE_DOMAIN || undefined,
  });
  
  res.setHeader('Set-Cookie', cookie);
};

/**
 * Remove JWT token cookie
 * @param {object} res - Next.js response object
 */
export const removeTokenCookie = (res) => {
  const cookie = serialize('token', '', {
    httpOnly: true,
    secure: process.env.NODE_ENV !== 'development',
    sameSite: 'strict',
    maxAge: -1,
    path: '/',
    domain: process.env.COOKIE_DOMAIN || undefined,
  });
  
  res.setHeader('Set-Cookie', cookie);
};

/**
 * Get JWT token from request cookies
 * @param {object} req - Next.js request object
 * @returns {string|null} - JWT token or null if not found
 */
export const getTokenFromCookies = (req) => {
  // Check for the cookie in the request
  const cookies = req.headers.cookie ? parse(req.headers.cookie) : {};
  return cookies.token || null;
};

/**
 * Authentication middleware for API routes
 * @param {function} handler - Next.js API route handler
 */
export const withAuth = (handler) => {
  return async (req, res) => {
    try {
      const token = getTokenFromCookies(req);
      
      if (!token) {
        return res.status(401).json({ success: false, message: 'Authentication required' });
      }
      
      const decoded = verifyToken(token);
      if (!decoded) {
        removeTokenCookie(res);
        return res.status(401).json({ success: false, message: 'Invalid token' });
      }
      
      // Add user data to request object
      req.user = decoded;
      
      // Get full staff data from Firestore
      const staffResult = await getStaffByUserId(decoded.id);
      if (!staffResult.success) {
        removeTokenCookie(res);
        return res.status(401).json({ success: false, message: 'Staff record not found' });
      }
      
      // Add full staff data to request
      req.staff = staffResult.data;
      
      // Continue to the API route handler
      return handler(req, res);
    } catch (error) {
      console.error('Auth middleware error:', error);
      return res.status(500).json({ success: false, message: 'Internal server error' });
    }
  };
};

/**
 * Role-based authorization middleware
 * @param {function} handler - Next.js API route handler
 * @param {Array<string>} allowedRoles - Array of allowed roles
 */
export const withRole = (handler, allowedRoles) => {
  return async (req, res) => {
    try {
      // First apply authentication middleware
      return withAuth(async (req, res) => {
        // Check if user role is in allowed roles
        if (!allowedRoles.includes(req.staff.role)) {
          return res.status(403).json({ success: false, message: 'Insufficient permissions' });
        }
        
        // Continue to the API route handler
        return handler(req, res);
      })(req, res);
    } catch (error) {
      console.error('Role middleware error:', error);
      return res.status(500).json({ success: false, message: 'Internal server error' });
    }
  };
};

/**
 * Get current user data from request
 * @param {object} req - Next.js request object
 * @returns {object|null} - User data or null if not authenticated
 */
export const getCurrentUserFromReq = async (req) => {
  try {
    const token = getTokenFromCookies(req);
    if (!token) return null;
    
    const decoded = verifyToken(token);
    if (!decoded) return null;
    
    const staffResult = await getStaffByUserId(decoded.id);
    if (!staffResult.success) return null;
    
    return staffResult.data;
  } catch (error) {
    console.error('Get current user error:', error);
    return null;
  }
};