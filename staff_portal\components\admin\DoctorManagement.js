import { useState } from 'react';
import {
  User,
  UserPlus,
  Edit,
  Trash2,
  Clock,
  CheckCircle,
  Save
} from 'react-feather';

// Input validation utilities
const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

const validateDoctorInput = (doctorData) => {
  const errors = [];

  // Name validation
  if (!doctorData.name?.trim()) {
    errors.push('Doctor name is required');
  } else if (doctorData.name.trim().length < 2) {
    errors.push('Doctor name must be at least 2 characters long');
  } else if (doctorData.name.trim().length > 100) {
    errors.push('Doctor name must be less than 100 characters');
  }

  // Specialty validation
  if (!doctorData.specialty?.trim()) {
    errors.push('Specialty is required');
  } else if (doctorData.specialty.trim().length < 2) {
    errors.push('Specialty must be at least 2 characters long');
  }

  // Phone validation
  if (!doctorData.phone?.trim()) {
    errors.push('Phone number is required');
  } else {
    const phoneRegex = /^[\+]?[1-9][\d]{9,14}$/;
    const cleanPhone = doctorData.phone.replace(/[\s\-\(\)]/g, '');
    if (!phoneRegex.test(cleanPhone) || cleanPhone.length < 10) {
      errors.push('Please enter a valid phone number (at least 10 digits)');
    }
  }

  // Email validation (optional but if provided should be valid)
  if (doctorData.email?.trim() && !validateEmail(doctorData.email.trim())) {
    errors.push('Please enter a valid email address');
  }

  // Available days validation
  if (!doctorData.availableDays || doctorData.availableDays.length === 0) {
    errors.push('At least one available day must be selected');
  }

  return errors;
};

// Helper function to show validation errors using notifications
const showValidationErrors = (errors, showError) => {
  if (errors.length === 1) {
    showError('Validation Error', errors[0]);
  } else {
    showError('Validation Errors', errors.join('\n'));
  }
};

export default function DoctorManagement({
  doctors,
  doctorOperations,
  user,
  showError,
  showSuccess
}) {
  // Doctor editing
  const [editingDoctor, setEditingDoctor] = useState(null);
  const [newDoctor, setNewDoctor] = useState({
    name: '',
    specialty: '',
    email: '',
    phone: '',
    availableDays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']
  });

  // Schedule management state
  const [managingSchedule, setManagingSchedule] = useState(null);
  const [doctorSchedule, setDoctorSchedule] = useState({
    monday: [],
    tuesday: [],
    wednesday: [],
    thursday: [],
    friday: [],
    saturday: [],
    sunday: []
  });
  const [loading, setLoading] = useState(false);

  // Create new doctor
  const createDoctor = async () => {
    if (!user) return;

    // Validate inputs
    const errors = validateDoctorInput(newDoctor);

    if (errors.length > 0) {
      showValidationErrors(errors, showError);
      return;
    }

    const result = await doctorOperations.createDoctor(newDoctor, user);

    if (result.success) {
      // Reset form
      setNewDoctor({
        name: '',
        specialty: '',
        email: '',
        phone: '',
        availableDays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']
      });
    }
  };

  // Update doctor
  const updateDoctor = async () => {
    if (!user || !editingDoctor) return;

    const doctorData = {
      name: editingDoctor.name,
      specialty: editingDoctor.specialty,
      email: editingDoctor.email,
      phone: editingDoctor.phone,
      availableDays: editingDoctor.availableDays
    };

    const result = await doctorOperations.updateDoctor(editingDoctor.id, doctorData, user);

    if (result.success) {
      setEditingDoctor(null);
    }
  };

  // Delete doctor
  const deleteDoctor = async (doctorId) => {
    if (!user || !doctorId) return;

    if (!confirm('Are you sure you want to delete this doctor? This action cannot be undone.')) {
      return;
    }

    await doctorOperations.deleteDoctor(doctorId, user);
  };

  // Schedule management functions
  const openScheduleManagement = async (doctor) => {
    setManagingSchedule(doctor);
    await fetchDoctorSchedule(doctor.id);
  };

  const fetchDoctorSchedule = async (doctorId) => {
    if (!user || !doctorId) return;

    try {
      setLoading(true);
      const res = await fetch(`/api/doctors/schedule?hospital_id=${user.hospital_id}&doctor_id=${doctorId}`);
      const data = await res.json();

      if (data.success) {
        setDoctorSchedule(data.data.schedule);
      } else {
        console.error('Failed to fetch doctor schedule:', data.message);
        showError('Fetch Failed', `Failed to fetch doctor schedule: ${data.message}`);
      }
    } catch (error) {
      console.error('Fetch doctor schedule error:', error);
      showError('Error', 'An error occurred while fetching doctor schedule');
    } finally {
      setLoading(false);
    }
  };

  const saveDoctorSchedule = async () => {
    if (!user || !managingSchedule) return;

    try {
      setLoading(true);
      const res = await fetch('/api/doctors/schedule', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          hospital_id: user.hospital_id,
          doctor_id: managingSchedule.id,
          schedule: doctorSchedule
        })
      });

      const data = await res.json();

      if (data.success) {
        showSuccess('Success', 'Doctor schedule saved successfully. Voice agent will be updated automatically.');
        setManagingSchedule(null);
      } else {
        showError('Save Failed', `Failed to save doctor schedule: ${data.message}`);
      }
    } catch (error) {
      console.error('Save doctor schedule error:', error);
      showError('Error', 'An error occurred while saving doctor schedule');
    } finally {
      setLoading(false);
    }
  };

  const addTimeSlot = (day) => {
    const existingSlots = doctorSchedule[day] || [];
    const newSlot = '09:00-10:00'; // Simple default
    setDoctorSchedule({
      ...doctorSchedule,
      [day]: [...existingSlots, newSlot]
    });
  };

  const removeTimeSlot = (day, index) => {
    const updatedSlots = doctorSchedule[day].filter((_, i) => i !== index);
    setDoctorSchedule({
      ...doctorSchedule,
      [day]: updatedSlots
    });
  };

  const updateTimeSlot = (day, index, value) => {
    const updatedSlots = [...doctorSchedule[day]];
    updatedSlots[index] = value;
    setDoctorSchedule({
      ...doctorSchedule,
      [day]: updatedSlots
    });
  };

  return (
    <div className="bg-white rounded-lg shadow overflow-hidden">
      <div className="px-6 py-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900">Doctors Management</h2>
      </div>
      
      {/* Add doctor form */}
      <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <h3 className="text-md font-medium text-gray-700 mb-3">Add New Doctor</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div>
            <label htmlFor="doctor-name" className="block text-sm font-medium text-gray-700">Name *</label>
            <input
              type="text"
              id="doctor-name"
              value={newDoctor.name}
              onChange={(e) => setNewDoctor({...newDoctor, name: e.target.value})}
              className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              required
            />
          </div>
          
          <div>
            <label htmlFor="doctor-specialty" className="block text-sm font-medium text-gray-700">Specialty *</label>
            <input
              type="text"
              id="doctor-specialty"
              value={newDoctor.specialty}
              onChange={(e) => setNewDoctor({...newDoctor, specialty: e.target.value})}
              className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              required
            />
          </div>
          
          <div>
            <label htmlFor="doctor-email" className="block text-sm font-medium text-gray-700">Email</label>
            <input
              type="email"
              id="doctor-email"
              value={newDoctor.email}
              onChange={(e) => setNewDoctor({...newDoctor, email: e.target.value})}
              className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
          </div>
          
          <div>
            <label htmlFor="doctor-phone" className="block text-sm font-medium text-gray-700">Phone *</label>
            <input
              type="tel"
              id="doctor-phone"
              value={newDoctor.phone}
              onChange={(e) => setNewDoctor({...newDoctor, phone: e.target.value})}
              className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              required
            />
          </div>
          
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700">Available Days</label>
            <div className="mt-2 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-7 gap-2">
              {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map((day) => (
                <label key={day} className="inline-flex items-center">
                  <input
                    type="checkbox"
                    checked={newDoctor.availableDays.includes(day)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setNewDoctor({
                          ...newDoctor, 
                          availableDays: [...newDoctor.availableDays, day]
                        });
                      } else {
                        setNewDoctor({
                          ...newDoctor, 
                          availableDays: newDoctor.availableDays.filter(d => d !== day)
                        });
                      }
                    }}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">{day}</span>
                </label>
              ))}
            </div>
          </div>
        </div>
        
        <div className="mt-4">
          <button
            onClick={createDoctor}
            className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
          >
            <UserPlus className="h-4 w-4 mr-2" />
            Add Doctor
          </button>
        </div>
      </div>
      
      {/* Doctors list */}
      <div className="px-6 py-4">
        <h3 className="text-md font-medium text-gray-700 mb-3">Doctors</h3>
        
        {doctors.length === 0 ? (
          <div className="text-center py-4">
            <p className="text-gray-500">No doctors found.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Specialty</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Available Days</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {doctors.map((doctor) => (
                  <tr key={doctor.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Dr. {doctor.name}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{doctor.specialty}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div>{doctor.phone}</div>
                      <div className="text-xs">{doctor.email}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex flex-wrap gap-1">
                        {doctor.availableDays?.map((day) => (
                          <span key={day} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800" role="status" aria-label={`Available on ${day}`}>
                            <CheckCircle className="h-3 w-3 mr-1" aria-hidden="true" />
                            <span className="sr-only">Available: </span>
                            {day.substring(0, 3)}
                          </span>
                        ))}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => setEditingDoctor(doctor)}
                          className="text-indigo-600 hover:text-indigo-900"
                          title="Edit Doctor"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => openScheduleManagement(doctor)}
                          className="text-green-600 hover:text-green-900"
                          title="Manage Schedule"
                        >
                          <Clock className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => deleteDoctor(doctor.id)}
                          className="text-red-600 hover:text-red-900"
                          title="Delete Doctor"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
      
      {/* Edit doctor modal */}
      {editingDoctor && (
        <div className="fixed inset-0 z-50 overflow-y-auto bg-gray-500 bg-opacity-75 flex items-center justify-center">
          <div className="bg-white rounded-lg overflow-hidden shadow-xl max-w-lg w-full">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Edit Doctor</h3>
            </div>
            
            <div className="px-6 py-4">
              <div className="space-y-4">
                <div>
                  <label htmlFor="edit-doctor-name" className="block text-sm font-medium text-gray-700">Name</label>
                  <input
                    type="text"
                    id="edit-doctor-name"
                    value={editingDoctor.name}
                    onChange={(e) => setEditingDoctor({...editingDoctor, name: e.target.value})}
                    className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
                
                <div>
                  <label htmlFor="edit-doctor-specialty" className="block text-sm font-medium text-gray-700">Specialty</label>
                  <input
                    type="text"
                    id="edit-doctor-specialty"
                    value={editingDoctor.specialty}
                    onChange={(e) => setEditingDoctor({...editingDoctor, specialty: e.target.value})}
                    className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
                
                <div>
                  <label htmlFor="edit-doctor-email" className="block text-sm font-medium text-gray-700">Email</label>
                  <input
                    type="email"
                    id="edit-doctor-email"
                    value={editingDoctor.email || ''}
                    onChange={(e) => setEditingDoctor({...editingDoctor, email: e.target.value})}
                    className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
                
                <div>
                  <label htmlFor="edit-doctor-phone" className="block text-sm font-medium text-gray-700">Phone</label>
                  <input
                    type="tel"
                    id="edit-doctor-phone"
                    value={editingDoctor.phone}
                    onChange={(e) => setEditingDoctor({...editingDoctor, phone: e.target.value})}
                    className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700">Available Days</label>
                  <div className="mt-2 grid grid-cols-2 sm:grid-cols-4 gap-2">
                    {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map((day) => (
                      <label key={day} className="inline-flex items-center">
                        <input
                          type="checkbox"
                          checked={editingDoctor.availableDays?.includes(day) || false}
                          onChange={(e) => {
                            const availableDays = editingDoctor.availableDays || [];
                            if (e.target.checked) {
                              setEditingDoctor({
                                ...editingDoctor, 
                                availableDays: [...availableDays, day]
                              });
                            } else {
                              setEditingDoctor({
                                ...editingDoctor, 
                                availableDays: availableDays.filter(d => d !== day)
                              });
                            }
                          }}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700">{day}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            </div>
            
            <div className="px-6 py-4 bg-gray-50 flex justify-end space-x-3">
              <button
                onClick={() => setEditingDoctor(null)}
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </button>
              <button
                onClick={updateDoctor}
                className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
              >
                Save Changes
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Schedule Management Modal */}
      {managingSchedule && (
        <div className="fixed inset-0 z-50 overflow-y-auto bg-gray-500 bg-opacity-75 flex items-center justify-center">
          <div className="bg-white rounded-lg overflow-hidden shadow-xl max-w-4xl w-full mx-4">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                Manage Schedule - Dr. {managingSchedule.name}
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                Configure weekly working hours and time slots. Changes will be automatically synced to the voice agent.
              </p>
            </div>

            <div className="px-6 py-4 max-h-96 overflow-y-auto">
              <div className="space-y-6">
                {['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].map((day) => (
                  <div key={day} className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="text-md font-medium text-gray-900 capitalize">{day}</h4>
                      <button
                        onClick={() => addTimeSlot(day)}
                        className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-white bg-teal-600 hover:bg-teal-700"
                      >
                        Add Time Slot
                      </button>
                    </div>

                    <div className="space-y-2">
                      {doctorSchedule[day]?.length === 0 ? (
                        <p className="text-sm text-gray-500 italic">No time slots configured</p>
                      ) : (
                        doctorSchedule[day]?.map((timeSlot, index) => (
                          <div key={index} className="flex items-center space-x-2">
                            <input
                              type="text"
                              value={timeSlot}
                              onChange={(e) => updateTimeSlot(day, index, e.target.value)}
                              placeholder="09:00-17:00"
                              className="flex-1 p-2 border border-gray-300 rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 text-sm"
                            />
                            <button
                              onClick={() => removeTimeSlot(day, index)}
                              className="text-red-600 hover:text-red-900"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </div>
                        ))
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="px-6 py-4 bg-gray-50 flex justify-end space-x-3">
              <button
                onClick={() => setManagingSchedule(null)}
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </button>
              <button
                onClick={saveDoctorSchedule}
                disabled={loading}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 disabled:opacity-50"
              >
                <Save className="h-4 w-4 mr-2" />
                {loading ? 'Saving...' : 'Save Schedule'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
