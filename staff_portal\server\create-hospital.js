#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to create a hospital for the Hospital Staff Portal
 * Usage: node create-hospital.js --id hospital_id --name "Hospital Name" --address "Address" --phone "+123456789" --email "<EMAIL>"
 */


import { Command } from 'commander';
const program = new Command();
import { v4 as uuidv4 } from 'uuid';
import { db } from '../lib/firebase.js';
// Add serverTimestamp to your imports
import { collection, doc, getDoc, setDoc, serverTimestamp } from 'firebase/firestore';
import { pool } from './db.js';

// Configure command line argument parsing
program
  .option('--id <id>', 'Hospital ID (will be generated if not provided)')
  .requiredOption('--name <name>', 'Hospital name')
  .option('--address <address>', 'Hospital address', 'To be updated')
  .option('--phone <phone>', 'Hospital phone number', 'To be updated')
  .option('--email <email>', 'Hospital email address', 'To be updated')
  .option('--db-only', 'Create only in PostgreSQL, not in Firebase', false)
  .option('--firebase-only', 'Create only in Firebase, not in PostgreSQL', false)
  .parse(process.argv);

const options = program.opts();

// Function to create hospital
async function createHospital(hospitalData) {
  try {
    console.log('Creating hospital...');
    
    // Generate ID if not provided
    const hospitalId = hospitalData.id || `hospital_${uuidv4().replace(/-/g, '')}`;
    
    // Create hospital in Firestore (unless db-only is specified)
    if (!options.dbOnly) {
      // Use v9 syntax instead of db.collection().doc()
      const hospitalDocRef = doc(db, 'hospitals', hospitalId);
      
      // Check if hospital already exists
      const hospital = await getDoc(hospitalDocRef);
      
      if (hospital.exists()) {
        console.log(`Hospital ${hospitalId} already exists in Firestore, updating...`);
      }
      
      // Create/update hospital in Firestore
      await setDoc(hospitalDocRef, {
        id: hospitalId,
        name: hospitalData.name,
        address: hospitalData.address,
        phone: hospitalData.phone,
        email: hospitalData.email,
        settings: hospitalData.settings || {},
        // Use serverTimestamp() instead of new Date()
        created_at: serverTimestamp(),
        updated_at: serverTimestamp()
      }, { merge: true });
      
      console.log(`Created/updated hospital in Firestore with ID: ${hospitalId}`);
    }
    
    // Create hospital in PostgreSQL (unless firebase-only is specified)
    if (!options.firebaseOnly) {
      // Check if hospitals table exists
      const tableExists = await checkTable('hospitals');
      if (!tableExists) {
        console.error('PostgreSQL hospitals table does not exist. Run database initialization script first:');
        console.error('node scripts/db_init.js');
        process.exit(1);
      }
      
      // Create/update hospital in PostgreSQL
      await pool.query(`
        INSERT INTO hospitals (id, name, address, phone, email, settings)
        VALUES ($1, $2, $3, $4, $5, $6)
        ON CONFLICT (id) DO UPDATE
        SET name = $2, address = $3, phone = $4, email = $5, settings = $6, updated_at = NOW()
      `, [
        hospitalId,
        hospitalData.name,
        hospitalData.address,
        hospitalData.phone,
        hospitalData.email,
        JSON.stringify(hospitalData.settings || {})
      ]);
      
      console.log(`Created/updated hospital in PostgreSQL with ID: ${hospitalId}`);
    }
    
    console.log(`
==========================================
Hospital created successfully!
==========================================
ID: ${hospitalId}
Name: ${hospitalData.name}
Address: ${hospitalData.address}
Phone: ${hospitalData.phone}
Email: ${hospitalData.email}
==========================================
    `);
    
    return hospitalId;
  } catch (error) {
    console.error('Error creating hospital:', error);
    process.exit(1);
  }
}

// Function to check if a table exists
async function checkTable(tableName) {
  try {
    const result = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public'
        AND table_name = $1
      );
    `, [tableName]);
    
    return result.rows[0].exists;
  } catch (error) {
    console.error(`Error checking if table ${tableName} exists:`, error);
    return false;
  }
}

// Main function
async function main() {
  try {
    // Create hospital
    const hospitalData = {
      id: options.id,
      name: options.name,
      address: options.address,
      phone: options.phone,
      email: options.email,
      settings: {
        working_hours: {
          start: '08:00',
          end: '18:00'
        },
        appointment_duration: 30, // minutes
      }
    };
    
    await createHospital(hospitalData);
    
    // Exit successfully
    process.exit(0);
  } catch (error) {
    console.error('Error in main function:', error);
    process.exit(1);
  }
}

// Run the script
main();