# Shared Redis Implementation Requirements
# Consolidated dependencies for voice_agent, whatsapp_agent, and staff_portal Redis operations

# Core Redis dependencies - Updated for Python 3.13 compatibility
redis==5.2.1
redis[hiredis]==5.2.1

# Async Redis support
aioredis>=2.0.0

# IndicBERT and multilingual model dependencies - Production versions
transformers==4.52.4  # IndicBERT and multilingual transformer support
torch==2.7.1  # ML framework for IndicBERT
sentence-transformers==4.1.0  # Semantic similarity and embeddings
sentencepiece==0.2.0  # Required for IndicBERT tokenization

# IndicBERT support - AI4Bharat model for Indian languages
indic-nlp-library==0.92  # For Indian language text normalization and processing

# Scientific computing - Updated for Python 3.13 compatibility
numpy==2.2.5  # Core numerical operations for embeddings
scipy==1.15.3  # Advanced scientific computing for similarity calculations

# Performance and monitoring
psutil==7.0.0  # System performance monitoring for cache optimization

# Optional: GPU support for faster IndicBERT processing
# torch-audio==2.7.1  # Uncomment if using GPU
# torchaudio==2.7.1   # Uncomment if using GPU

# Development and testing dependencies
pytest==8.4.0
pytest-asyncio==1.0.0
pytest-redis>=3.0.0

# Environment management
python-dotenv==1.1.0  # For Redis configuration management

# Note: This file contains all dependencies for:
# - Voice Agent semantic processing and IndicBERT integration
# - WhatsApp Agent multilingual caching
# - Staff Portal Redis operations
# - Shared cache monitoring and optimization
#
# Install with: pip install -r shared/redis/requirements.txt
