{"language": "en", "language_name": "English", "doctor_name_queries": ["when does Dr. {doctor_name} come", "what time does Dr. {doctor_name} arrive", "is Dr. {doctor_name} available", "when can I see Dr. {doctor_name}", "Dr. {doctor_name} timings", "when does {doctor_name} see patients", "Dr. {doctor_name} schedule", "Dr. {doctor_name} consultation hours", "when is Dr. {doctor_name} in the hospital", "Dr. {doctor_name} appointment availability"], "doctor_specialty_queries": ["when is the {specialty} available", "what time does the {specialty} come", "is the {specialty} doctor available", "when can I see a {specialty}", "{specialty} doctor timings", "schedule of {specialty} doctor", "{specialty} availability", "{specialty} specialist timings", "when does the {specialty} specialist come", "consultation hours for {specialty}"], "test_name_queries": ["what is the price of {test_name}", "how much does {test_name} cost", "what is the fee for {test_name}", "{test_name} price", "{test_name} cost", "how much for {test_name}", "charges for {test_name}", "what does {test_name} cost", "price of {test_name} test", "cost of {test_name} examination"], "test_general_queries": ["blood test price", "test costs", "examination fees", "lab test charges", "diagnostic costs", "medical test prices", "pathology charges", "laboratory test fees", "radiology test costs", "imaging test prices"], "common_queries": {"greeting": ["hello", "hi", "good morning", "good afternoon", "good evening", "hey there", "greetings", "hello there"], "help": ["help me", "I need help", "can you help", "what can you do", "how can you help", "assist me", "I need assistance", "can you assist"], "appointment": ["book appointment", "schedule appointment", "make appointment", "appointment booking", "when can I get appointment", "fix appointment", "reserve appointment", "set up appointment"], "hospital_info": ["hospital timings", "visiting hours", "hospital address", "contact number", "hospital location", "emergency contact"]}, "specialties_english": {"Cardiology": "Cardiologist", "Neurology": "Neurologist", "Orthopedics": "Orthopedic Doctor", "Pediatrics": "Pediatrician", "Gynecology": "Gynecologist", "Dermatology": "Dermatologist", "ENT": "ENT Specialist", "Ophthalmology": "Eye Doctor", "General Medicine": "General Physician", "Surgery": "Surgeon", "Psychiatry": "Psychiatrist", "Radiology": "Radiologist", "Pathology": "Pathologist", "Anesthesiology": "Anesthesiologist", "Emergency Medicine": "Emergency Doctor"}, "tests_english": {"Blood Test": "Blood Test", "X-Ray": "X-Ray", "CT Scan": "CT Scan", "MRI": "MRI Scan", "Ultrasound": "Ultrasound", "ECG": "ECG", "Urine Test": "Urine Test", "Endoscopy": "Endoscopy", "Biopsy": "Biopsy", "Mammography": "Mammography"}, "response_templates": {"doctor_schedule": "Dr. {doctor_name} is available {schedule}. Consultation fee is {fee}.", "test_price": "The cost of {test_name} is {price}. Duration: {duration}.", "appointment_booking": "To book an appointment, please call our reception or visit the hospital.", "general_help": "I can help you with doctor schedules, test prices, and appointment information."}, "metadata": {"version": "2.0", "description": "Enhanced English query templates for shared Redis voice agent", "total_templates": 78, "last_updated": "2024-01-15", "compatibility": "shared_redis_v1.0"}}