/**
 * Logger module for consistent logging across the staff portal application
 * This module provides structured logging with proper log levels and formatting
 */

const LOG_LEVELS = {
  ERROR: 0,
  WARN: 1,
  INFO: 2,
  DEBUG: 3,
};

// Set default log level based on environment
const DEFAULT_LOG_LEVEL = process.env.NODE_ENV === 'production' 
  ? LOG_LEVELS.INFO 
  : LOG_LEVELS.DEBUG;

// Allow overriding log level with environment variable
const CURRENT_LOG_LEVEL = process.env.LOG_LEVEL
  ? LOG_LEVELS[process.env.LOG_LEVEL.toUpperCase()] || DEFAULT_LOG_LEVEL
  : DEFAULT_LOG_LEVEL;

/**
 * Format log timestamp
 * @returns {string} Formatted timestamp
 */
function getTimestamp() {
  return new Date().toISOString();
}

/**
 * Format log message with metadata
 * @param {string} level - Log level 
 * @param {string} message - Main log message
 * @param {object} metadata - Additional structured data
 * @returns {string} Formatted log message
 */
function formatLogMessage(level, message, metadata = {}) {
  const timestamp = getTimestamp();
  
  // Basic log message with timestamp and level
  let logMessage = `[${timestamp}] [${level}] ${message}`;
  
  // Add metadata if present
  if (Object.keys(metadata).length > 0) {
    try {
      const metadataString = JSON.stringify(metadata);
      logMessage += ` ${metadataString}`;
    } catch (error) {
      logMessage += ` [Error serializing metadata: ${error.message}]`;
    }
  }
  
  return logMessage;
}

/**
 * Log message at specified level
 * @param {string} level - Log level 
 * @param {string} message - Main log message
 * @param {object} metadata - Additional structured data
 */
function log(level, message, metadata = {}) {
  // Check if we should log at this level
  const levelValue = LOG_LEVELS[level.toUpperCase()];
  
  if (levelValue <= CURRENT_LOG_LEVEL) {
    const formattedMessage = formatLogMessage(level, message, metadata);
    
    // Use appropriate console method based on level
    switch (level.toUpperCase()) {
      case 'ERROR':
        console.error(formattedMessage);
        break;
      case 'WARN':
        console.warn(formattedMessage);
        break;
      case 'DEBUG':
        console.debug(formattedMessage);
        break;
      case 'INFO':
      default:
        console.info(formattedMessage);
        break;
    }
    
    // In a real production environment, we would also send logs to a service
    // like CloudWatch, Datadog, or another logging service
  }
}

// Define logger interface with methods for each log level
const logger = {
  error: (message, metadata = {}) => log('ERROR', message, metadata),
  warn: (message, metadata = {}) => log('WARN', message, metadata),
  info: (message, metadata = {}) => log('INFO', message, metadata),
  debug: (message, metadata = {}) => log('DEBUG', message, metadata),
  
  // Add a method to log requests in a consistent format
  request: (req, status, responseTime) => {
    const method = req.method;
    const url = req.url;
    const ip = req.headers['x-forwarded-for'] || req.connection.remoteAddress;
    const userAgent = req.headers['user-agent'];
    
    // Create structured metadata for the request
    const metadata = {
      method,
      url,
      status,
      responseTime: `${responseTime}ms`,
      ip,
      userAgent,
    };
    
    // Add user ID if authenticated
    if (req.user && req.user.id) {
      metadata.userId = req.user.id;
      metadata.hospitalId = req.user.hospital_id;
    }
    
    // Log at appropriate level based on status code
    if (status >= 500) {
      logger.error(`Request ${method} ${url} failed with ${status}`, metadata);
    } else if (status >= 400) {
      logger.warn(`Request ${method} ${url} failed with ${status}`, metadata);
    } else {
      logger.info(`Request ${method} ${url} completed with ${status}`, metadata);
    }
  },
  
  // Create a child logger with additional context
  child: (context) => {
    return {
      error: (message, metadata = {}) => log('ERROR', message, { ...context, ...metadata }),
      warn: (message, metadata = {}) => log('WARN', message, { ...context, ...metadata }),
      info: (message, metadata = {}) => log('INFO', message, { ...context, ...metadata }),
      debug: (message, metadata = {}) => log('DEBUG', message, { ...context, ...metadata }),
    };
  }
};

// Request logging middleware for Next.js API routes
const requestLogger = (handler) => {
  return async (req, res) => {
    const start = Date.now();
    
    // Create a custom 'end' function to intercept the response
    const originalEnd = res.end;
    res.end = function() {
      const responseTime = Date.now() - start;
      logger.request(req, res.statusCode, responseTime);
      
      // Call the original end function
      originalEnd.apply(res, arguments);
    };
    
    // Call the original handler
    return handler(req, res);
  };
};

// Export logger and middleware using ESM syntax
export { logger, requestLogger, LOG_LEVELS };