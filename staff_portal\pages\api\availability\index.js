import { withRole } from '../../../lib/auth';
import {
  getDoctorsAndTestsByDate,
  updateMultipleAvailability
} from '../../../lib/firebase';
import redisClient from '../../../lib/redis_client';
import { logger } from '../../../lib/logger';

/**
 * Validate date format and ensure it's a valid date
 */
function validateDate(dateString) {
  if (!dateString) {
    return { valid: false, error: 'Date parameter is required' };
  }

  const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
  if (!dateRegex.test(dateString)) {
    return { valid: false, error: 'Invalid date format. Use YYYY-MM-DD' };
  }

  // Check if it's a valid date
  const date = new Date(dateString);
  if (isNaN(date.getTime())) {
    return { valid: false, error: 'Invalid date value' };
  }

  // Ensure the date string matches what was parsed (handles cases like 2023-13-01)
  const [year, month, day] = dateString.split('-').map(Number);
  if (date.getFullYear() !== year || date.getMonth() !== month - 1 || date.getDate() !== day) {
    return { valid: false, error: 'Invalid date value' };
  }

  return { valid: true };
}

/**
 * API endpoint for managing doctor and test availability
 * Accessible by admin and receptionist roles
 */
export default withRole(async (req, res) => {
  const hospitalId = req.user?.hospital_id;
  
  if (!hospitalId) {
    return res.status(401).json({ 
      success: false, 
      message: 'Hospital ID missing from user context' 
    });
  }

  try {
    switch (req.method) {
      case 'GET':
        return await handleGetAvailability(req, res, hospitalId);
      case 'PUT':
        return await handleUpdateAvailability(req, res, hospitalId);
      default:
        res.setHeader('Allow', ['GET', 'PUT']);
        return res.status(405).json({ 
          success: false, 
          message: `Method ${req.method} Not Allowed` 
        });
    }
  } catch (error) {
    logger.error('[API_AVAILABILITY] Unexpected error:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
}, ['admin', 'receptionist']); // Allow both admin and receptionist roles

/**
 * Handle GET request - fetch doctors and tests availability for a specific date
 */
async function handleGetAvailability(req, res, hospitalId) {
  try {
    const { date } = req.query;

    const dateValidation = validateDate(date);
    if (!dateValidation.valid) {
      return res.status(400).json({
        success: false,
        message: dateValidation.error
      });
    }
    
    logger.info(`[API_AVAILABILITY] Fetching availability for hospital ${hospitalId} on ${date}`);
    
    const result = await getDoctorsAndTestsByDate(hospitalId, date);
    
    if (result.success) {
      // Add Redis connectivity status
      try {
        const isRedisConnected = await redisClient.isRedisConnected();
        result.redisConnected = isRedisConnected;
      } catch (redisError) {
        logger.warn('[API_AVAILABILITY] Redis connectivity check failed:', redisError);
        result.redisConnected = false;
      }
      
      return res.status(200).json(result);
    } else {
      return res.status(500).json(result);
    }
  } catch (error) {
    logger.error('[API_AVAILABILITY] Error in GET handler:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to fetch availability data',
      error: error.message 
    });
  }
}

/**
 * Handle PUT request - update availability for multiple doctors and tests
 */
async function handleUpdateAvailability(req, res, hospitalId) {
  try {
    const { date, updates } = req.body;

    if (!updates || !Array.isArray(updates)) {
      return res.status(400).json({
        success: false,
        message: 'Updates array is required'
      });
    }

    const dateValidation = validateDate(date);
    if (!dateValidation.valid) {
      return res.status(400).json({
        success: false,
        message: dateValidation.error
      });
    }
    
    logger.info(`[API_AVAILABILITY] Updating availability for hospital ${hospitalId} on ${date} (${updates.length} items)`);
    
    // Update availability in Firebase
    const updateResult = await updateMultipleAvailability(hospitalId, updates, date);
    
    if (!updateResult.success) {
      return res.status(400).json(updateResult);
    }
    
    // Trigger Redis sync with voice agent
    let syncStatus = { status: 'pending', message: 'Initiating availability sync...' };
    
    try {
      logger.info(`[API_AVAILABILITY] Triggering Redis availability sync for hospital ${hospitalId}`);
      const syncResult = await redisClient.triggerAvailabilityRefresh(hospitalId, date);
      
      if (syncResult.success) {
        syncStatus = { 
          status: 'success', 
          message: 'Voice agent availability cache updated successfully',
          details: syncResult.data 
        };
        logger.info('[API_AVAILABILITY] Redis availability sync completed successfully');
      } else {
        syncStatus = { 
          status: 'error', 
          message: syncResult.error || 'Failed to sync availability with voice agent',
          details: syncResult 
        };
        logger.warn('[API_AVAILABILITY] Redis availability sync failed:', syncResult.error);
      }
    } catch (syncError) {
      syncStatus = { 
        status: 'error', 
        message: 'Redis availability sync error: ' + syncError.message 
      };
      logger.error('[API_AVAILABILITY] Redis availability sync error:', syncError);
    }
    
    return res.status(200).json({ 
      success: true, 
      message: 'Availability updated successfully',
      data: {
        date,
        hospitalId,
        updateResults: updateResult.data,
        syncStatus,
        updatedAt: new Date().toISOString()
      }
    });
    
  } catch (error) {
    logger.error('[API_AVAILABILITY] Error in PUT handler:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to update availability',
      error: error.message 
    });
  }
}
