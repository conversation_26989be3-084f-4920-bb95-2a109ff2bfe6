# Voice Agent Startup Guide

## Quick Start

**Single Command Startup** - This starts both FastAPI (port 8000) and WebSocket (port 8765) servers:

```bash
python -m uvicorn voice_agent.main:app --host 0.0.0.0 --port 8000
```

That's it! Both servers will start automatically through FastAPI lifespan management.

## Prerequisites

### 1. Environment Setup

Ensure you have the required environment variables configured:

```bash
# Core Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
FIREBASE_PROJECT_ID=your-project-id

# WebSocket Configuration
WEBSOCKET_HOST=0.0.0.0
WEBSOCKET_PORT=8765
MAX_WEBSOCKET_CONNECTIONS=1000

# Database Configuration
# Hospital-specific database connections are loaded from Firebase
```

### 2. Dependencies Installation

```bash
# Install Python dependencies
pip install -r requirements.txt

# Verify key dependencies
pip list | grep -E "(uvicorn|websockets|fastapi|redis|firebase)"
```

### 3. External Services

Ensure these services are running and accessible:

- **Redis Server**: For caching and state management
- **Firebase**: For hospital configurations and booking limits
- **PostgreSQL**: Hospital-specific databases (configured per hospital)

## Detailed Startup Process

### Step 1: Pre-startup Checks

```bash
# Check Redis connectivity
redis-cli ping
# Expected output: PONG

# Check if ports are available
netstat -tulpn | grep -E ":8000|:8765"
# Should show no existing processes on these ports

# Verify working directory
pwd
# Should be in the voice agent project root
```

### Step 2: Start the Application

```bash
# Standard startup
python -m uvicorn voice_agent.main:app --host 0.0.0.0 --port 8000

# With reload for development
python -m uvicorn voice_agent.main:app --host 0.0.0.0 --port 8000 --reload

# With log level specification
python -m uvicorn voice_agent.main:app --host 0.0.0.0 --port 8000 --log-level info
```

### Step 3: Verify Startup Success

You should see output similar to:

```
INFO:     Started server process [12345]
INFO:     Waiting for application startup.
INFO:     ✅ Redis connection established
INFO:     ✅ Firebase initialized successfully
INFO:     ✅ WebSocket server started on 0.0.0.0:8765
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
```

## Verification Steps

### 1. Health Checks

```bash
# Check overall system health
curl http://localhost:8000/health
# Expected: {"status": "healthy", "timestamp": "..."}

# Check WebSocket server health
curl http://localhost:8000/health/websocket
# Expected: {"websocket_status": "running", "active_connections": 0}
```

### 2. WebSocket Server Status

```bash
# Check WebSocket server status
curl http://localhost:8000/websocket/status
# Expected: {"status": "running", "port": 8765, "connections": {...}}

# Check WebSocket metrics
curl http://localhost:8000/websocket/metrics/summary
# Expected: Performance metrics and connection statistics
```

### 3. Test WebSocket Connectivity

```bash
# Install wscat for WebSocket testing (if not already installed)
npm install -g wscat

# Test WebSocket connection
wscat -c ws://localhost:8765/ws/jambonz/test_hospital
# Should connect successfully and show connection established
```

### 4. Verify Hospital Configuration Loading

```bash
# Test hospital configuration endpoint (replace hospital_1 with actual ID)
curl http://localhost:8000/jambonz/application-url/hospital_1
# Expected: Hospital-specific configuration data
```

## Production Startup

### Using systemd (Recommended)

Create a systemd service file:

```bash
sudo nano /etc/systemd/system/voice-agent.service
```

```ini
[Unit]
Description=Voice Agent Service
After=network.target redis.service

[Service]
Type=exec
User=voiceagent
Group=voiceagent
WorkingDirectory=/path/to/voice-agent
Environment=PATH=/path/to/venv/bin
ExecStart=/path/to/venv/bin/python -m uvicorn voice_agent.main:app --host 0.0.0.0 --port 8000
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

```bash
# Enable and start the service
sudo systemctl enable voice-agent.service
sudo systemctl start voice-agent.service

# Check service status
sudo systemctl status voice-agent.service
```

### Using Docker

```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8000 8765

CMD ["python", "-m", "uvicorn", "voice_agent.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

```bash
# Build and run
docker build -t voice-agent .
docker run -p 8000:8000 -p 8765:8765 voice-agent
```

## Troubleshooting

### Common Startup Issues

#### 1. Port Already in Use

```bash
# Error: "Address already in use"
# Solution: Find and kill the process using the port
sudo fuser -k 8000/tcp
sudo fuser -k 8765/tcp

# Or find the process ID
lsof -ti:8000
lsof -ti:8765
```

#### 2. Redis Connection Failed

```bash
# Error: "Redis connection failed"
# Check Redis status
sudo systemctl status redis
redis-cli ping

# Start Redis if not running
sudo systemctl start redis
```

#### 3. Firebase Authentication Error

```bash
# Error: "Firebase authentication failed"
# Check environment variables
echo $FIREBASE_PROJECT_ID
echo $GOOGLE_APPLICATION_CREDENTIALS

# Verify Firebase credentials file exists
ls -la $GOOGLE_APPLICATION_CREDENTIALS
```

#### 4. WebSocket Server Failed to Start

```bash
# Check logs for WebSocket startup errors
# Look for specific error messages in the startup output

# Common causes:
# - Port 8765 already in use
# - Insufficient permissions
# - Network interface binding issues
```

### Startup Logs Analysis

#### Successful Startup Indicators

```
✅ Redis connection established
✅ Firebase initialized successfully  
✅ WebSocket server started on 0.0.0.0:8765
✅ Application startup complete
```

#### Warning Signs

```
⚠️  Redis connection slow (>1000ms)
⚠️  Firebase initialization timeout
⚠️  WebSocket server binding issues
⚠️  High memory usage during startup
```

#### Error Indicators

```
❌ Redis connection failed
❌ Firebase authentication error
❌ WebSocket server failed to start
❌ Database connection timeout
```

## Development vs Production

### Development Mode

```bash
# With auto-reload for code changes
python -m uvicorn voice_agent.main:app --host 0.0.0.0 --port 8000 --reload

# With debug logging
python -m uvicorn voice_agent.main:app --host 0.0.0.0 --port 8000 --log-level debug
```

### Production Mode

```bash
# Standard production startup
python -m uvicorn voice_agent.main:app --host 0.0.0.0 --port 8000

# With multiple workers (if needed)
python -m uvicorn voice_agent.main:app --host 0.0.0.0 --port 8000 --workers 4
```

## Monitoring During Startup

### Real-time Monitoring

```bash
# Monitor startup logs
tail -f logs/voice_agent.log

# Monitor system resources
htop

# Monitor network connections
watch -n 2 'netstat -tulpn | grep -E ":8000|:8765"'
```

### Post-Startup Monitoring

```bash
# Continuous health monitoring
watch -n 5 'curl -s http://localhost:8000/health | jq'

# WebSocket connection monitoring
watch -n 10 'curl -s http://localhost:8000/websocket/status | jq .active_connections'
```

## Shutdown Process

### Graceful Shutdown

```bash
# Send SIGTERM to allow graceful shutdown
kill -TERM <process_id>

# Or use Ctrl+C if running in foreground
# The application will:
# 1. Stop accepting new connections
# 2. Complete ongoing calls
# 3. Close WebSocket connections gracefully
# 4. Clean up resources
```

### Force Shutdown (if needed)

```bash
# Force kill if graceful shutdown fails
kill -KILL <process_id>

# Clean up any remaining connections
sudo fuser -k 8000/tcp
sudo fuser -k 8765/tcp
```

## Quick Reference Commands

```bash
# Start application
python -m uvicorn voice_agent.main:app --host 0.0.0.0 --port 8000

# Health check
curl http://localhost:8000/health

# WebSocket status
curl http://localhost:8000/websocket/status

# Test WebSocket connection
wscat -c ws://localhost:8765/ws/jambonz/test_hospital

# View logs
tail -f logs/voice_agent.log

# Stop application
# Ctrl+C (if running in foreground) or kill -TERM <pid>
```

## Next Steps After Startup

1. **Configure Jambonz**: Update Jambonz applications to use `wss://your-domain:8765/ws/jambonz/{hospital_id}`
2. **Test Voice Calls**: Make test calls to verify end-to-end functionality
3. **Monitor Performance**: Set up monitoring dashboards for ongoing operations
4. **Configure Alerts**: Set up alerts for system health and performance metrics

For detailed port configuration information, see [PORT_CONFIGURATION_GUIDE.md](./PORT_CONFIGURATION_GUIDE.md).
