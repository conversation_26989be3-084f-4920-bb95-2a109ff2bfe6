"""
Semantic Processing Components for Shared Redis Implementation

Provides semantic caching and processing capabilities for:
- Voice Agent: IndicBERT-powered semantic caching for Indian languages
- WhatsApp Agent: Multilingual support for English and regional languages
"""

from .embedding_manager import EmbeddingManager, get_embedding_manager
from .indic_bert_cache import IndicBertCache
from .multilingual_cache import MultilingualCache

__all__ = [
    "EmbeddingManager",
    "get_embedding_manager",
    "IndicBertCache", 
    "MultilingualCache"
]
