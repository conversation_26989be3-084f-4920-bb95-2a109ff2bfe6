import React, { useEffect } from 'react';
import { AlertCircle, CheckCircle, AlertTriangle, Info, X } from 'react-feather';

/**
 * Enhanced Toast component for error notifications and other message types
 * Supports success, error, warning, and info notifications
 */
export function ErrorToast({ 
  id,
  type = 'info', 
  title, 
  message, 
  duration = 5000,
  dismissible = true,
  onClose, 
  onClick 
}) {
  useEffect(() => {
    if (duration > 0 && onClose) {
      const timer = setTimeout(() => {
        onClose(id);
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [duration, onClose, id]);

  // Get icon based on notification type
  const getIcon = () => {
    const iconProps = { className: "w-5 h-5" };
    
    switch (type) {
      case 'success':
        return <CheckCircle {...iconProps} className="w-5 h-5 text-green-600" />;
      case 'error':
        return <AlertCircle {...iconProps} className="w-5 h-5 text-red-600" />;
      case 'warning':
        return <AlertTriangle {...iconProps} className="w-5 h-5 text-yellow-600" />;
      case 'info':
      default:
        return <Info {...iconProps} className="w-5 h-5 text-blue-600" />;
    }
  };

  // Get styling based on notification type
  const getStyles = () => {
    switch (type) {
      case 'success':
        return {
          border: 'border-l-green-500',
          bg: 'bg-green-50',
          titleColor: 'text-green-900',
          messageColor: 'text-green-700',
        };
      case 'error':
        return {
          border: 'border-l-red-500',
          bg: 'bg-red-50',
          titleColor: 'text-red-900',
          messageColor: 'text-red-700',
        };
      case 'warning':
        return {
          border: 'border-l-yellow-500',
          bg: 'bg-yellow-50',
          titleColor: 'text-yellow-900',
          messageColor: 'text-yellow-700',
        };
      case 'info':
      default:
        return {
          border: 'border-l-blue-500',
          bg: 'bg-blue-50',
          titleColor: 'text-blue-900',
          messageColor: 'text-blue-700',
        };
    }
  };

  const styles = getStyles();

  return (
    <div 
      className={`fixed bottom-4 right-4 max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto border-l-4 ${styles.border} animate-slide-in-right z-50`}
      role="alert"
      onClick={onClick}
    >
      <div className={`p-4 ${styles.bg} rounded-lg`}>
        <div className="flex items-start">
          <div className="flex-shrink-0">
            {getIcon()}
          </div>
          <div className="ml-3 w-0 flex-1">
            {title && (
              <div className={`text-sm font-medium ${styles.titleColor}`}>
                {title}
              </div>
            )}
            {message && (
              <p className={`${title ? 'mt-1' : ''} text-sm ${styles.messageColor}`}>
                {message}
              </p>
            )}
          </div>
          {dismissible && (
            <div className="ml-4 flex-shrink-0 flex">
              <button
                className="bg-transparent rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                onClick={(e) => {
                  e.stopPropagation();
                  if (onClose) onClose(id);
                }}
              >
                <span className="sr-only">Close</span>
                <X className="h-4 w-4" />
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default ErrorToast;
