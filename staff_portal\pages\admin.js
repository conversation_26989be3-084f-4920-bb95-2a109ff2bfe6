import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import {
  Users,
  Settings,
  ArrowLeft,
  Bell,
  AlertCircle,
  Calendar,
  Clock,
  Shield,
  User
} from 'react-feather';
import Layout from '../components/Layout';
import { useNotifications } from '../hooks/useNotifications';
import { NotificationContainer } from '../components/notifications/NotificationContainer';
import { useStaffOperations } from '../hooks/useStaffOperations';
import { useDoctorOperations } from '../hooks/useDoctorOperations';

// Import the new components
import StaffManagement from '../components/admin/StaffManagement';
import DoctorManagement from '../components/admin/DoctorManagement';
import SchedulingConfig from '../components/admin/SchedulingConfig';
import HospitalSettings from '../components/admin/HospitalSettings';
import VoiceAgentTesting from '../components/admin/VoiceAgentTesting';
import BookingLimits from '../components/admin/BookingLimits';







export default function AdminPage() {
  const router = useRouter();
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Notification system
  const {
    notifications,
    removeNotification,
    showSuccess,
    showError,
    showWarning
  } = useNotifications();

  const [tab, setTab] = useState('staff'); // 'staff', 'doctors', 'booking-limits', 'scheduling-config', 'settings', 'testing'
  const [staff, setStaff] = useState([]);
  const [doctors, setDoctors] = useState([]);
  const [hospitalSettings, setHospitalSettings] = useState({
    name: '',
    address: '',
    phone: '',
    email: '',
    enableSmsReminders: true,
    enableEmailReminders: false,
    reminderHoursBeforeAppointment: 24,
    enableTestResultNotifications: true
  });

  // Scheduling configuration state
  const [schedulingConfig, setSchedulingConfig] = useState({
    appointment_duration_minutes: 30,
    working_hours: {
      start: "09:00",
      end: "17:00",
      lunch_break: {
        start: "12:00",
        end: "13:00"
      }
    },
    time_slot_interval_minutes: 30,
    max_slots_per_day: 16,
    advance_booking_days: 30,
    same_day_booking_cutoff_hours: 2,
    test_booking: {
      time_slot_interval_minutes: 30,
      same_day_booking_cutoff_hours: 1,
      max_slots_per_day: 32
    }
  });
  






  // Fetch functions for CRUD hooks
  const fetchStaffList = async (hospitalId) => {
    try {
      setLoading(true);
      const res = await fetch(`/api/staff?hospital_id=${hospitalId}`);
      const data = await res.json();

      if (data.success) {
        setStaff(data.data);
      } else {
        console.error('Failed to fetch staff:', data.message);
      }
    } catch (error) {
      console.error('Fetch staff error:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchDoctorsList = async (hospitalId) => {
    try {
      setLoading(true);
      const res = await fetch(`/api/doctors?hospital_id=${hospitalId}`);
      const data = await res.json();

      if (data.success) {
        setDoctors(data.data);
      } else {
        console.error('Failed to fetch doctors:', data.message);
      }
    } catch (error) {
      console.error('Fetch doctors error:', error);
    } finally {
      setLoading(false);
    }
  };

  // CRUD operation hooks
  const staffOperations = useStaffOperations(fetchStaffList);
  const doctorOperations = useDoctorOperations(fetchDoctorsList);

  // Get user status on load
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const res = await fetch('/api/auth/status');
        const data = await res.json();

        if (data.success) {
          // SECURITY: Check permissions BEFORE setting user state to prevent
          // brief exposure of sensitive data to unauthorized users
          const allowedRoles = ['admin', 'receptionist'];
          if (!allowedRoles.includes(data.user.role)) {
            showError('Access Denied', 'You do not have permission to access this page');
            router.push('/dashboard');
            return;
          }

          // Only set user state after permission validation passes
          setUser(data.user);

          // Set default tab based on role
          if (data.user.role === 'receptionist') {
            setTab('booking-limits'); // Receptionists start with booking limits
          } else {
            setTab('staff'); // Admins start with staff management
          }

          // Fetch data
          fetchStaff(data.user.hospital_id);
          fetchDoctors(data.user.hospital_id);
          if (data.user.role === 'admin') {
            fetchHospitalSettings(data.user.hospital_id);
            fetchSchedulingConfig(data.user.hospital_id);
          }
        } else {
          // Redirect to login if not authenticated
          showError('Authentication Required', 'Please log in to access this page');
          router.push('/login');
        }
      } catch (error) {
        console.error('Auth check error:', error);
        showError('Authentication Error', 'Unable to verify your credentials. Please try logging in again.');
        router.push('/login');
      } finally {
        setLoading(false);
      }
    };
    
    checkAuth();
  }, [router]);

  // SECURITY: Auto-redirect users away from unauthorized tabs
  useEffect(() => {
    if (user && user.role === 'receptionist') {
      const adminOnlyTabs = ['staff', 'scheduling-config', 'settings', 'testing', 'reminders'];
      if (adminOnlyTabs.includes(tab)) {
        showWarning('Access Restricted', 'Redirecting to an authorized section');
        setTab('booking-limits');
      }
    }
  }, [user, tab, showWarning]);

  // Use the fetchStaffList function defined above
  const fetchStaff = fetchStaffList;

  // Use the fetchDoctorsList function defined above
  const fetchDoctors = fetchDoctorsList;

  // Fetch hospital settings
  const fetchHospitalSettings = async (hospitalId) => {
    try {
      setLoading(true);
      const res = await fetch(`/api/admin/settings?hospital_id=${hospitalId}`);
      const data = await res.json();

      if (data.success) {
        setHospitalSettings(data.data);
      } else {
        console.error('Failed to fetch hospital settings:', data.message);
      }
    } catch (error) {
      console.error('Fetch hospital settings error:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch scheduling configuration
  const fetchSchedulingConfig = async (hospitalId) => {
    try {
      setLoading(true);
      const res = await fetch(`/api/admin/scheduling-config?hospital_id=${hospitalId}`);
      const data = await res.json();

      if (data.success) {
        setSchedulingConfig(data.data);
      } else {
        console.error('Failed to fetch scheduling configuration:', data.message);
      }
    } catch (error) {
      console.error('Fetch scheduling configuration error:', error);
    } finally {
      setLoading(false);
    }
  };

  // Save hospital settings
  const saveHospitalSettings = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const res = await fetch('/api/admin/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          hospitalId: user.hospital_id,
          settings: hospitalSettings
        })
      });

      const data = await res.json();

      if (data.success) {
        showSuccess('Success', 'Hospital settings saved successfully');
      } else {
        showError('Save Failed', `Failed to save settings: ${data.message}`);
      }
    } catch (error) {
      console.error('Save hospital settings error:', error);
      showError('Error', 'An error occurred while saving settings');
    } finally {
      setLoading(false);
    }
  };

  // Save scheduling configuration
  const saveSchedulingConfig = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const res = await fetch('/api/admin/scheduling-config', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          hospitalId: user.hospital_id,
          config: schedulingConfig
        })
      });

      const data = await res.json();

      if (data.success) {
        showSuccess('Success', 'Scheduling configuration saved successfully. Voice agent will be updated automatically.');
      } else {
        showError('Save Failed', `Failed to save scheduling configuration: ${data.message}`);
      }
    } catch (error) {
      console.error('Save scheduling configuration error:', error);
      showError('Error', 'An error occurred while saving scheduling configuration');
    } finally {
      setLoading(false);
    }
  };









  // SECURITY: Show loading state while authentication and authorization are in progress
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-2xl font-semibold text-slate-700 flex items-center">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-teal-500 mr-3"></div>
          Verifying permissions...
        </div>
      </div>
    );
  }

  // SECURITY: Additional check to ensure user is properly authenticated and authorized
  // This prevents any rendering of sensitive content if user state is somehow set without proper validation
  if (!user || !['admin', 'receptionist'].includes(user.role)) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-2xl font-semibold text-red-600 mb-4">Access Denied</div>
          <p className="text-gray-600 mb-4">You do not have permission to access this page.</p>
          <Link href="/dashboard" className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-teal-600 hover:bg-teal-700">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Return to Dashboard
          </Link>
        </div>
      </div>
    );
  }

  return (
    <Layout title={user?.role === 'admin' ? "Admin Dashboard" : "Staff Dashboard"} user={user}>
      <div className="flex items-center space-x-4 mb-6">
        <Link href="/dashboard" className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-slate-700 bg-slate-100 hover:bg-slate-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Dashboard
        </Link>
        
      </div>
      
      {/* Tab navigation */}
      <div className="bg-white shadow-sm rounded-lg border border-slate-200 mb-6">
        <div className="px-4 sm:px-6 lg:px-8">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setTab('staff')}
              className={`${
                tab === 'staff'
                  ? 'border-teal-500 text-teal-600'
                  : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            >
              <Users className="inline-block h-5 w-5 mr-2" />
              Staff Management
            </button>
            
            <button
              onClick={() => setTab('doctors')}
              className={`${
                tab === 'doctors'
                  ? 'border-teal-500 text-teal-600'
                  : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            >
              <User className="inline-block h-5 w-5 mr-2" />
              Doctors
            </button>

            <button
              onClick={() => setTab('booking-limits')}
              className={`${
                tab === 'booking-limits'
                  ? 'border-teal-500 text-teal-600'
                  : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            >
              <Calendar className="inline-block h-5 w-5 mr-2" />
              Booking Limits
            </button>

            {/* Only show scheduling config, settings and reminders for admin users */}
            {user?.role === 'admin' && (
              <>
                <button
                  onClick={() => setTab('scheduling-config')}
                  className={`${
                    tab === 'scheduling-config'
                      ? 'border-teal-500 text-teal-600'
                      : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                >
                  <Clock className="inline-block h-5 w-5 mr-2" />
                  Scheduling Config
                </button>

                <button
                  onClick={() => setTab('settings')}
                  className={`${
                    tab === 'settings'
                      ? 'border-teal-500 text-teal-600'
                      : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                >
                  <Settings className="inline-block h-5 w-5 mr-2" />
                  Hospital Settings
                </button>

                <button
                  onClick={() => setTab('reminders')}
                  className={`${
                    tab === 'reminders'
                      ? 'border-teal-500 text-teal-600'
                      : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                >
                  <Bell className="inline-block h-5 w-5 mr-2" />
                  Reminders
                </button>

                <button
                  onClick={() => setTab('testing')}
                  className={`${
                    tab === 'testing'
                      ? 'border-teal-500 text-teal-600'
                      : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                >
                  <AlertCircle className="inline-block h-5 w-5 mr-2" />
                  Voice Agent Testing
                </button>
              </>
            )}
          </nav>
        </div>
      </div>
      
      {/* Main content */}
      <div>
        {/* Staff Management */}
        {tab === 'staff' && (
          <StaffManagement
            staff={staff}
            staffOperations={staffOperations}
            user={user}
            showError={showError}
            showSuccess={showSuccess}
          />
        )}

        {/* Doctors */}
        {tab === 'doctors' && (
          <DoctorManagement
            doctors={doctors}
            doctorOperations={doctorOperations}
            user={user}
            showError={showError}
            showSuccess={showSuccess}
          />
        )}

        {/* Booking Limits Management */}
        {tab === 'booking-limits' && (
          <BookingLimits user={user} />
        )}

        {/* Scheduling Configuration - Only for Admin */}
        {tab === 'scheduling-config' && user?.role === 'admin' && (
          <SchedulingConfig
            schedulingConfig={schedulingConfig}
            setSchedulingConfig={setSchedulingConfig}
            onSave={saveSchedulingConfig}
            loading={loading}
          />
        )}

        {/* Voice Agent Testing - Only for Admin */}
        {tab === 'testing' && user?.role === 'admin' && (
          <VoiceAgentTesting
            user={user}
            showError={showError}
            showSuccess={showSuccess}
          />
        )}

        {/* Hospital Settings - Only for Admin */}
        {tab === 'settings' && user?.role === 'admin' && (
          <HospitalSettings
            hospitalSettings={hospitalSettings}
            setHospitalSettings={setHospitalSettings}
            onSave={saveHospitalSettings}
            loading={loading}
          />
        )}

        {/* Reminders - Only for Admin */}
        {tab === 'reminders' && user?.role === 'admin' && (
          <HospitalSettings
            hospitalSettings={hospitalSettings}
            setHospitalSettings={setHospitalSettings}
            onSave={saveHospitalSettings}
            loading={loading}
          />
        )}
        {/* Security Fallback: Show unauthorized access message for invalid tab/role combinations */}
        {((tab === 'staff' || tab === 'scheduling-config' || tab === 'settings' || tab === 'testing' || tab === 'reminders') && user.role !== 'admin') && (
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="px-6 py-8 text-center">
              <div className="text-red-600 mb-4">
                <Shield className="h-12 w-12 mx-auto" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Access Restricted</h3>
              <p className="text-gray-600 mb-4">
                This section is only available to administrators. Your current role ({user.role}) does not have permission to access this feature.
              </p>
              <button
                onClick={() => setTab('booking-limits')}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-teal-600 hover:bg-teal-700"
              >
                Go to Booking Limits
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Notification Container */}
      <NotificationContainer
        notifications={notifications}
        onRemove={removeNotification}
      />
    </Layout>
  );
}