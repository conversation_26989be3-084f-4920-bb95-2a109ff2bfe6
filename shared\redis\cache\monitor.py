"""
Cache Monitoring for Shared Redis Implementation

Provides comprehensive monitoring and performance analysis for Redis caches
across voice agent, WhatsApp agent, and staff portal.
"""

import logging
import threading
import time
from dataclasses import dataclass, asdict
from typing import Dict, Any, List, Optional, Callable, Union
from datetime import datetime, timedelta
from enum import Enum
from ..base_operations import RedisOperations
from ..connection_manager import get_redis_connection

logger = logging.getLogger(__name__)


class CacheOptimizationLevel(Enum):
    """Cache optimization levels for different performance requirements."""
    CONSERVATIVE = "conservative"
    BALANCED = "balanced"
    AGGRESSIVE = "aggressive"


class AgentType(Enum):
    """Supported agent types for cache monitoring."""
    VOICE_AGENT = "voice_agent"
    WHATSAPP_AGENT = "whatsapp_agent"
    STAFF_PORTAL = "staff_portal"


@dataclass
class CachePerformanceMetrics:
    """Enhanced performance metrics for cache operations with agent support."""
    hit_rate: float
    miss_rate: float
    total_requests: int
    total_hits: int
    total_misses: int
    average_response_time: float
    memory_usage: int
    memory_usage_human: str
    connected_clients: int
    keyspace_info: Dict[str, Any]
    timestamp: datetime
    agent_type: str = "unknown"

    def to_dict(self) -> Dict[str, Any]:
        """Convert metrics to dictionary format."""
        result = asdict(self)
        result['timestamp'] = self.timestamp.isoformat()
        return result


@dataclass
class CacheOptimizationConfig:
    """Configuration for cache optimization strategies."""
    # TTL optimization
    default_ttl: int = 3600
    semantic_cache_ttl: int = 86400
    call_context_ttl: int = 600
    hospital_data_ttl: int = 7200

    # Performance thresholds
    target_hit_rate: float = 0.85
    max_memory_usage_mb: int = 512
    max_response_time_ms: float = 50.0

    # Optimization levels
    optimization_level: CacheOptimizationLevel = CacheOptimizationLevel.BALANCED

    # Agent-specific settings
    voice_agent_batch_size: int = 100
    whatsapp_agent_batch_size: int = 50
    semantic_similarity_threshold: float = 0.8

    # Monitoring intervals
    metrics_collection_interval: int = 60  # seconds
    optimization_check_interval: int = 300  # seconds

    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary format."""
        result = asdict(self)
        result['optimization_level'] = self.optimization_level.value
        return result

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CacheOptimizationConfig':
        """Create config from dictionary."""
        if 'optimization_level' in data:
            data['optimization_level'] = CacheOptimizationLevel(data['optimization_level'])
        return cls(**data)


@dataclass
class CacheMetrics:
    """Cache performance metrics."""
    cache_name: str
    total_operations: int
    cache_hits: int
    cache_misses: int
    hit_rate: float
    average_latency_ms: float
    p95_latency_ms: float
    p99_latency_ms: float
    memory_usage_mb: float
    evictions: int
    errors: int
    timestamp: float


@dataclass
class SystemMetrics:
    """System-wide Redis metrics."""
    connected_clients: int
    used_memory_mb: float
    used_memory_peak_mb: float
    total_commands_processed: int
    instantaneous_ops_per_sec: int
    keyspace_hits: int
    keyspace_misses: int
    evicted_keys: int
    expired_keys: int
    timestamp: float


class CacheMonitor:
    """
    Comprehensive cache monitoring and performance analysis.
    Tracks metrics across all applications using shared Redis.
    """
    
    def __init__(self, redis_ops: Optional[RedisOperations] = None):
        """
        Initialize cache monitor.
        
        Args:
            redis_ops: Optional Redis operations instance
        """
        self.redis_ops = redis_ops or RedisOperations()
        self.connection_manager = get_redis_connection()
        
        self._lock = threading.Lock()
        self._metrics_history: Dict[str, List[CacheMetrics]] = {}
        self._system_metrics_history: List[SystemMetrics] = []
        self._latency_samples: Dict[str, List[float]] = {}
        
        # Configuration
        self.max_history_size = 1000
        self.max_latency_samples = 10000
        
        # Monitoring state
        self._monitoring_active = False
        self._monitor_thread: Optional[threading.Thread] = None
        self._monitor_interval = 60  # seconds
    
    def record_operation(self, cache_name: str, operation_time_ms: float, hit: Optional[bool] = None):
        """
        Record cache operation for monitoring.
        
        Args:
            cache_name: Name of the cache
            operation_time_ms: Operation time in milliseconds
            hit: Whether it was a cache hit (True), miss (False), or unknown (None)
        """
        with self._lock:
            # Record latency sample
            if cache_name not in self._latency_samples:
                self._latency_samples[cache_name] = []
            
            self._latency_samples[cache_name].append(operation_time_ms)
            
            # Limit sample size
            if len(self._latency_samples[cache_name]) > self.max_latency_samples:
                self._latency_samples[cache_name] = self._latency_samples[cache_name][-8000:]
    
    def get_cache_metrics(self, cache_name: str) -> Optional[CacheMetrics]:
        """
        Get current metrics for a specific cache.
        
        Args:
            cache_name: Name of the cache
            
        Returns:
            CacheMetrics or None if no data available
        """
        try:
            with self._lock:
                latency_samples = self._latency_samples.get(cache_name, [])
                
                if not latency_samples:
                    return None
                
                # Calculate latency percentiles
                sorted_latencies = sorted(latency_samples)
                count = len(sorted_latencies)
                
                avg_latency = sum(sorted_latencies) / count
                p95_latency = sorted_latencies[int(count * 0.95)] if count > 0 else 0
                p99_latency = sorted_latencies[int(count * 0.99)] if count > 0 else 0
                
                # Get Redis stats for this cache
                redis_stats = self.redis_ops.get_stats()
                
                return CacheMetrics(
                    cache_name=cache_name,
                    total_operations=redis_stats.get("operations", 0),
                    cache_hits=redis_stats.get("cache_hits", 0),
                    cache_misses=redis_stats.get("cache_misses", 0),
                    hit_rate=redis_stats.get("hit_rate", 0),
                    average_latency_ms=avg_latency,
                    p95_latency_ms=p95_latency,
                    p99_latency_ms=p99_latency,
                    memory_usage_mb=0,  # Will be filled by system metrics
                    evictions=0,  # Will be filled by system metrics
                    errors=redis_stats.get("errors", 0),
                    timestamp=time.time()
                )
                
        except Exception as e:
            logger.error(f"Error getting cache metrics for {cache_name}: {e}")
            return None
    
    def get_system_metrics(self) -> Optional[SystemMetrics]:
        """
        Get system-wide Redis metrics.
        
        Returns:
            SystemMetrics or None if unavailable
        """
        try:
            client = self.connection_manager.get_sync_client()
            if not client:
                return None
            
            info = client.info()
            
            return SystemMetrics(
                connected_clients=info.get("connected_clients", 0),
                used_memory_mb=info.get("used_memory", 0) / (1024 * 1024),
                used_memory_peak_mb=info.get("used_memory_peak", 0) / (1024 * 1024),
                total_commands_processed=info.get("total_commands_processed", 0),
                instantaneous_ops_per_sec=info.get("instantaneous_ops_per_sec", 0),
                keyspace_hits=info.get("keyspace_hits", 0),
                keyspace_misses=info.get("keyspace_misses", 0),
                evicted_keys=info.get("evicted_keys", 0),
                expired_keys=info.get("expired_keys", 0),
                timestamp=time.time()
            )
            
        except Exception as e:
            logger.error(f"Error getting system metrics: {e}")
            return None
    
    def get_application_metrics(self) -> Dict[str, Any]:
        """
        Get metrics for all applications using shared Redis.
        
        Returns:
            Dict with metrics for each application
        """
        try:
            metrics = {}
            
            # Voice agent metrics
            voice_metrics = self.get_cache_metrics("voice_agent")
            if voice_metrics:
                metrics["voice_agent"] = voice_metrics
            
            # WhatsApp agent metrics
            whatsapp_metrics = self.get_cache_metrics("whatsapp_agent")
            if whatsapp_metrics:
                metrics["whatsapp_agent"] = whatsapp_metrics
            
            # Staff portal metrics
            staff_metrics = self.get_cache_metrics("staff_portal")
            if staff_metrics:
                metrics["staff_portal"] = staff_metrics
            
            # System metrics
            system_metrics = self.get_system_metrics()
            if system_metrics:
                metrics["system"] = system_metrics
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error getting application metrics: {e}")
            return {}
    
    def get_performance_report(self) -> Dict[str, Any]:
        """
        Generate comprehensive performance report.
        
        Returns:
            Dict with performance analysis and recommendations
        """
        try:
            report = {
                "timestamp": time.time(),
                "applications": {},
                "system": {},
                "recommendations": [],
                "alerts": []
            }
            
            # Get current metrics
            app_metrics = self.get_application_metrics()
            
            for app_name, metrics in app_metrics.items():
                if app_name == "system":
                    report["system"] = metrics.__dict__ if hasattr(metrics, '__dict__') else metrics
                    continue
                
                app_report = {
                    "metrics": metrics.__dict__ if hasattr(metrics, '__dict__') else metrics,
                    "status": "healthy",
                    "issues": []
                }
                
                # Analyze performance
                if hasattr(metrics, 'hit_rate') and metrics.hit_rate < 0.7:
                    app_report["issues"].append("Low cache hit rate")
                    app_report["status"] = "warning"
                    report["recommendations"].append(f"Improve cache strategy for {app_name}")
                
                if hasattr(metrics, 'average_latency_ms') and metrics.average_latency_ms > 100:
                    app_report["issues"].append("High average latency")
                    app_report["status"] = "warning"
                    report["recommendations"].append(f"Optimize cache operations for {app_name}")
                
                if hasattr(metrics, 'errors') and metrics.errors > 0:
                    app_report["issues"].append("Cache errors detected")
                    app_report["status"] = "error"
                    report["alerts"].append(f"Cache errors in {app_name}: {metrics.errors}")
                
                report["applications"][app_name] = app_report
            
            # System-level analysis
            system_metrics = app_metrics.get("system")
            if system_metrics:
                if hasattr(system_metrics, 'used_memory_mb') and system_metrics.used_memory_mb > 1000:
                    report["recommendations"].append("Consider increasing Redis memory or implementing eviction policies")
                
                if hasattr(system_metrics, 'connected_clients') and system_metrics.connected_clients > 100:
                    report["recommendations"].append("High number of connected clients - monitor connection pooling")
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating performance report: {e}")
            return {"error": str(e), "timestamp": time.time()}
    
    def start_monitoring(self, interval: int = 60):
        """
        Start continuous monitoring.
        
        Args:
            interval: Monitoring interval in seconds
        """
        if self._monitoring_active:
            logger.warning("Monitoring already active")
            return
        
        self._monitor_interval = interval
        self._monitoring_active = True
        
        def monitor_loop():
            while self._monitoring_active:
                try:
                    # Collect metrics
                    metrics = self.get_application_metrics()
                    
                    # Store in history
                    with self._lock:
                        for app_name, app_metrics in metrics.items():
                            if app_name == "system":
                                self._system_metrics_history.append(app_metrics)
                                if len(self._system_metrics_history) > self.max_history_size:
                                    self._system_metrics_history = self._system_metrics_history[-800:]
                            else:
                                if app_name not in self._metrics_history:
                                    self._metrics_history[app_name] = []
                                
                                self._metrics_history[app_name].append(app_metrics)
                                if len(self._metrics_history[app_name]) > self.max_history_size:
                                    self._metrics_history[app_name] = self._metrics_history[app_name][-800:]
                    
                    time.sleep(self._monitor_interval)
                    
                except Exception as e:
                    logger.error(f"Error in monitoring loop: {e}")
                    time.sleep(self._monitor_interval)
        
        self._monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        self._monitor_thread.start()
        
        logger.info(f"Started cache monitoring with {interval}s interval")
    
    def stop_monitoring(self):
        """Stop continuous monitoring."""
        self._monitoring_active = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5)
        logger.info("Stopped cache monitoring")
    
    def get_metrics_history(self, app_name: str, hours: int = 24) -> List[CacheMetrics]:
        """
        Get metrics history for an application.
        
        Args:
            app_name: Application name
            hours: Number of hours of history to return
            
        Returns:
            List of historical metrics
        """
        cutoff_time = time.time() - (hours * 3600)
        
        with self._lock:
            history = self._metrics_history.get(app_name, [])
            return [m for m in history if m.timestamp >= cutoff_time]
    
    def clear_history(self):
        """Clear all metrics history."""
        with self._lock:
            self._metrics_history.clear()
            self._system_metrics_history.clear()
            self._latency_samples.clear()
        logger.info("Cleared metrics history")


# Global cache monitor instance
_cache_monitor: Optional[CacheMonitor] = None
_monitor_lock = threading.Lock()


def get_cache_monitor() -> CacheMonitor:
    """
    Get global cache monitor instance.
    
    Returns:
        CacheMonitor instance
    """
    global _cache_monitor
    
    with _monitor_lock:
        if _cache_monitor is None:
            _cache_monitor = CacheMonitor()
            logger.info("Initialized global cache monitor")
    
    return _cache_monitor


# Enhanced monitoring functions for voice_agent and whatsapp_agent compatibility

def get_redis_cache_metrics(agent_type: str = "unknown") -> CachePerformanceMetrics:
    """
    Get Redis cache metrics (compatibility function for voice_agent).

    Args:
        agent_type: Type of agent requesting metrics

    Returns:
        CachePerformanceMetrics with comprehensive cache statistics
    """
    try:
        monitor = get_cache_monitor()

        # Get Redis info
        sync_client = monitor.connection_manager.get_sync_client()
        if not sync_client:
            raise Exception("Redis client not available")

        info = sync_client.info()

        # Calculate metrics
        hits = info.get("keyspace_hits", 0)
        misses = info.get("keyspace_misses", 0)
        total_requests = hits + misses
        hit_rate = (hits / total_requests * 100) if total_requests > 0 else 0.0
        miss_rate = 100.0 - hit_rate

        # Get memory usage
        memory_used = info.get("used_memory", 0)
        memory_human = info.get("used_memory_human", "0B")

        # Get keyspace info
        keyspace_info = {}
        for key, value in info.items():
            if key.startswith("db"):
                keyspace_info[key] = value

        # Calculate average response time from latency samples
        avg_response_time = 0.0
        if agent_type in monitor._latency_samples and monitor._latency_samples[agent_type]:
            avg_response_time = sum(monitor._latency_samples[agent_type]) / len(monitor._latency_samples[agent_type])

        return CachePerformanceMetrics(
            hit_rate=hit_rate,
            miss_rate=miss_rate,
            total_requests=total_requests,
            total_hits=hits,
            total_misses=misses,
            average_response_time=avg_response_time,
            memory_usage=memory_used,
            memory_usage_human=memory_human,
            connected_clients=info.get("connected_clients", 0),
            keyspace_info=keyspace_info,
            timestamp=datetime.now(),
            agent_type=agent_type
        )

    except Exception as e:
        logger.error(f"Error getting Redis cache metrics: {e}")
        # Return default metrics on error
        return CachePerformanceMetrics(
            hit_rate=0.0,
            miss_rate=100.0,
            total_requests=0,
            total_hits=0,
            total_misses=0,
            average_response_time=0.0,
            memory_usage=0,
            memory_usage_human="0B",
            connected_clients=0,
            keyspace_info={},
            timestamp=datetime.now(),
            agent_type=agent_type
        )


def get_optimization_recommendations(agent_type: str = "unknown") -> Dict[str, Any]:
    """
    Get cache optimization recommendations based on current performance.

    Args:
        agent_type: Type of agent for specific recommendations

    Returns:
        Dictionary with optimization recommendations
    """
    try:
        metrics = get_redis_cache_metrics(agent_type)
        recommendations = []

        # Hit rate recommendations
        if metrics.hit_rate < 70:
            recommendations.append({
                "type": "hit_rate",
                "severity": "high",
                "message": f"Low hit rate ({metrics.hit_rate:.1f}%). Consider increasing TTL values or improving cache key strategies.",
                "suggested_action": "Review cache TTL settings and key patterns"
            })
        elif metrics.hit_rate < 85:
            recommendations.append({
                "type": "hit_rate",
                "severity": "medium",
                "message": f"Moderate hit rate ({metrics.hit_rate:.1f}%). Room for improvement.",
                "suggested_action": "Optimize cache warming strategies"
            })

        # Memory usage recommendations
        memory_mb = metrics.memory_usage / (1024 * 1024)
        if memory_mb > 400:
            recommendations.append({
                "type": "memory",
                "severity": "high",
                "message": f"High memory usage ({memory_mb:.1f}MB). Consider implementing cache eviction policies.",
                "suggested_action": "Review cache size limits and implement LRU eviction"
            })

        # Response time recommendations
        if metrics.average_response_time > 100:
            recommendations.append({
                "type": "performance",
                "severity": "medium",
                "message": f"High average response time ({metrics.average_response_time:.1f}ms).",
                "suggested_action": "Consider Redis connection pooling optimization"
            })

        # Agent-specific recommendations
        if agent_type == "voice_agent":
            if metrics.average_response_time > 50:
                recommendations.append({
                    "type": "voice_specific",
                    "severity": "medium",
                    "message": "Voice agent requires low latency. Consider semantic cache preloading.",
                    "suggested_action": "Implement semantic cache warming for common queries"
                })

        return {
            "agent_type": agent_type,
            "timestamp": datetime.now().isoformat(),
            "metrics_summary": metrics.to_dict(),
            "recommendations": recommendations,
            "overall_health": "good" if metrics.hit_rate > 85 and memory_mb < 300 else "needs_attention"
        }

    except Exception as e:
        logger.error(f"Error generating optimization recommendations: {e}")
        return {
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
