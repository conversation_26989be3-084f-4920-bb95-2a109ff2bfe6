import React, { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { Search, LogOut, Bell, Calendar, User, Settings, Menu, X } from 'react-feather';
import NotificationCenter from '../notifications/NotificationCenter';

/**
 * Header component with consistent styling
 * @param {Object} props - Component props
 * @param {Object} props.user - User object
 * @param {number} props.notificationCount - Notification count
 * @param {Function} props.onLogout - Logout handler
 * @param {Function} props.onSearch - Search handler
 */
const Header = ({ 
  user, 
  notificationCount = 0, 
  onLogout,
}) => {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [localNotificationCount, setLocalNotificationCount] = useState(notificationCount);
  const notificationRef = useRef(null);
  
  const handleSearch = (e) => {
    e.preventDefault();
    if (searchTerm.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchTerm)}`);
      setSearchTerm('');
    }
  };
  
  const handleNotificationClick = () => {
    setShowNotifications(!showNotifications);
  };
  
  // Close notifications when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (notificationRef.current && !notificationRef.current.contains(event.target)) {
        setShowNotifications(false);
      }
    }
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLogout = async () => {
    if (onLogout) {
      onLogout();
    } else {
      try {
        const res = await fetch('/api/auth/logout', {
          method: 'POST',
        });
        
        if (res.ok) {
          router.push('/login');
        }
      } catch (error) {
        console.error('Logout error:', error);
      }
    }
  };

  const navItems = [
    { name: 'Dashboard', href: '/dashboard', icon: User },
    { name: 'Appointments', href: '/appointments', icon: Calendar },
    { name: 'Search', href: '/search', icon: Search },
  ];

  // Only show admin link if user has admin role
  if (user && user.role === 'admin') {
    navItems.push({ name: 'Admin', href: '/admin', icon: Settings });
  }

  return (
    <header className="bg-[#003253] shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex">
            <div className="flex-shrink-0 flex items-center">
              <Link href="/dashboard" className="text-white font-bold text-xl">
                Voice Health Portal
              </Link>
            </div>
            
            {/* Desktop navigation */}
            <nav className="hidden md:ml-6 md:flex md:space-x-4">
              {navItems.map((item) => {
                const isActive = router.pathname === item.href;
                const Icon = item.icon;
                
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={`inline-flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                      isActive 
                        ? 'text-white bg-[#004b7a]' 
                        : 'text-blue-100 hover:text-white hover:bg-[#004b7a]'
                    }`}
                  >
                    <Icon className="mr-1.5 h-5 w-5" />
                    {item.name}
                  </Link>
                );
              })}
            </nav>
          </div>
          
          <div className="flex items-center">
            {/* Search - without button, pressing Enter submits */}
            <form onSubmit={handleSearch} className="hidden md:block mr-4">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-blue-300" />
                </div>
                <input
                  type="text"
                  placeholder="Search..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-transparent rounded-md leading-5 bg-[#004b7a] text-blue-100 placeholder-blue-300 focus:outline-none focus:bg-white focus:text-slate-800 focus:placeholder-slate-400 focus:ring-white focus:border-white sm:text-sm"
                />
              </div>
            </form>
            
            {/* Notifications */}
            <div className="relative" ref={notificationRef}>
              <button 
                onClick={handleNotificationClick}
                className="ml-4 flex-shrink-0 p-1 rounded-full text-blue-100 hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-[#003253] focus:ring-white"
              >
                <span className="sr-only">View notifications</span>
                <div className="relative">
                  <Bell className="h-6 w-6" />
                  {localNotificationCount > 0 && (
                    <span className="absolute -top-1 -right-1 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-red-100 bg-red-600 rounded-full">
                      {localNotificationCount > 9 ? '9+' : localNotificationCount}
                    </span>
                  )}
                </div>
              </button>
              
              {/* Notification dropdown */}
              {showNotifications && (
                <div className="absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg py-1 z-50 max-h-96 overflow-y-auto">
                  {user && user.hospital_id && (
                    <NotificationCenter 
                      hospitalId={user.hospital_id}
                      onCountUpdate={setLocalNotificationCount}
                      onNotificationSelect={() => setShowNotifications(false)}
                    />
                  )}
                </div>
              )}
            </div>
            
            {/* Profile dropdown - simplified for this example */}
            {user && (
              <div className="ml-4 relative flex-shrink-0">
                <div className="flex items-center">
                  <div className="hidden md:block">
                    <div className="flex items-center">
                      <div className="ml-3">
                        <div className="text-base font-medium text-white">{user.name}</div>
                        <div className="text-sm font-medium text-teal-100">{user.role}</div>
                      </div>
                    </div>
                  </div>
                  
                  <button
                    onClick={handleLogout}
                    className="ml-4 flex-shrink-0 p-1 rounded-full text-teal-100 hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-teal-600 focus:ring-white"
                  >
                    <span className="sr-only">Log out</span>
                    <LogOut className="h-6 w-6" />
                  </button>
                </div>
              </div>
            )}
            
            {/* Mobile menu button */}
            <div className="flex items-center md:hidden ml-4">
              <button
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className="inline-flex items-center justify-center p-2 rounded-md text-teal-100 hover:text-white hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-teal-600 focus:ring-white"
              >
                <span className="sr-only">Open main menu</span>
                {mobileMenuOpen ? (
                  <X className="block h-6 w-6" />
                ) : (
                  <Menu className="block h-6 w-6" />
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
      
      {/* Mobile menu */}
      {mobileMenuOpen && (
        <div className="md:hidden">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
            {navItems.map((item) => {
              const isActive = router.pathname === item.href;
              const Icon = item.icon;
              
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`block px-3 py-2 rounded-md text-base font-medium ${
                    isActive 
                      ? 'text-white bg-teal-700' 
                      : 'text-teal-100 hover:text-white hover:bg-teal-700'
                  }`}
                >
                  <div className="flex items-center">
                    <Icon className="mr-3 h-5 w-5" />
                    {item.name}
                  </div>
                </Link>
              );
            })}
          </div>
          
          {/* Mobile search */}
          <div className="pt-4 pb-3 border-t border-teal-700">
            <form onSubmit={handleSearch} className="px-2">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-teal-300" />
                </div>
                <input
                  type="text"
                  placeholder="Search..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-transparent rounded-md leading-5 bg-teal-700 text-teal-100 placeholder-teal-300 focus:outline-none focus:bg-white focus:text-slate-800 focus:placeholder-slate-400 focus:ring-white focus:border-white sm:text-sm"
                />
              </div>
            </form>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
