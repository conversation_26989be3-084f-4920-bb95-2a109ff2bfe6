import { withRole } from '../../../lib/auth';
import { getFirestore } from '../../../lib/firebase';
import redisClient from '../../../lib/redis_client';

// Only admin users can access scheduling configuration
export default withRole(async (req, res) => {
  // GET - Fetch hospital scheduling configuration
  if (req.method === 'GET') {
    try {
      // SECURITY: Use only authenticated user's hospital_id, ignore any client-provided hospital_id
      const hospitalId = req.user.hospital_id;

      if (!hospitalId) {
        return res.status(401).json({
          success: false,
          message: 'Hospital ID missing from authenticated user'
        });
      }
      
      // Get hospital scheduling configuration from Firestore
      let db;
      try {
        db = getFirestore();
      } catch (dbError) {
        console.error('Failed to initialize Firestore:', dbError);
        return res.status(500).json({
          success: false,
          message: 'Database initialization failed'
        });
      }
      const hospitalRef = db.collection('hospitals').doc(`hospital_${hospitalId}_data`);
      let hospitalDoc;

      try {
        hospitalDoc = await hospitalRef.get();
      } catch (firestoreError) {
        console.error('Firestore read error:', firestoreError);
        return res.status(500).json({
          success: false,
          message: 'Failed to retrieve hospital data'
        });
      }

      if (!hospitalDoc.exists) {
        return res.status(404).json({
          success: false,
          message: 'Hospital not found'
        });
      }
      
      const hospitalData = hospitalDoc.data();
      const schedulingConfig = hospitalData.scheduling_config || {};
      
      // Provide default configuration if not set
      const defaultConfig = {
        appointment_duration_minutes: 30,
        working_hours: {
          start: "09:00",
          end: "17:00",
          lunch_break: {
            start: "12:00",
            end: "13:00"
          }
        },
        time_slot_interval_minutes: 30,
        max_slots_per_day: 16,
        advance_booking_days: 30,
        same_day_booking_cutoff_hours: 2,
        test_booking: {
          time_slot_interval_minutes: 30,
          same_day_booking_cutoff_hours: 1,
          max_slots_per_day: 32
        }
      };
      
      const config = { ...defaultConfig, ...schedulingConfig };
      
      return res.status(200).json({
        success: true,
        data: config
      });
    } catch (error) {
      console.error('Get scheduling configuration error:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
  
  // PUT - Update hospital scheduling configuration
  if (req.method === 'PUT') {
    try {
      const { config } = req.body;

      // SECURITY: Use only authenticated user's hospital_id, ignore any client-provided hospitalId
      const hospitalId = req.user.hospital_id;

      // Validate required parameters
      if (!hospitalId) {
        return res.status(401).json({
          success: false,
          message: 'Hospital ID missing from authenticated user'
        });
      }

      if (!config) {
        return res.status(400).json({
          success: false,
          message: 'Configuration is required'
        });
      }
      
      // Validate configuration structure
      const validationResult = validateSchedulingConfig(config);
      if (!validationResult.valid) {
        return res.status(400).json({
          success: false,
          message: `Invalid configuration: ${validationResult.error}`
        });
      }
      
      // Update hospital scheduling configuration in Firestore
      let db;
      try {
        db = getFirestore();
      } catch (dbError) {
        console.error('Failed to initialize Firestore:', dbError);
        return res.status(500).json({
          success: false,
          message: 'Database initialization failed'
        });
      }
      const hospitalRef = db.collection('hospitals').doc(`hospital_${hospitalId}_data`);

      try {
        await hospitalRef.update({
          scheduling_config: config,
          updated_at: new Date().toISOString()
        });
      } catch (firestoreError) {
        console.error('Firestore update error:', firestoreError);
        return res.status(500).json({
          success: false,
          message: 'Failed to update scheduling configuration'
        });
      }
      
      // Trigger voice agent configuration refresh
      try {
        if (redisClient && typeof redisClient.triggerSchedulingConfigRefresh === 'function') {
          await redisClient.triggerSchedulingConfigRefresh(hospitalId);
        } else {
          console.warn('Redis client not available for configuration refresh');
        }
      } catch (refreshError) {
        console.warn('Failed to trigger voice agent refresh:', refreshError);
        // Don't fail the request if refresh fails
      }
      
      return res.status(200).json({
        success: true,
        message: 'Scheduling configuration updated successfully'
      });
    } catch (error) {
      console.error('Update scheduling configuration error:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
  
  // Method not allowed
  return res.status(405).json({
    success: false,
    message: 'Method not allowed'
  });
}, ['admin']); // Only admin role can access this endpoint

/**
 * Convert time string (HH:MM) to minutes since midnight
 * @param {string} timeStr - Time in HH:MM format
 * @returns {number} Minutes since midnight
 */
function timeToMinutes(timeStr) {
  const [hours, minutes] = timeStr.split(':').map(Number);
  return hours * 60 + minutes;
}

/**
 * Validate scheduling configuration structure and values
 */
function validateSchedulingConfig(config) {
  try {
    // Check required fields
    if (!config.appointment_duration_minutes || config.appointment_duration_minutes < 5 || config.appointment_duration_minutes > 180) {
      return { valid: false, error: 'Appointment duration must be between 5 and 180 minutes' };
    }
    
    if (!config.working_hours || !config.working_hours.start || !config.working_hours.end) {
      return { valid: false, error: 'Working hours start and end times are required' };
    }
    
    // Validate time format (HH:MM)
    const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
    if (!timeRegex.test(config.working_hours.start) || !timeRegex.test(config.working_hours.end)) {
      return { valid: false, error: 'Working hours must be in HH:MM format' };
    }

    // Validate end time is after start time
    const startMinutes = timeToMinutes(config.working_hours.start);
    const endMinutes = timeToMinutes(config.working_hours.end);

    if (endMinutes <= startMinutes) {
      return { valid: false, error: 'Working hours end time must be after start time' };
    }

    // Validate lunch break if provided
    if (config.working_hours.lunch_break) {
      if (!timeRegex.test(config.working_hours.lunch_break.start) || !timeRegex.test(config.working_hours.lunch_break.end)) {
        return { valid: false, error: 'Lunch break times must be in HH:MM format' };
      }

      // Validate lunch break is within working hours
      const lunchStartMinutes = timeToMinutes(config.working_hours.lunch_break.start);
      const lunchEndMinutes = timeToMinutes(config.working_hours.lunch_break.end);

      if (lunchStartMinutes < startMinutes || lunchEndMinutes > endMinutes) {
        return { valid: false, error: 'Lunch break must be within working hours' };
      }

      if (lunchEndMinutes <= lunchStartMinutes) {
        return { valid: false, error: 'Lunch break end time must be after start time' };
      }
    }
    
    // Validate time slot interval
    if (!config.time_slot_interval_minutes ||
        typeof config.time_slot_interval_minutes !== 'number' ||
        config.time_slot_interval_minutes < 5 ||
        config.time_slot_interval_minutes > 120) {
      return { valid: false, error: 'Time slot interval must be a number between 5 and 120 minutes' };
    }

    // Validate that appointment duration is compatible with time slot interval
    if (config.appointment_duration_minutes % config.time_slot_interval_minutes !== 0) {
      return { valid: false, error: 'Appointment duration must be a multiple of time slot interval' };
    }
    
    // Validate max slots per day
    if (!config.max_slots_per_day ||
        typeof config.max_slots_per_day !== 'number' ||
        config.max_slots_per_day < 1 ||
        config.max_slots_per_day > 200) {
      return { valid: false, error: 'Max slots per day must be a number between 1 and 200' };
    }

    // Calculate theoretical maximum slots based on working hours
    let workingMinutes = endMinutes - startMinutes;
    if (config.working_hours.lunch_break) {
      const lunchStartMinutes = timeToMinutes(config.working_hours.lunch_break.start);
      const lunchEndMinutes = timeToMinutes(config.working_hours.lunch_break.end);
      workingMinutes -= (lunchEndMinutes - lunchStartMinutes);
    }

    const theoreticalMaxSlots = Math.floor(workingMinutes / config.time_slot_interval_minutes);
    if (config.max_slots_per_day > theoreticalMaxSlots) {
      return {
        valid: false,
        error: `Max slots per day (${config.max_slots_per_day}) exceeds theoretical maximum (${theoreticalMaxSlots}) based on working hours and time slot interval`
      };
    }
    
    // Validate advance booking days
    if (!config.advance_booking_days ||
        typeof config.advance_booking_days !== 'number' ||
        config.advance_booking_days < 1 ||
        config.advance_booking_days > 365) {
      return { valid: false, error: 'Advance booking days must be a number between 1 and 365' };
    }
    
    // Validate same day booking cutoff
    if (config.same_day_booking_cutoff_hours === undefined ||
        config.same_day_booking_cutoff_hours === null ||
        typeof config.same_day_booking_cutoff_hours !== 'number' ||
        config.same_day_booking_cutoff_hours < 0 ||
        config.same_day_booking_cutoff_hours > 24) {
      return { valid: false, error: 'Same day booking cutoff must be a number between 0 and 24 hours' };
    }

    // Validate test booking configuration if provided
    if (config.test_booking) {
      if (config.test_booking.time_slot_interval_minutes !== undefined) {
        if (typeof config.test_booking.time_slot_interval_minutes !== 'number' ||
            config.test_booking.time_slot_interval_minutes < 5 ||
            config.test_booking.time_slot_interval_minutes > 120) {
          return { valid: false, error: 'Test booking time slot interval must be between 5 and 120 minutes' };
        }
      }

      if (config.test_booking.same_day_booking_cutoff_hours !== undefined) {
        if (typeof config.test_booking.same_day_booking_cutoff_hours !== 'number' ||
            config.test_booking.same_day_booking_cutoff_hours < 0 ||
            config.test_booking.same_day_booking_cutoff_hours > 24) {
          return { valid: false, error: 'Test booking same day cutoff must be between 0 and 24 hours' };
        }
      }

      if (config.test_booking.max_slots_per_day !== undefined) {
        if (typeof config.test_booking.max_slots_per_day !== 'number' ||
            config.test_booking.max_slots_per_day < 1 ||
            config.test_booking.max_slots_per_day > 200) {
          return { valid: false, error: 'Test booking max slots per day must be between 1 and 200' };
        }
      }
    }

    return { valid: true };
  } catch (error) {
    return { valid: false, error: 'Invalid configuration format' };
  }
}
