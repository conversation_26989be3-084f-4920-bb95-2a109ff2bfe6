#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to create an admin user for the Hospital Staff Portal
 * Usage: node create-admin.js --username admin --password your_secure_password --name "Admin User" --email <EMAIL> --hospital hospital_id
 */

import { Command } from 'commander';
const program = new Command();
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';
import { db } from '../lib/firebase.js';
// Add these imports:
import { collection, query, where, getDocs, doc, getDoc, setDoc } from 'firebase/firestore';
import { pool } from '../server/db.js';
import admin from 'firebase-admin';
import path from 'path';
import { fileURLToPath, pathToFileURL } from 'url';
import fs from 'fs';

// Configure command line argument parsing
program
  .requiredOption('--username <username>', 'Admin username')
  .requiredOption('--password <password>', 'Admin password')
  .requiredOption('--name <name>', 'Admin full name')
  .requiredOption('--email <email>', 'Admin email address')
  .option('--hospital <hospital_id>', 'Hospital ID', 'default')
  .option('--role <role>', 'User role (default: admin)', 'admin')
  .parse(process.argv);

const options = program.opts();

// Function to hash password
async function hashPassword(password) {
  return bcrypt.hash(password, 10);
}

// Function to create admin user
async function createAdminUser(userData) {
  try {
    console.log('Creating admin user...');

    // Check if hospital exists, create it if not
    await ensureHospitalExists(userData.hospital_id);

    // Check if user already exists
    const staffCollection = collection(db, 'staff');
    const userQuery = query(staffCollection, where('email', '==', userData.email));
    const snapshot = await getDocs(userQuery);

    if (!snapshot.empty) {
      console.error(`User with email ${userData.email} already exists!`);
      process.exit(1);
    }

    // Use fs to read and parse the service account JSON
    const __filename = fileURLToPath(import.meta.url);
    const __dirname = path.dirname(__filename);
    const serviceAccountPath = path.join(__dirname, 'serviceAccountKey.json');
    const serviceAccount = JSON.parse(fs.readFileSync(serviceAccountPath, 'utf8'));

    // Initialize Firebase Admin if not already initialized
    if (!admin.apps.length) {
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: serviceAccount.project_id,
      });
    }

    // Create user in Firebase Auth
    let firebaseUser;
    try {
      firebaseUser = await admin.auth().createUser({
        email: userData.email,
        password: userData.password,
        displayName: userData.name,
      });
    } catch (err) {
      if (err.code === 'auth/email-already-exists') {
        firebaseUser = await admin.auth().getUserByEmail(userData.email);
      } else {
        throw err;
      }
    }
    const userId = firebaseUser.uid; // Use Firebase Auth UID

    // Create user in Firestore
    const userDocRef = doc(db, 'staff', userId);

    // Hash the password (optional, but not needed if using Firebase Auth for login)
    // const hashedPassword = await hashPassword(userData.password);

    await setDoc(userDocRef, {
      id: userId,
      username: userData.username,
      name: userData.name,
      email: userData.email,
      role: userData.role,
      hospital_id: userData.hospital_id,
      // password_hash: hashedPassword, // Not needed if using Firebase Auth
      created_at: new Date(),
      updated_at: new Date()
    });

    console.log(`Created admin user in Firestore with ID: ${userId}`);

    // Create user in PostgreSQL
    await pool.query(`
      INSERT INTO staff (id, name, email, role, hospital_id, settings)
      VALUES ($1, $2, $3, $4, $5, $6)
      ON CONFLICT (id) DO UPDATE
      SET name = $2, email = $3, role = $4, hospital_id = $5, settings = $6
    `, [
      userId,
      userData.name,
      userData.email,
      userData.role,
      userData.hospital_id,
      JSON.stringify({
        username: userData.username,
      })
    ]);

    console.log(`Created admin user in PostgreSQL with ID: ${userId}`);
    console.log(`
==========================================
Admin user created successfully!
==========================================
Username: ${userData.username}
Name: ${userData.name}
Email: ${userData.email}
Role: ${userData.role}
Hospital ID: ${userData.hospital_id}
==========================================
    `);

    return userId;
  } catch (error) {
    console.error('Error creating admin user:', error);
    process.exit(1);
  }
}

// Function to ensure hospital exists
async function ensureHospitalExists(hospitalId) {
  try {
    // Check if hospital exists in Firestore
    const hospitalDocRef = doc(db, 'hospitals', hospitalId);
    const hospitalSnap = await getDoc(hospitalDocRef);

    // If hospital doesn't exist, create it
    if (!hospitalSnap.exists()) {
      console.log(`Hospital ${hospitalId} does not exist, creating it...`);

      // Create hospital in Firestore
      await setDoc(hospitalDocRef, {
        id: hospitalId,
        name: `Hospital ${hospitalId}`,
        address: 'To be updated',
        phone: 'To be updated',
        email: 'To be updated',
        settings: {},
        created_at: new Date(),
        updated_at: new Date()
      });
      
      // Create hospital in PostgreSQL
      await pool.query(`
        INSERT INTO hospitals (id, name, address, phone, email, settings)
        VALUES ($1, $2, $3, $4, $5, $6)
        ON CONFLICT (id) DO NOTHING
      `, [
        hospitalId,
        `Hospital ${hospitalId}`,
        'To be updated',
        'To be updated',
        'To be updated',
        '{}'
      ]);
      
      console.log(`Created hospital ${hospitalId}`);
    }
  } catch (error) {
    console.error(`Error ensuring hospital exists:`, error);
    process.exit(1);
  }
}

// Check if the PostgreSQL hospitals table exists
async function ensureTablesExist() {
  try {
    // Check if hospitals table exists
    const result = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public'
        AND table_name = 'hospitals'
      );
    `);
    
    if (!result.rows[0].exists) {
      console.error('PostgreSQL tables do not exist. Run database initialization script first:');
      console.error('node scripts/db_init.js');
      process.exit(1);
    }
  } catch (error) {
    console.error('Error checking PostgreSQL tables:', error);
    process.exit(1);
  }
}

// Main function
async function main() {
  try {
    // Ensure database tables exist
    await ensureTablesExist();
    
    // Create admin user
    const userData = {
      username: options.username,
      password: options.password,
      name: options.name,
      email: options.email,
      role: options.role,
      hospital_id: options.hospital
    };
    
    await createAdminUser(userData);
    
    // Exit successfully
    process.exit(0);
  } catch (error) {
    console.error('Error in main function:', error);
    process.exit(1);
  }
}

// Run the script
main();