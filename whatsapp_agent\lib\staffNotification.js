/**
 * Staff Notification Module for WhatsApp Agent
 * 
 * Handles sending OCR-related notifications to the staff portal
 */

import { v4 as uuidv4 } from 'uuid';
import { logger } from './logger.js';
import * as firebase from './firebase.js';

// Notification types
export const NOTIFICATION_TYPES = {
  OCR_ERROR: 'ocr_error',
  OCR_LOW_CONFIDENCE: 'ocr_low_confidence',
  MANUAL_REVIEW: 'manual_review_requested'
};

/**
 * Send OCR error notification to staff portal
 * @param {Object} data - Notification data
 * @returns {Promise<Object>} - Result with notification ID
 */
export const sendOcrErrorNotification = async (data) => {
  try {
    const { hospitalId, phoneNumber, mediaUrl, ocrText, error, confidence, timestamp } = data;
    
    // Create notification document
    const notificationId = uuidv4();
    const notification = {
      id: notificationId,
      type: NOTIFICATION_TYPES.OCR_ERROR,
      hospitalId,
      phoneNumber,
      mediaUrl,
      ocrText,
      error,
      confidence,
      timestamp: timestamp || new Date().toISOString(),
      status: 'pending',
      viewed: false,
      source: 'whatsapp'
    };
    
    // Save to Firebase
    await firebase.createNotification(hospitalId, notificationId, notification);
    
    logger.info(`Staff notification created for OCR error: ${notificationId}`);
    
    return {
      success: true,
      notificationId
    };
  } catch (error) {
    logger.error('Error sending OCR error notification:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Send low confidence OCR notification to staff portal
 * @param {Object} data - Notification data
 * @returns {Promise<Object>} - Result with notification ID
 */
export const sendLowConfidenceNotification = async (data) => {
  try {
    const { hospitalId, phoneNumber, mediaUrl, ocrText, confidence, prescriptionDetails, timestamp } = data;
    
    // Create notification document
    const notificationId = uuidv4();
    const notification = {
      id: notificationId,
      type: NOTIFICATION_TYPES.OCR_LOW_CONFIDENCE,
      hospitalId,
      phoneNumber,
      mediaUrl,
      ocrText,
      confidence,
      prescriptionDetails,
      timestamp: timestamp || new Date().toISOString(),
      status: 'pending',
      viewed: false,
      source: 'whatsapp'
    };
    
    // Save to Firebase
    await firebase.createNotification(hospitalId, notificationId, notification);
    
    logger.info(`Staff notification created for low OCR confidence: ${notificationId}`);
    
    return {
      success: true,
      notificationId
    };
  } catch (error) {
    logger.error('Error sending low confidence notification:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Request manual review from staff
 * @param {Object} data - Notification data
 * @returns {Promise<Object>} - Result with notification ID
 */
export const requestManualReview = async (data) => {
  try {
    const { hospitalId, phoneNumber, mediaUrl, ocrText, reason, prescriptionDetails, timestamp } = data;
    
    // Create notification document
    const notificationId = uuidv4();
    const notification = {
      id: notificationId,
      type: NOTIFICATION_TYPES.MANUAL_REVIEW,
      hospitalId,
      phoneNumber,
      mediaUrl,
      ocrText,
      reason,
      prescriptionDetails,
      timestamp: timestamp || new Date().toISOString(),
      status: 'pending',
      viewed: false,
      source: 'whatsapp',
      priority: 'high'
    };
    
    // Save to Firebase
    await firebase.createNotification(hospitalId, notificationId, notification);
    
    logger.info(`Manual review requested: ${notificationId}`);
    
    return {
      success: true,
      notificationId
    };
  } catch (error) {
    logger.error('Error requesting manual review:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

export default {
  sendOcrErrorNotification,
  sendLowConfidenceNotification,
  requestManualReview,
  NOTIFICATION_TYPES
};
