"""
Shared Redis Implementation for Voice Health Portal

This package provides a unified Redis implementation for:
- Voice Agent: IndicBERT-powered semantic caching, call context management
- WhatsApp Agent: Multilingual support, doctor availability caching
- Staff Portal: Availability status management

Features:
- Production-ready connection pooling
- IndicBERT semantic caching for voice agent
- Multilingual processing for WhatsApp agent
- Comprehensive monitoring and optimization
- Cross-platform compatibility (Python async/sync, Node.js)
"""

from .connection_manager import RedisConnectionManager, get_redis_connection
from .config import RedisConfig, get_redis_config
from .base_operations import RedisOperations

__version__ = "1.0.0"
__all__ = [
    "RedisConnectionManager",
    "get_redis_connection", 
    "RedisConfig",
    "get_redis_config",
    "RedisOperations"
]
