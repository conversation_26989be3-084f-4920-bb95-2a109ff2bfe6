import { useState } from 'react';
import {
  Users,
  UserPlus,
  Edit,
  Trash2,
  Shield,
  UserCheck,
  Activity,
  Stethoscope,
  Clipboard,
  User
} from 'react-feather';

// Utility functions for accessible status indicators
const getStatusIcon = (status, type = 'general') => {
  if (type === 'role') {
    const roleIcons = {
      admin: <Shield className="h-3 w-3 mr-1" aria-hidden="true" />,
      doctor: <Stethoscope className="h-3 w-3 mr-1" aria-hidden="true" />,
      receptionist: <UserCheck className="h-3 w-3 mr-1" aria-hidden="true" />,
      nurse: <Activity className="h-3 w-3 mr-1" aria-hidden="true" />,
      lab_technician: <Clipboard className="h-3 w-3 mr-1" aria-hidden="true" />
    };
    return roleIcons[status] || <User className="h-3 w-3 mr-1" aria-hidden="true" />;
  }
  return null;
};

const getAccessibleStatusText = (status, type = 'general') => {
  if (type === 'role') {
    const roleLabels = {
      admin: 'Administrator',
      doctor: 'Doctor',
      receptionist: 'Receptionist',
      nurse: 'Nurse',
      lab_technician: 'Lab Technician'
    };
    return roleLabels[status] || status;
  }
  return status;
};

// Input validation utilities
const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

const validatePassword = (password) => {
  const errors = [];

  if (!password) {
    errors.push('Password is required');
    return errors;
  }

  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }

  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }

  if (!/[0-9]/.test(password)) {
    errors.push('Password must contain at least one number');
  }

  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }

  return errors;
};

const validateStaffInput = (staffData) => {
  const errors = [];
  const validRoles = ['admin', 'doctor', 'receptionist', 'nurse', 'lab_technician'];

  // Name validation
  if (!staffData.name?.trim()) {
    errors.push('Name is required');
  } else if (staffData.name.trim().length < 2) {
    errors.push('Name must be at least 2 characters long');
  } else if (staffData.name.trim().length > 100) {
    errors.push('Name must be less than 100 characters');
  }

  // Email validation
  if (!staffData.email?.trim()) {
    errors.push('Email is required');
  } else if (!validateEmail(staffData.email.trim())) {
    errors.push('Please enter a valid email address');
  }

  // Role validation
  if (!staffData.role) {
    errors.push('Role is required');
  } else if (!validRoles.includes(staffData.role)) {
    errors.push('Invalid role selected');
  }

  // Password validation
  const passwordErrors = validatePassword(staffData.password);
  errors.push(...passwordErrors);

  return errors;
};

// Helper function to show validation errors using notifications
const showValidationErrors = (errors, showError) => {
  if (errors.length === 1) {
    showError('Validation Error', errors[0]);
  } else {
    showError('Validation Errors', errors.join('\n'));
  }
};

export default function StaffManagement({ 
  staff, 
  staffOperations, 
  user, 
  showError, 
  showSuccess 
}) {
  // Staff editing
  const [editingStaff, setEditingStaff] = useState(null);
  const [newStaff, setNewStaff] = useState({
    name: '',
    email: '',
    role: 'receptionist',
    password: ''
  });

  // Create new staff member
  const createStaffMember = async () => {
    if (!user) return;

    // Validate inputs
    const errors = validateStaffInput(newStaff);

    if (errors.length > 0) {
      showValidationErrors(errors, showError);
      return;
    }

    const result = await staffOperations.createStaff(newStaff, user);

    if (result.success) {
      // Reset form
      setNewStaff({
        name: '',
        email: '',
        role: 'receptionist',
        password: ''
      });
    }
  };

  // Update staff member
  const updateStaffMember = async () => {
    if (!user || !editingStaff) return;

    const staffData = {
      name: editingStaff.name,
      email: editingStaff.email,
      role: editingStaff.role,
      // Only include password if it's been changed
      ...(editingStaff.password ? { password: editingStaff.password } : {})
    };

    const result = await staffOperations.updateStaff(editingStaff.id, staffData, user);

    if (result.success) {
      setEditingStaff(null);
    }
  };

  // Delete staff member
  const deleteStaffMember = async (staffId) => {
    if (!user || !staffId) return;

    if (!confirm('Are you sure you want to delete this staff member? This action cannot be undone.')) {
      return;
    }

    await staffOperations.deleteStaff(staffId, user);
  };

  return (
    <div className="bg-white rounded-lg shadow overflow-hidden">
      <div className="px-6 py-4 border-b border-slate-200">
        <h2 className="text-lg font-semibold text-slate-900">Staff Management</h2>
      </div>
      
      {/* Add staff form */}
      <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <h3 className="text-md font-medium text-slate-700 mb-3">Add New Staff Member</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label htmlFor="staff-name" className="block text-sm font-medium text-slate-700">Name *</label>
            <input
              type="text"
              id="staff-name"
              value={newStaff.name}
              onChange={(e) => setNewStaff({...newStaff, name: e.target.value})}
              className="mt-1 p-2 block w-full border border-slate-300 rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
              required
            />
          </div>
          
          <div>
            <label htmlFor="staff-email" className="block text-sm font-medium text-slate-700">Email *</label>
            <input
              type="email"
              id="staff-email"
              value={newStaff.email}
              onChange={(e) => setNewStaff({...newStaff, email: e.target.value})}
              className="mt-1 p-2 block w-full border border-slate-300 rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
              required
            />
          </div>
          
          <div>
            <label htmlFor="staff-role" className="block text-sm font-medium text-slate-700">Role *</label>
            <select
              id="staff-role"
              value={newStaff.role}
              onChange={(e) => setNewStaff({...newStaff, role: e.target.value})}
              className="mt-1 p-2 block w-full border border-slate-300 rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
              required
            >
              <option value="admin">Admin</option>
              <option value="doctor">Doctor</option>
              <option value="receptionist">Receptionist</option>
              <option value="nurse">Nurse</option>
              <option value="lab_technician">Lab Technician</option>
            </select>
          </div>
          
          <div>
            <label htmlFor="staff-password" className="block text-sm font-medium text-slate-700">Password *</label>
            <input
              type="password"
              id="staff-password"
              value={newStaff.password}
              onChange={(e) => setNewStaff({...newStaff, password: e.target.value})}
              className="mt-1 p-2 block w-full border border-slate-300 rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
              required
            />
          </div>
        </div>
        
        <div className="mt-4">
          <button
            onClick={createStaffMember}
            className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
          >
            <UserPlus className="h-4 w-4 mr-2" />
            Add Staff Member
          </button>
        </div>
      </div>
      
      {/* Staff list */}
      <div className="px-6 py-4">
        <h3 className="text-md font-medium text-slate-700 mb-3">Staff Members</h3>
        
        {staff.length === 0 ? (
          <div className="text-center py-4">
            <p className="text-slate-500">No staff members found.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-slate-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Name</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Email</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Role</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-slate-200">
                {staff.map((staffMember) => (
                  <tr key={staffMember.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-slate-900">{staffMember.name}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500">{staffMember.email}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                        ${staffMember.role === 'admin' ? 'bg-red-100 text-red-800' : ''}
                        ${staffMember.role === 'doctor' ? 'bg-blue-100 text-blue-800' : ''}
                        ${staffMember.role === 'receptionist' ? 'bg-green-100 text-green-800' : ''}
                        ${staffMember.role === 'nurse' ? 'bg-purple-100 text-purple-800' : ''}
                        ${staffMember.role === 'lab_technician' ? 'bg-yellow-100 text-yellow-800' : ''}
                      `} role="status" aria-label={`Role: ${getAccessibleStatusText(staffMember.role, 'role')}`}>
                        {getStatusIcon(staffMember.role, 'role')}
                        <span className="sr-only">Role: </span>
                        {staffMember.role.replace('_', ' ')}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        onClick={() => setEditingStaff(staffMember)}
                        className="text-indigo-600 hover:text-indigo-900 mr-4"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => deleteStaffMember(staffMember.id)}
                        className="text-red-600 hover:text-red-900"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
      
      {/* Edit staff modal */}
      {editingStaff && (
        <div className="fixed inset-0 z-50 overflow-y-auto bg-gray-500 bg-opacity-75 flex items-center justify-center">
          <div className="bg-white rounded-lg overflow-hidden shadow-xl max-w-lg w-full">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Edit Staff Member</h3>
            </div>
            
            <div className="px-6 py-4">
              <div className="space-y-4">
                <div>
                  <label htmlFor="edit-staff-name" className="block text-sm font-medium text-gray-700">Name</label>
                  <input
                    type="text"
                    id="edit-staff-name"
                    value={editingStaff.name}
                    onChange={(e) => setEditingStaff({...editingStaff, name: e.target.value})}
                    className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
                
                <div>
                  <label htmlFor="edit-staff-email" className="block text-sm font-medium text-gray-700">Email</label>
                  <input
                    type="email"
                    id="edit-staff-email"
                    value={editingStaff.email}
                    onChange={(e) => setEditingStaff({...editingStaff, email: e.target.value})}
                    className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
                
                <div>
                  <label htmlFor="edit-staff-role" className="block text-sm font-medium text-gray-700">Role</label>
                  <select
                    id="edit-staff-role"
                    value={editingStaff.role}
                    onChange={(e) => setEditingStaff({...editingStaff, role: e.target.value})}
                    className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  >
                    <option value="admin">Admin</option>
                    <option value="doctor">Doctor</option>
                    <option value="receptionist">Receptionist</option>
                    <option value="nurse">Nurse</option>
                    <option value="lab_technician">Lab Technician</option>
                  </select>
                </div>
                
                <div>
                  <label htmlFor="edit-staff-password" className="block text-sm font-medium text-gray-700">
                    New Password (leave blank to keep unchanged)
                  </label>
                  <input
                    type="password"
                    id="edit-staff-password"
                    value={editingStaff.password || ''}
                    onChange={(e) => setEditingStaff({...editingStaff, password: e.target.value || null})}
                    className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
              </div>
            </div>
            
            <div className="px-6 py-4 bg-gray-50 flex justify-end space-x-3">
              <button
                onClick={() => setEditingStaff(null)}
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </button>
              <button
                onClick={updateStaffMember}
                className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
              >
                Save Changes
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
