import { withRole } from '../../../lib/auth';
import { 
  updateDoctorBookingLimits, 
  getDoctorBookingLimits, 
  getAllDoctorsBookingLimits 
} from '../../../lib/firebase';
import redisClient from '../../../lib/redis_client';
import { logger } from '../../../lib/logger';

/**
 * API endpoint for managing doctor booking limits
 * Accessible by admin and receptionist roles
 */
export default withRole(async (req, res) => {
  const hospitalId = req.user?.hospital_id;
  
  if (!hospitalId) {
    return res.status(401).json({ 
      success: false, 
      message: 'Hospital ID missing from user context' 
    });
  }

  try {
    switch (req.method) {
      case 'GET':
        return await handleGetBookingLimits(req, res, hospitalId);
      case 'PUT':
        return await handleUpdateBookingLimits(req, res, hospitalId);
      default:
        res.setHeader('Allow', ['GET', 'PUT']);
        return res.status(405).json({ 
          success: false, 
          message: `Method ${req.method} Not Allowed` 
        });
    }
  } catch (error) {
    logger.error('[API_BOOKING_LIMITS] Unexpected error:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
}, ['admin', 'receptionist']); // Allow both admin and receptionist roles

/**
 * Handle GET request - fetch booking limits for doctors
 */
async function handleGetBookingLimits(req, res, hospitalId) {
  try {
    const { doctorId } = req.query;
    
    if (doctorId) {
      // Get specific doctor's booking limits
      logger.info(`[API_BOOKING_LIMITS] Fetching limits for doctor ${doctorId} in hospital ${hospitalId}`);
      
      const result = await getDoctorBookingLimits(hospitalId, doctorId);
      
      if (result.success) {
        // Also get current Redis status if available
        try {
          const redisStatus = await redisClient.getBookingAvailabilityStatus(hospitalId, doctorId);
          result.data.redisStatus = redisStatus.success ? redisStatus.data : null;
        } catch (redisError) {
          logger.warn('[API_BOOKING_LIMITS] Redis status check failed:', redisError);
          result.data.redisStatus = null;
        }
        
        return res.status(200).json(result);
      } else {
        return res.status(404).json(result);
      }
    } else {
      // Get all doctors' booking limits for this hospital
      logger.info(`[API_BOOKING_LIMITS] Fetching limits for all doctors in hospital ${hospitalId}`);
      
      const result = await getAllDoctorsBookingLimits(hospitalId);
      
      if (result.success) {
        // Add Redis connectivity status
        try {
          const isRedisConnected = await redisClient.isRedisConnected();
          result.redisConnected = isRedisConnected;
        } catch (redisError) {
          logger.warn('[API_BOOKING_LIMITS] Redis connectivity check failed:', redisError);
          result.redisConnected = false;
        }
        
        return res.status(200).json(result);
      } else {
        return res.status(500).json(result);
      }
    }
  } catch (error) {
    logger.error('[API_BOOKING_LIMITS] Error in GET handler:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to fetch booking limits',
      error: error.message 
    });
  }
}

/**
 * Handle PUT request - update booking limits for a doctor
 */
async function handleUpdateBookingLimits(req, res, hospitalId) {
  try {
    const { doctorId, dailyLimits } = req.body;
    
    if (!doctorId || !dailyLimits) {
      return res.status(400).json({ 
        success: false, 
        message: 'Doctor ID and daily limits are required' 
      });
    }
    
    logger.info(`[API_BOOKING_LIMITS] Updating limits for doctor ${doctorId} in hospital ${hospitalId}`);
    
    // Get current limits for comparison
    const currentLimitsResult = await getDoctorBookingLimits(hospitalId, doctorId);
    const oldLimits = currentLimitsResult.success ? currentLimitsResult.data.dailyLimits : null;
    
    // Update limits in Firebase
    const updateResult = await updateDoctorBookingLimits(hospitalId, doctorId, dailyLimits);
    
    if (!updateResult.success) {
      return res.status(400).json(updateResult);
    }
    
    // Trigger Redis sync with voice agent
    let syncStatus = { status: 'pending', message: 'Initiating sync...' };
    
    try {
      logger.info(`[API_BOOKING_LIMITS] Triggering Redis sync for hospital ${hospitalId}`);
      const syncResult = await redisClient.triggerBookingLimitRefresh(hospitalId);
      
      if (syncResult.success) {
        syncStatus = { 
          status: 'success', 
          message: 'Voice agent cache updated successfully',
          details: syncResult.data 
        };
        logger.info('[API_BOOKING_LIMITS] Redis sync completed successfully');
      } else {
        syncStatus = { 
          status: 'error', 
          message: syncResult.error || 'Failed to sync with voice agent',
          details: syncResult 
        };
        logger.warn('[API_BOOKING_LIMITS] Redis sync failed:', syncResult.error);
      }
    } catch (syncError) {
      syncStatus = { 
        status: 'error', 
        message: 'Redis sync error: ' + syncError.message 
      };
      logger.error('[API_BOOKING_LIMITS] Redis sync error:', syncError);
    }
    
    // Get updated doctor info for response
    const updatedResult = await getDoctorBookingLimits(hospitalId, doctorId);
    const doctorInfo = updatedResult.success ? updatedResult.data : { doctorName: 'Unknown Doctor' };
    
    return res.status(200).json({ 
      success: true, 
      message: 'Booking limits updated successfully',
      data: {
        doctorId,
        doctorName: doctorInfo.doctorName,
        oldLimits,
        newLimits: dailyLimits,
        syncStatus,
        updatedAt: new Date().toISOString()
      }
    });
    
  } catch (error) {
    logger.error('[API_BOOKING_LIMITS] Error in PUT handler:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to update booking limits',
      error: error.message 
    });
  }
}
