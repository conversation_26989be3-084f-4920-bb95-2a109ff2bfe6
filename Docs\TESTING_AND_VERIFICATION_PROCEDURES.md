# Testing and Verification Procedures

## Overview

This document provides comprehensive testing and verification procedures for the Voice Agent system, covering both FastAPI endpoints (port 8000) and WebSocket functionality (port 8765).

## 🚀 Quick Start Testing

### 1. Start the Voice Agent

```bash
# Navigate to project directory
cd /path/to/VoiceHealthPortalDNT

# Start the voice agent (starts both FastAPI and WebSocket servers)
python -m uvicorn voice_agent.main:app --host 0.0.0.0 --port 8000
```

### 2. Verify Both Servers Are Running

```bash
# Test FastAPI server (port 8000)
curl http://localhost:8000/health

# Expected response:
# {"status": "healthy", "timestamp": "2024-01-01T12:00:00", "version": "1.0.0"}

# Test WebSocket server status via FastAPI
curl http://localhost:8000/health/websocket

# Expected response:
# {"websocket_status": "running", "connections": 0, "uptime_seconds": 123}
```

## 🔧 Detailed Testing Procedures

### A. FastAPI Server Testing (Port 8000)

#### 1. Health Check Endpoints

```bash
# Basic health check
curl -X GET http://localhost:8000/health

# WebSocket health check
curl -X GET http://localhost:8000/health/websocket

# Detailed system status
curl -X GET http://localhost:8000/status
```

#### 2. WebSocket Control Endpoints

```bash
# Get WebSocket server status
curl -X GET http://localhost:8000/websocket/status

# Get WebSocket metrics
curl -X GET http://localhost:8000/websocket/metrics

# Restart WebSocket server (if needed)
curl -X POST http://localhost:8000/websocket/control/restart
```

#### 3. Hospital Configuration Testing

```bash
# Test hospital configuration retrieval
curl -X GET "http://localhost:8000/hospital/config?hospital_id=1"

# Test hospital validation
curl -X POST "http://localhost:8000/hospital/validate" \
  -H "Content-Type: application/json" \
  -d '{"hospital_id": "1"}'
```

### B. WebSocket Server Testing (Port 8765)

#### 1. Connection Testing with wscat

Install wscat if not available:
```bash
npm install -g wscat
```

Test WebSocket connection:
```bash
# Test connection to hospital 1
wscat -c ws://localhost:8765/ws/jambonz/1 -s ws.jambonz.org

# Test connection to hospital 2
wscat -c ws://localhost:8765/ws/jambonz/hospital_2 -s ws.jambonz.org
```

#### 2. Message Flow Testing

Once connected via wscat, test Jambonz message flow:

**Test Session New Message:**
```json
{
  "type": "session:new",
  "msgid": "test-msg-001",
  "call_sid": "test-call-001",
  "from": "+**********",
  "to": "+**********",
  "direction": "inbound"
}
```

**Expected Response:**
```json
{
  "type": "ack",
  "msgid": "test-msg-001",
  "verbs": [
    {
      "verb": "gather",
      "actionHook": "websocket",
      "input": ["speech", "dtmf"],
      "timeout": 10,
      "bargein": true,
      "say": {
        "text": "Welcome message in Hindi..."
      }
    }
  ]
}
```

#### 3. Hospital-Specific Routing Testing

Test that different hospital IDs route correctly:

```bash
# Terminal 1: Connect to hospital 1
wscat -c ws://localhost:8765/ws/jambonz/1 -s ws.jambonz.org

# Terminal 2: Connect to hospital 2  
wscat -c ws://localhost:8765/ws/jambonz/2 -s ws.jambonz.org

# Terminal 3: Check connection distribution
curl http://localhost:8000/websocket/metrics | jq '.hospital_stats'
```

### C. Integration Testing

#### 1. End-to-End Call Flow Testing

Create a test script to simulate complete call flow:

```bash
# Create test script
cat > test_call_flow.py << 'EOF'
import asyncio
import websockets
import json
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_call_flow():
    uri = "ws://localhost:8765/ws/jambonz/1"
    
    async with websockets.connect(uri, subprotocols=["ws.jambonz.org"]) as websocket:
        logger.info("Connected to WebSocket")
        
        # Send session:new
        session_new = {
            "type": "session:new",
            "msgid": "test-001",
            "call_sid": "call-001",
            "from": "+**********",
            "to": "+**********"
        }
        
        await websocket.send(json.dumps(session_new))
        logger.info("Sent session:new")
        
        # Receive response
        response = await websocket.recv()
        data = json.loads(response)
        logger.info(f"Received: {data}")
        
        # Send language selection
        gather_response = {
            "type": "verb:hook",
            "msgid": "test-002",
            "call_sid": "call-001",
            "verb": "gather",
            "dtmf": {"digits": "1"}
        }
        
        await websocket.send(json.dumps(gather_response))
        logger.info("Sent language selection")
        
        # Receive main menu
        response = await websocket.recv()
        data = json.loads(response)
        logger.info(f"Received main menu: {data}")

if __name__ == "__main__":
    asyncio.run(test_call_flow())
EOF

# Run the test
python test_call_flow.py
```

#### 2. Load Testing

Test multiple concurrent connections:

```bash
# Create load test script
cat > load_test.py << 'EOF'
import asyncio
import websockets
import json
import time
from concurrent.futures import ThreadPoolExecutor

async def create_connection(hospital_id, connection_id):
    uri = f"ws://localhost:8765/ws/jambonz/{hospital_id}"
    
    try:
        async with websockets.connect(uri, subprotocols=["ws.jambonz.org"]) as websocket:
            # Send session:new
            message = {
                "type": "session:new",
                "msgid": f"load-test-{connection_id}",
                "call_sid": f"call-{connection_id}",
                "from": "+**********",
                "to": "+**********"
            }
            
            await websocket.send(json.dumps(message))
            response = await websocket.recv()
            
            print(f"Connection {connection_id} to hospital {hospital_id}: SUCCESS")
            
            # Keep connection alive for 10 seconds
            await asyncio.sleep(10)
            
    except Exception as e:
        print(f"Connection {connection_id} to hospital {hospital_id}: FAILED - {e}")

async def load_test():
    tasks = []
    
    # Create 50 connections across 3 hospitals
    for i in range(50):
        hospital_id = (i % 3) + 1  # Distribute across hospitals 1, 2, 3
        task = create_connection(hospital_id, i)
        tasks.append(task)
    
    # Run all connections concurrently
    await asyncio.gather(*tasks, return_exceptions=True)

if __name__ == "__main__":
    start_time = time.time()
    asyncio.run(load_test())
    end_time = time.time()
    print(f"Load test completed in {end_time - start_time:.2f} seconds")
EOF

# Run load test
python load_test.py
```

## 🔍 Monitoring and Verification

### 1. Real-time Monitoring

Monitor the system during testing:

```bash
# Terminal 1: Monitor logs
tail -f voice_agent.log

# Terminal 2: Monitor WebSocket metrics
watch -n 2 'curl -s http://localhost:8000/websocket/metrics | jq'

# Terminal 3: Monitor system resources
htop
```

### 2. Key Metrics to Verify

#### Connection Metrics
```bash
curl -s http://localhost:8000/websocket/metrics | jq '.connection_stats'
```

Expected output:
```json
{
  "total_connections": 5,
  "active_connections": 3,
  "connections_per_hospital": {
    "1": 2,
    "2": 1
  }
}
```

#### Performance Metrics
```bash
curl -s http://localhost:8000/websocket/metrics | jq '.performance_stats'
```

Expected output:
```json
{
  "average_response_time_ms": 45.2,
  "messages_per_second": 12.5,
  "error_rate_percent": 0.1
}
```

### 3. Error Verification

Test error handling:

```bash
# Test invalid hospital ID
wscat -c ws://localhost:8765/ws/jambonz/invalid_hospital -s ws.jambonz.org

# Test malformed messages
echo '{"invalid": "json"' | wscat -c ws://localhost:8765/ws/jambonz/1 -s ws.jambonz.org

# Test connection limits (if configured)
# Run load test with more connections than max_connections setting
```

## ✅ Verification Checklist

### Pre-Production Checklist

- [ ] **FastAPI Server (8000)**
  - [ ] Health endpoints respond correctly
  - [ ] WebSocket control endpoints work
  - [ ] Hospital configuration endpoints functional
  - [ ] Error handling works properly

- [ ] **WebSocket Server (8765)**
  - [ ] Accepts connections with correct subprotocol
  - [ ] Hospital ID extraction from path works
  - [ ] Message routing by hospital ID works
  - [ ] Connection pooling functions correctly

- [ ] **Integration**
  - [ ] Both servers start/stop together
  - [ ] Lifespan management works correctly
  - [ ] Cross-server communication functional
  - [ ] Error propagation works

- [ ] **Multi-Hospital Support**
  - [ ] Different hospital IDs route correctly
  - [ ] Hospital-specific configurations load
  - [ ] Connection isolation by hospital works
  - [ ] Metrics show per-hospital breakdown

- [ ] **Performance**
  - [ ] Response times under 100ms for simple operations
  - [ ] Can handle expected concurrent connections
  - [ ] Memory usage remains stable under load
  - [ ] No connection leaks after disconnections

### Production Readiness Checklist

- [ ] **Security**
  - [ ] WebSocket connections use WSS in production
  - [ ] Hospital ID validation prevents unauthorized access
  - [ ] Error messages don't expose sensitive information
  - [ ] Rate limiting configured appropriately

- [ ] **Monitoring**
  - [ ] Logging configured for production level
  - [ ] Metrics collection enabled
  - [ ] Health check endpoints monitored
  - [ ] Alerting configured for critical failures

- [ ] **Scalability**
  - [ ] Connection limits configured appropriately
  - [ ] Resource cleanup works correctly
  - [ ] Graceful shutdown implemented
  - [ ] Load balancing considerations addressed

## 🚨 Troubleshooting Common Issues

### Issue 1: WebSocket Connection Refused

**Symptoms**: `Connection refused` when connecting to port 8765

**Solutions**:
1. Verify both servers started: `curl http://localhost:8000/health/websocket`
2. Check firewall settings: `netstat -tlnp | grep 8765`
3. Review startup logs for errors

### Issue 2: Hospital ID Not Extracted

**Symptoms**: All connections show `hospital_id: null` in metrics

**Solutions**:
1. Verify path format: `/ws/jambonz/{hospital_id}`
2. Check WebSocket manager logs for extraction errors
3. Verify hospital ID validation logic

### Issue 3: High Response Times

**Symptoms**: Response times > 200ms consistently

**Solutions**:
1. Check database connection pool settings
2. Monitor Redis connection performance
3. Review LLM API response times
4. Check for blocking operations in async code

### Issue 4: Connection Leaks

**Symptoms**: Connection count increases but never decreases

**Solutions**:
1. Verify connection cleanup in finally blocks
2. Check for uncaught exceptions preventing cleanup
3. Monitor connection pool metrics
4. Review WebSocket close handling

## 📊 Performance Benchmarks

### Expected Performance Targets

- **Connection Establishment**: < 50ms
- **Message Processing**: < 100ms
- **Concurrent Connections**: 1000+ per server
- **Memory Usage**: < 512MB for 1000 connections
- **CPU Usage**: < 50% under normal load

### Benchmark Commands

```bash
# Connection establishment benchmark
time wscat -c ws://localhost:8765/ws/jambonz/1 -s ws.jambonz.org --close

# Message processing benchmark
# (Use the load_test.py script with timing measurements)

# Memory usage monitoring
ps aux | grep "python.*voice_agent" | awk '{print $6}'
```

This comprehensive testing guide ensures your Voice Agent system is production-ready and performs optimally in multi-hospital environments.
