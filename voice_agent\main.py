import os
import json
import async<PERSON>
import uuid
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, Optional
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, Request, Depends
from fastapi.middleware.cors import CORSMiddleware
import logging

# Import WebSocket components
from .websocket_server import websocket_server
from .websocket_config import websocket_config
from .websocket_metrics import metrics_collector
from .websocket_call_context import WebSocketCallContext, websocket_context_manager
from .websocket_handlers import message_handler
from .websocket_response_builder import response_builder

# Import existing components
from .call_context import CallContext # Import new CallContext
from .database import get_firestore_db, get_postgres_connection, get_doctors, get_tests
from .models import HospitalConfig, SIPTrunk
from .nlp import process_speech
from .utils import create_ssh_tunnel, close_ssh_tunnel, record_call
from .semantic_integration import semantic_integration, process_query
from .language_config import (
    language_config, get_primary_language, get_supported_languages,
    detect_language, get_speech_code, get_language_name
)
# Import new LLM integration
from .llm_integration import voice_llm_integration, process_voice_input, extract_booking_intent

# Import API endpoints for configuration management
from .api_endpoints import router as api_router

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Enhanced lifespan event handler for FastAPI application with WebSocket support.
    Replaces the deprecated @app.on_event("startup") and @app.on_event("shutdown") decorators.
    """
    # Startup logic
    logger.info("Starting Megha Voice Agent with WebSocket and Semantic Caching")

    # Test Redis connection using shared Redis implementation
    try:
        adapter = get_python_adapter()
        async_client = adapter.connection_manager.get_async_client()
        if async_client:
            await async_client.ping()
            logger.info("Shared Redis connection successful")
        else:
            logger.error("Failed to get shared Redis async client")
    except Exception as e:
        logger.error(f"Shared Redis connection failed: {e}")

    # Proactively test async Redis pools used by semantic integration
    try:
        logger.info("Testing semantic integration async Redis connections...")
        redis_ready = await semantic_integration.ensure_async_pool_ready()
        if redis_ready:
            logger.info("✅ Semantic integration async Redis pools are ready")
        else:
            logger.error("❌ Semantic integration async Redis pools failed readiness test")
            logger.warning("⚠️ This may cause runtime failures during voice interactions")
    except Exception as e:
        logger.error(f"❌ Error testing semantic integration async Redis pools: {e}")
        logger.warning("⚠️ Async Redis connections may fail during runtime")

    # Start WebSocket server and metrics collection
    try:
        logger.info("Starting WebSocket server for Jambonz integration...")

        # Start metrics collection
        if websocket_config.enable_metrics:
            await metrics_collector.start_collection(int(websocket_config.metrics_interval))
            logger.info("✅ WebSocket metrics collection started")

        # Start WebSocket server
        await websocket_server.start()
        logger.info(f"✅ WebSocket server started on {websocket_config.host}:{websocket_config.port}")
        logger.info(f"WebSocket subprotocol: {websocket_config.jambonz_subprotocol}")
        logger.info(f"Max concurrent connections: {websocket_config.max_connections}")

        # Store server reference in app state for access from other endpoints
        app.state.websocket_server = websocket_server
        app.state.websocket_config = websocket_config
        app.state.metrics_collector = metrics_collector

    except Exception as e:
        logger.error(f"❌ Failed to start WebSocket server: {e}")
        raise

    # Initialize semantic engine warm-up in the background (non-blocking)
    try:
        async def warmup_semantic_cache():
            try:
                logger.info("Starting semantic cache warm-up process...")

                # Load hospital data dynamically from Firebase
                hospitals_data = await load_hospitals_for_semantic_cache()
                if hospitals_data:
                    logger.info(f"Loaded {len(hospitals_data)} hospitals, starting cache warm-up...")

                    # Warm up the semantic cache
                    success = await semantic_integration.warm_up_cache(hospitals_data)

                    if success:
                        logger.info(f"✅ Semantic cache warmed up successfully for {len(hospitals_data)} hospitals")

                        # Log summary of loaded data
                        total_doctors = sum(len(h.get('doctors', [])) for h in hospitals_data)
                        total_tests = sum(len(h.get('tests', [])) for h in hospitals_data)
                        logger.info(f"Cache contains: {total_doctors} doctors, {total_tests} tests across all hospitals")

                        # Initialize date-aware integration for hospitals
                        try:
                            from .semantic_integration import initialize_date_aware_for_hospital, is_date_aware_enabled

                            date_aware_initialized = 0
                            for hospital_data in hospitals_data:
                                hospital_id = hospital_data.get('id')
                                hospital_timezone = hospital_data.get('timezone', 'Asia/Kolkata')

                                if hospital_id:
                                    if initialize_date_aware_for_hospital(hospital_id, hospital_timezone):
                                        date_aware_initialized += 1
                                        logger.info(f"✅ Date-aware integration initialized for hospital {hospital_id}")
                                    else:
                                        logger.warning(f"⚠️ Failed to initialize date-aware integration for hospital {hospital_id}")

                            if date_aware_initialized > 0:
                                logger.info(f"✅ Date-aware integration initialized for {date_aware_initialized}/{len(hospitals_data)} hospitals")
                            else:
                                logger.warning("⚠️ Date-aware integration not available or failed for all hospitals")

                        except Exception as e:
                            logger.warning(f"⚠️ Error initializing date-aware integration: {e}")
                    else:
                        logger.warning("⚠️ Semantic cache warm-up completed with some failures")
                else:
                    logger.warning("⚠️ No hospital data found for semantic cache warm-up")
            except Exception as e:
                logger.error(f"❌ Error warming up semantic cache: {e}")

        app.state.semantic_warmup_task = asyncio.create_task(warmup_semantic_cache())
        logger.info("Started semantic cache warm-up in background task")
    except Exception as e:
        logger.error(f"Error launching semantic cache warm-up task: {e}")

    # Start booking limit scheduler for daily cache refresh
    try:
        from .booking_limit_scheduler import booking_scheduler
        await booking_scheduler.start_scheduler()
        logger.info("✅ Booking limit scheduler started - daily refresh at 12:00 PM")
    except Exception as e:
        logger.error(f"❌ Error starting booking limit scheduler: {e}")

    # Yield control to the application
    yield

    # Shutdown logic
    logger.info("Shutting down Megha Voice Agent with WebSocket")

    # Stop WebSocket server and metrics collection
    try:
        logger.info("Stopping WebSocket server...")

        # Stop metrics collection
        if websocket_config.enable_metrics:
            await metrics_collector.stop_collection()
            logger.info("✅ WebSocket metrics collection stopped")

        # Stop WebSocket server
        await websocket_server.stop()
        logger.info("✅ WebSocket server stopped")

    except Exception as e:
        logger.error(f"❌ Error stopping WebSocket server: {e}")

    # Stop booking limit scheduler
    try:
        from .booking_limit_scheduler import booking_scheduler
        await booking_scheduler.stop_scheduler()
        logger.info("✅ Booking limit scheduler stopped")
    except Exception as e:
        logger.error(f"❌ Error stopping booking limit scheduler: {e}")

    # Cancel and await the semantic warmup task if it's still running
    task = getattr(app.state, "semantic_warmup_task", None)
    if task:
        task.cancel()
        try:
            await task
        except asyncio.CancelledError:
            logger.info("Semantic warmup task cancelled successfully")
        except Exception as e:
            logger.error(f"Error during semantic warmup task cancellation: {e}")

    # Close all active SSH tunnels
    for hospital_id, tunnel in active_tunnels.items():
        try:
            close_ssh_tunnel(tunnel)
            logger.info(f"Closed SSH tunnel for hospital {hospital_id}")
        except Exception as e:
            logger.error(f"Error closing SSH tunnel for hospital {hospital_id}: {e}")

    # Redis connections are managed by the centralized pool
    # No explicit cleanup needed as the pool handles connection lifecycle
    logger.info("Redis connections managed by centralized pool - no explicit cleanup needed")

app = FastAPI(title="Megha Voice Agent", lifespan=lifespan)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API router for configuration management
app.include_router(api_router)

# Use shared Redis implementation for all Redis operations
# This provides enhanced features with IndicBERT semantic caching
from shared.redis.adapters.python_adapter import get_python_adapter

# Keep track of active SSH tunnels
active_tunnels = {}

async def load_hospitals_for_semantic_cache():
    """
    Load hospital data from Firebase for semantic cache warm-up.
    Returns a list of hospital configurations with doctors and tests data.
    Production-ready with error handling, retry logic, and dynamic data loading.
    """
    max_retries = 3
    base_delay = 2  # seconds

    for attempt in range(max_retries):
        retry_delay = base_delay * (2 ** attempt)  # Exponential backoff: 2, 4, 8 seconds
        try:
            db = get_firestore_db()
            hospitals_data = []

            # Get all hospital documents from Firebase with timeout
            hospitals_collection = db.collection('hospitals')
            loop = asyncio.get_running_loop()
            # Off-load the blocking Firestore call to the default executor
            hospital_docs = await loop.run_in_executor(None, hospitals_collection.stream)

            for hospital_doc in hospital_docs:
                try:
                    hospital_id = None
                    hospital_config = hospital_doc.to_dict()

                    # Extract hospital ID from document ID (format: hospital_{id}_data)
                    doc_id = hospital_doc.id
                    if doc_id.startswith('hospital_') and doc_id.endswith('_data'):
                        hospital_id = doc_id.replace('hospital_', '').replace('_data', '')
                    else:
                        # Fallback: try to get ID from document data
                        hospital_id = hospital_config.get('id')

                    if not hospital_id:
                        logger.warning(f"Skipping hospital document {doc_id}: no valid hospital ID found")
                        continue

                    # Get supported languages for this hospital (with Hindi as primary)
                    hospital_languages = hospital_config.get('languages', language_config.get_supported_languages())

                    # Ensure Hindi is first if available
                    if "hi" in hospital_languages and hospital_languages[0] != "hi":
                        hospital_languages = ["hi"] + [lang for lang in hospital_languages if lang != "hi"]

                    # Load doctors data for this hospital
                    doctors_data = []
                    try:
                        doctors = await get_doctors(hospital_id, force_refresh=True)
                        for doctor in doctors:
                            # Validate doctor data before adding
                            if not doctor.get('name'):
                                logger.warning(f"Skipping doctor with missing name in hospital {hospital_id}: {doctor}")
                                continue

                            # Transform doctor data for semantic cache
                            doctor_info = {
                                "id": doctor.get('id', ''),
                                "name": doctor.get('name', 'Unknown Doctor'),
                                "specialty": doctor.get('specialty', 'General Medicine'),
                                "schedule": doctor.get('schedule', {}),
                                "availability": doctor.get('availability', {}),
                                "price": doctor.get('price', 0.0)
                            }
                            doctors_data.append(doctor_info)

                        logger.info(f"Loaded {len(doctors_data)} doctors for hospital {hospital_id}")
                    except Exception as e:
                        logger.error(f"Error loading doctors for hospital {hospital_id}: {e}")
                        # Continue with empty doctors list - system should still work

                    # Load tests data for this hospital
                    tests_data = []
                    try:
                        tests = await get_tests(hospital_id, force_refresh=True)
                        for test in tests:
                            # Validate test data before adding
                            if not test.get('name'):
                                logger.warning(f"Skipping test with missing name in hospital {hospital_id}: {test}")
                                continue

                            # Transform test data for semantic cache
                            cost = test.get('cost', 0.0)
                            # Ensure cost is numeric
                            try:
                                cost = float(cost) if cost is not None else 0.0
                            except (ValueError, TypeError):
                                logger.warning(f"Invalid cost for test {test.get('name')} in hospital {hospital_id}: {cost}")
                                cost = 0.0

                            test_info = {
                                "id": test.get('id', ''),
                                "name": test.get('name', 'Unknown Test'),
                                "description": test.get('description', ''),
                                "duration": test.get('duration', 30),
                                "cost": cost,
                                "price": f"₹{cost}",  # Format price for display
                                "requirements": test.get('requirements', '')
                            }
                            tests_data.append(test_info)

                        logger.info(f"Loaded {len(tests_data)} tests for hospital {hospital_id}")
                    except Exception as e:
                        logger.error(f"Error loading tests for hospital {hospital_id}: {e}")
                        # Continue with empty tests list - system should still work

                    # Create hospital data structure for semantic cache
                    hospital_data = {
                        "id": hospital_id,
                        "name": hospital_config.get('name', f'Hospital {hospital_id}'),
                        "languages": hospital_languages,
                        "doctors": doctors_data,
                        "tests": tests_data,
                        "settings": hospital_config.get('settings', {}),
                        "address": hospital_config.get('address', ''),
                        "phone": hospital_config.get('phone', ''),
                        "emergency_number": hospital_config.get('emergency_number', '911')
                    }

                    hospitals_data.append(hospital_data)
                    logger.info(f"Successfully loaded hospital {hospital_id} with {len(doctors_data)} doctors and {len(tests_data)} tests")

                except Exception as e:
                    logger.error(f"Error processing hospital document {hospital_doc.id}: {e}")
                    continue

            logger.info(f"Successfully loaded {len(hospitals_data)} hospitals for semantic cache")
            return hospitals_data

        except Exception as e:
            logger.error(f"Attempt {attempt + 1}/{max_retries} failed loading hospitals from Firebase: {e}")
            if attempt == max_retries - 1:
                # Last attempt failed
                logger.error("All attempts failed to load hospitals from Firebase")
                return []
            else:
                # Wait before retry with exponential backoff
                await asyncio.sleep(retry_delay)
                continue

    # This should never be reached, but just in case
    return []

# Startup and shutdown logic moved to lifespan function above

async def get_hospital_config(hospital_id: str) -> HospitalConfig:
    """
    Get hospital configuration from Firestore and establish SSH tunnel if needed
    """
    logger.info(f"Getting hospital configuration for hospital ID: {hospital_id}")
    db = get_firestore_db()
    
    # Path for hospital configuration document is now hospital_{hospital_id}_data
    hospital_doc_id = f'hospital_{hospital_id}_data' # Updated document ID
    logger.info(f"Looking for hospital document at hospitals/{hospital_doc_id}") # Updated logging
    
    try:
        # Get the hospital document from the 'hospitals' collection in a non-blocking way
        loop = asyncio.get_running_loop()
        hospital_doc = await loop.run_in_executor(None, lambda: db.collection('hospitals').document(hospital_doc_id).get())
        
        if hospital_doc.exists:
            logger.info(f"Found hospital config at hospitals/{hospital_doc_id}") # Updated logging
            hospital_data = hospital_doc.to_dict()
            logger.info(f"Hospital data: {hospital_data}")
            
            # If hospital_data doesn't have an 'id' field, add it
            if 'id' not in hospital_data:
                hospital_data['id'] = hospital_id
                
            try:
                config = HospitalConfig(**hospital_data)
                
                # Check if SSH tunnel is needed and not already active
                if hasattr(config, 'ssh_tunnel') and config.ssh_tunnel and hospital_id not in active_tunnels:
                    try:
                        # Create SSH tunnel for PostgreSQL connection with robust port management
                        tunnel = create_ssh_tunnel(
                            ssh_host=config.ssh_tunnel.get('host'),
                            ssh_user=config.ssh_tunnel.get('user'),
                            ssh_private_key=config.ssh_tunnel.get('private_key_path'),
                            remote_host='localhost',  # Assume PostgreSQL is on the same server
                            remote_port=5432,
                            hospital_id=hospital_id  # Let port manager handle port allocation
                        )
                        active_tunnels[hospital_id] = tunnel
                        logger.info(f"Established SSH tunnel for hospital {hospital_id}")
                    except Exception as tunnel_err:
                        logger.error(f"Error creating SSH tunnel: {tunnel_err}")
                        # Continue without tunnel
                
                return config
            except Exception as config_err:
                logger.error(f"Error creating hospital config: {config_err}")
                
        else:
            logger.error(f"Hospital document {hospital_doc_id} does not exist in 'hospitals' collection") # Updated logging (implicitly via hospital_doc_id)
    except Exception as e:
        logger.error(f"Error getting hospital config: {e}")
    
    # If we get here, we couldn't find a valid hospital configuration
    # Updated error message to reflect the new document path format
    logger.error(f"Hospital configuration document 'hospitals/{hospital_doc_id}' not found.")
    
    # Instead of returning a fake hospital config, raise a proper exception
    # This will be caught by the try/except in the calling functions
    # and return an appropriate error message to the caller
    raise HTTPException(
        status_code=404,
        detail=f"Hospital {hospital_id} configuration (hospitals/{hospital_doc_id}) not found. Please contact support." # Updated detail
    )

# Removed old get_call_context and update_call_context functions
# as CallContext class now handles its own persistence.

# WebSocket endpoints and monitoring - webhooks completely replaced

@app.get("/websocket/status")
async def websocket_status():
    """
    Get WebSocket server status and configuration
    """
    try:
        server_stats = await websocket_server.get_server_stats()

        return {
            "status": "running",
            "websocket_enabled": True,
            "webhook_enabled": False,  # Webhooks completely removed
            "mode": "websocket_only",
            "configuration": {
                "host": websocket_config.host,
                "port": websocket_config.port,
                "max_connections": websocket_config.max_connections,
                "subprotocol": websocket_config.jambonz_subprotocol,
                "metrics_enabled": websocket_config.enable_metrics
            },
            "server_stats": server_stats,
            "jambonz_application_url": f"wss://{websocket_config.host}:{websocket_config.port}/ws/jambonz/{{hospital_id}}"
        }
    except Exception as e:
        logger.error(f"Error getting WebSocket status: {e}")
        return {
            "status": "error",
            "error": str(e),
            "websocket_enabled": False
        }

@app.get("/websocket/metrics")
async def websocket_metrics():
    """
    Get WebSocket performance metrics
    """
    if not websocket_config.enable_metrics:
        raise HTTPException(status_code=404, detail="Metrics disabled")

    try:
        return metrics_collector.get_current_metrics()
    except Exception as e:
        logger.error(f"Error getting WebSocket metrics: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving metrics: {str(e)}")

@app.get("/websocket/metrics/summary")
async def websocket_metrics_summary():
    """
    Get WebSocket performance summary
    """
    if not websocket_config.enable_metrics:
        raise HTTPException(status_code=404, detail="Metrics disabled")

    try:
        return metrics_collector.get_performance_summary()
    except Exception as e:
        logger.error(f"Error getting WebSocket metrics summary: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving metrics summary: {str(e)}")

@app.get("/websocket/connections")
async def list_websocket_connections():
    """
    List active WebSocket connections and calls
    """
    try:
        server_stats = await websocket_server.get_server_stats()
        context_stats = websocket_context_manager.get_stats()

        return {
            "active_connections": server_stats["websocket_manager"]["active_connections"],
            "active_calls": server_stats["websocket_manager"]["active_calls"],
            "total_connections": server_stats["websocket_manager"]["total_connections"],
            "hospital_stats": server_stats["websocket_manager"]["hospital_stats"],
            "context_manager": context_stats,
            "connection_details": server_stats["websocket_manager"].get("connection_details", {})
        }
    except Exception as e:
        logger.error(f"Error listing WebSocket connections: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving connections: {str(e)}")

@app.get("/jambonz/application-url/{hospital_id}")
async def get_jambonz_application_url(hospital_id: str):
    """
    Get the WebSocket application URL for a specific hospital to configure in Jambonz
    """
    try:
        # Validate hospital exists
        hospital_config = await get_hospital_config(hospital_id)

        # Generate WebSocket URL for this hospital
        protocol = "wss" if websocket_config.host != "localhost" else "ws"
        websocket_url = f"{protocol}://{websocket_config.host}:{websocket_config.port}/ws/jambonz/{hospital_id}"

        return {
            "hospital_id": hospital_id,
            "hospital_name": hospital_config.name,
            "websocket_url": websocket_url,
            "configuration_example": {
                "call_hook": {
                    "url": websocket_url,
                    "method": "websocket"
                },
                "call_status_hook": {
                    "url": websocket_url,
                    "method": "websocket"
                }
            },
            "mode": "websocket_only",
            "subprotocol": websocket_config.jambonz_subprotocol
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating Jambonz URL for hospital {hospital_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Error generating application URL: {str(e)}")

@app.get("/health")
async def health_check():
    """
    Enhanced health check that includes WebSocket status
    """
    try:
        health_status = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "mode": "websocket_only",
            "services": {
                "websocket_server": False,
                "redis": False,
                "semantic_integration": False,
                "metrics_collection": False
            }
        }

        # Check WebSocket server
        try:
            server_stats = await websocket_server.get_server_stats()
            health_status["services"]["websocket_server"] = True
            health_status["websocket"] = {
                "running": True,
                "connections": server_stats["websocket_manager"]["active_connections"],
                "calls": server_stats["websocket_manager"]["active_calls"],
                "total_connections": server_stats["websocket_manager"]["total_connections"]
            }
        except Exception as e:
            health_status["services"]["websocket_server"] = False
            health_status["websocket"] = {"running": False, "error": str(e)}

        # Check Redis
        try:
            adapter = get_python_adapter()
            async_client = adapter.connection_manager.get_async_client()
            await async_client.ping()
            health_status["services"]["redis"] = True
        except Exception as e:
            health_status["services"]["redis"] = False
            health_status["redis_error"] = str(e)

        # Check semantic integration
        try:
            metrics = await semantic_integration.get_performance_metrics()
            health_status["services"]["semantic_integration"] = True
            health_status["semantic_integration"] = {
                "cache_size": metrics.get("cache_size", 0),
                "hit_rate": metrics.get("hit_rate", 0)
            }
        except Exception as e:
            health_status["services"]["semantic_integration"] = False
            health_status["semantic_error"] = str(e)

        # Check metrics collection
        if websocket_config.enable_metrics:
            try:
                summary = metrics_collector.get_performance_summary()
                health_status["services"]["metrics_collection"] = True
                health_status["metrics"] = summary
            except Exception as e:
                health_status["services"]["metrics_collection"] = False
                health_status["metrics_error"] = str(e)

        # Determine overall status
        all_services_healthy = all(health_status["services"].values())
        health_status["status"] = "healthy" if all_services_healthy else "degraded"

        return health_status

    except Exception as e:
        logger.error(f"Error in health check: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

@app.get("/health/websocket")
async def websocket_health():
    """
    Detailed WebSocket health check
    """
    try:
        server_stats = await websocket_server.get_server_stats()
        context_stats = websocket_context_manager.get_stats()

        return {
            "status": "healthy",
            "websocket_server": {
                "running": True,
                "host": websocket_config.host,
                "port": websocket_config.port,
                "subprotocol": websocket_config.jambonz_subprotocol
            },
            "connections": server_stats["websocket_manager"],
            "contexts": context_stats,
            "configuration": {
                "max_connections": websocket_config.max_connections,
                "heartbeat_interval": websocket_config.heartbeat_interval,
                "metrics_enabled": websocket_config.enable_metrics
            },
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Error in WebSocket health check: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

@app.post("/websocket/control/start")
async def start_websocket_server():
    """
    Start WebSocket server (admin endpoint)
    """
    try:
        if hasattr(app.state, 'websocket_server'):
            server_stats = await app.state.websocket_server.get_server_stats()
            if server_stats["websocket_manager"]["server_running"]:
                return {"status": "already_running", "message": "WebSocket server is already running"}

        # Start the server
        await websocket_server.start()
        return {"status": "started", "message": "WebSocket server started successfully"}

    except Exception as e:
        logger.error(f"Failed to start WebSocket server: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start WebSocket server: {str(e)}")

@app.post("/websocket/control/stop")
async def stop_websocket_server():
    """
    Stop WebSocket server (admin endpoint)
    """
    try:
        if hasattr(app.state, 'websocket_server'):
            await app.state.websocket_server.stop()
            return {"status": "stopped", "message": "WebSocket server stopped successfully"}
        else:
            return {"status": "not_running", "message": "WebSocket server is not running"}

    except Exception as e:
        logger.error(f"Failed to stop WebSocket server: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to stop WebSocket server: {str(e)}")

@app.post("/websocket/control/restart")
async def restart_websocket_server():
    """
    Restart WebSocket server (admin endpoint)
    """
    try:
        # Stop first
        if hasattr(app.state, 'websocket_server'):
            await app.state.websocket_server.stop()

        # Start again
        await websocket_server.start()
        return {"status": "restarted", "message": "WebSocket server restarted successfully"}

    except Exception as e:
        logger.error(f"Failed to restart WebSocket server: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to restart WebSocket server: {str(e)}")

@app.get("/websocket/metrics/history")
async def websocket_metrics_history(hours: int = 1):
    """
    Get historical WebSocket metrics
    """
    if not websocket_config.enable_metrics:
        raise HTTPException(status_code=404, detail="Metrics disabled")

    if not (1 <= hours <= 24):
        raise HTTPException(status_code=400, detail="Hours must be between 1 and 24")

    try:
        return {
            "hours": hours,
            "data": metrics_collector.get_historical_metrics(hours),
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Error getting historical metrics: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving historical metrics: {str(e)}")

# WebSocket-only endpoints - all webhook functionality removed and replaced with WebSocket

@app.get("/api/websocket/calls/active")
async def get_active_websocket_calls():
    """
    Get all active WebSocket calls across all hospitals
    """
    try:
        server_stats = await websocket_server.get_server_stats()
        context_stats = websocket_context_manager.get_stats()

        # Get active call contexts
        active_calls = await list_active_calls()

        call_details = []
        for call_id in active_calls:
            try:
                ctx = await CallContext.get_or_create(call_id=call_id)
                if ctx:
                    call_details.append({
                        "call_id": call_id,
                        "hospital_id": ctx.hospital_id,
                        "caller_number": ctx.caller_number,
                        "state": ctx.state,
                        "language": ctx.language,
                        "created_at": ctx.created_at,
                        "last_updated": ctx.last_updated
                    })
            except Exception as e:
                logger.error(f"Error getting context for call {call_id}: {e}")

        return {
            "total_active_calls": len(active_calls),
            "websocket_connections": server_stats["websocket_manager"]["active_connections"],
            "websocket_calls": server_stats["websocket_manager"]["active_calls"],
            "hospital_distribution": server_stats["websocket_manager"]["hospital_stats"],
            "call_details": call_details,
            "context_manager_stats": context_stats,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error getting active WebSocket calls: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving active calls: {str(e)}")

@app.get("/api/websocket/calls/{call_id}")
async def get_websocket_call_details(call_id: str):
    """
    Get detailed information about a specific WebSocket call
    """
    try:
        # Check if call exists in context
        ctx = await CallContext.get_or_create(call_id=call_id)
        if not ctx:
            raise HTTPException(status_code=404, detail=f"Call {call_id} not found")

        # Get WebSocket connection info if available
        connection_info = None
        try:
            server_stats = await websocket_server.get_server_stats()
            connection_details = server_stats["websocket_manager"].get("connection_details", {})
            connection_info = connection_details.get(call_id)
        except Exception as e:
            logger.warning(f"Could not get WebSocket connection info for {call_id}: {e}")

        return {
            "call_id": call_id,
            "context": {
                "hospital_id": ctx.hospital_id,
                "caller_number": ctx.caller_number,
                "state": ctx.state,
                "language": ctx.language,
                "created_at": ctx.created_at,
                "last_updated": ctx.last_updated,
                "conversation_history": ctx.conversation_history[-10:],  # Last 10 entries
                "state_history": ctx.state_history,
                "data": ctx.data,
                "entities": ctx.entities
            },
            "websocket_connection": connection_info,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting call details for {call_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving call details: {str(e)}")

@app.delete("/api/websocket/calls/{call_id}")
async def terminate_websocket_call(call_id: str):
    """
    Terminate a specific WebSocket call and clean up its context
    """
    try:
        # Check if call exists
        call_exists = await call_context_exists(call_id)
        if not call_exists:
            raise HTTPException(status_code=404, detail=f"Call {call_id} not found")

        # Terminate WebSocket connection if active
        try:
            await websocket_server.terminate_call(call_id)
        except Exception as e:
            logger.warning(f"Could not terminate WebSocket connection for {call_id}: {e}")

        # Clean up call context
        deleted = await delete_call_context(call_id)

        return {
            "call_id": call_id,
            "terminated": True,
            "context_deleted": deleted,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error terminating call {call_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Error terminating call: {str(e)}")
@app.get("/api/websocket/hospitals/{hospital_id}/calls")
async def get_hospital_websocket_calls(hospital_id: str):
    """
    Get all active WebSocket calls for a specific hospital
    """
    try:
        # Validate hospital exists
        hospital_config = await get_hospital_config(hospital_id)

        # Get all active calls
        active_calls = await list_active_calls()

        # Filter calls for this hospital
        hospital_calls = []
        for call_id in active_calls:
            try:
                ctx = await CallContext.get_or_create(call_id=call_id)
                if ctx and ctx.hospital_id == hospital_id:
                    hospital_calls.append({
                        "call_id": call_id,
                        "caller_number": ctx.caller_number,
                        "state": ctx.state,
                        "language": ctx.language,
                        "created_at": ctx.created_at,
                        "last_updated": ctx.last_updated,
                        "conversation_entries": len(ctx.conversation_history)
                    })
            except Exception as e:
                logger.error(f"Error getting context for call {call_id}: {e}")

        # Get WebSocket connection stats for this hospital
        server_stats = await websocket_server.get_server_stats()
        hospital_stats = server_stats["websocket_manager"]["hospital_stats"].get(hospital_id, {})

        return {
            "hospital_id": hospital_id,
            "hospital_name": hospital_config.name,
            "total_calls": len(hospital_calls),
            "calls": hospital_calls,
            "websocket_stats": hospital_stats,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting hospital WebSocket calls for {hospital_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving hospital calls: {str(e)}")

@app.post("/api/websocket/hospitals/{hospital_id}/broadcast")
async def broadcast_to_hospital_calls(hospital_id: str, request: Request):
    """
    Broadcast a message to all active WebSocket calls for a specific hospital
    """
    try:
        payload = await request.json()
        message = payload.get("message", "")
        message_type = payload.get("type", "announcement")

        if not message:
            raise HTTPException(status_code=400, detail="Message is required")

        # Validate hospital exists
        hospital_config = await get_hospital_config(hospital_id)

        # Get all active calls for this hospital
        active_calls = await list_active_calls()
        hospital_calls = []

        for call_id in active_calls:
            try:
                ctx = await CallContext.get_or_create(call_id=call_id)
                if ctx and ctx.hospital_id == hospital_id:
                    hospital_calls.append(call_id)
            except Exception as e:
                logger.error(f"Error checking call {call_id}: {e}")

        # Broadcast message via WebSocket
        broadcast_results = []
        for call_id in hospital_calls:
            try:
                result = await websocket_server.broadcast_to_call(call_id, {
                    "type": message_type,
                    "message": message,
                    "hospital_id": hospital_id,
                    "timestamp": datetime.now().isoformat()
                })
                broadcast_results.append({
                    "call_id": call_id,
                    "success": result,
                    "error": None
                })
            except Exception as e:
                broadcast_results.append({
                    "call_id": call_id,
                    "success": False,
                    "error": str(e)
                })

        successful_broadcasts = sum(1 for r in broadcast_results if r["success"])

        return {
            "hospital_id": hospital_id,
            "hospital_name": hospital_config.name,
            "message": message,
            "type": message_type,
            "total_calls": len(hospital_calls),
            "successful_broadcasts": successful_broadcasts,
            "results": broadcast_results,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error broadcasting to hospital {hospital_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Error broadcasting message: {str(e)}")
# All webhook endpoints removed - WebSocket-only implementation

# Remove webhook/emergency endpoint and replace with WebSocket emergency handling
# Emergency handling is now done through WebSocket messages

@app.post("/api/semantic-query")
async def semantic_query_endpoint(request: Request):
    """
    Semantic query endpoint for testing semantic engine
    """
    try:
        payload = await request.json()
        query = payload.get("query", "")
        hospital_id = payload.get("hospital_id", "default")
        language = payload.get("language", "en")

        if not query:
            raise HTTPException(status_code=400, detail="Query is required")

        # Process with semantic engine
        result = await process_query(
            query=query,
            hospital_id=hospital_id,
            language=language,
            context={"source": "api_test"}
        )

        return {
            "query": query,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in semantic query endpoint: {e}")
        raise HTTPException(status_code=500, detail=f"Error processing query: {str(e)}")

# Legacy endpoints for backward compatibility (return WebSocket info)

@app.get("/api/hospitals")
async def list_hospitals():
    """
    List all hospitals with their WebSocket configuration
    """
    try:
        # Get all hospitals from database
        hospitals = await get_all_hospitals()

        hospital_list = []
        for hospital in hospitals:
            hospital_data = {
                "hospital_id": hospital.get("id"),
                "name": hospital.get("name"),
                "websocket_url": f"wss://{websocket_config.host}:{websocket_config.port}/ws/jambonz/{hospital.get('id')}",
                "languages": hospital.get("languages", ["hi", "bn", "en"]),
                "services": hospital.get("services", ["doctor_booking", "test_booking"])
            }
            hospital_list.append(hospital_data)

        return {
            "hospitals": hospital_list,
            "total_count": len(hospital_list),
            "websocket_mode": True,
            "webhook_mode": False,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error listing hospitals: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving hospitals: {str(e)}")

@app.get("/api/hospitals/{hospital_id}")
async def get_hospital_info(hospital_id: str):
    """
    Get detailed information about a specific hospital including WebSocket configuration
    """
    try:
        # Validate hospital exists
        hospital_config = await get_hospital_config(hospital_id)

        # Get current call statistics for this hospital
        server_stats = await websocket_server.get_server_stats()
        hospital_stats = server_stats["websocket_manager"]["hospital_stats"].get(hospital_id, {})

        # Get active calls for this hospital
        active_calls = await list_active_calls()
        hospital_calls = []
        for call_id in active_calls:
            try:
                ctx = await CallContext.get_or_create(call_id=call_id)
                if ctx and ctx.hospital_id == hospital_id:
                    hospital_calls.append({
                        "call_id": call_id,
                        "caller_number": ctx.caller_number,
                        "state": ctx.state,
                        "language": ctx.language,
                        "created_at": ctx.created_at
                    })
            except Exception as e:
                logger.error(f"Error getting context for call {call_id}: {e}")

        return {
            "hospital_id": hospital_id,
            "name": hospital_config.name,
            "websocket_url": f"wss://{websocket_config.host}:{websocket_config.port}/ws/jambonz/{hospital_id}",
            "languages": getattr(hospital_config, 'languages', ["hi", "bn", "en"]),
            "services": getattr(hospital_config, 'services', ["doctor_booking", "test_booking"]),
            "current_stats": {
                "active_calls": len(hospital_calls),
                "total_connections": hospital_stats.get("total_connections", 0),
                "total_calls": hospital_stats.get("total_calls", 0)
            },
            "active_calls": hospital_calls,
            "websocket_mode": True,
            "webhook_mode": False,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting hospital info for {hospital_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving hospital info: {str(e)}")

# Helper functions for call context management

async def list_active_calls() -> list:
    """
    Get list of all active call IDs from Redis
    """
    try:
        adapter = get_python_adapter()
        async_client = adapter.connection_manager.get_async_client()

        # Get all call context keys
        keys = await async_client.keys("call_context:*")
        call_ids = [key.decode().split(":")[-1] for key in keys]

        return call_ids
    except Exception as e:
        logger.error(f"Error listing active calls: {e}")
        return []

async def call_context_exists(call_id: str) -> bool:
    """
    Check if a call context exists in Redis
    """
    try:
        adapter = get_python_adapter()
        async_client = adapter.connection_manager.get_async_client()

        exists = await async_client.exists(f"call_context:{call_id}")
        return bool(exists)
    except Exception as e:
        logger.error(f"Error checking call context existence for {call_id}: {e}")
        return False

async def delete_call_context(call_id: str) -> bool:
    """
    Delete a call context from Redis
    """
    try:
        adapter = get_python_adapter()
        async_client = adapter.connection_manager.get_async_client()

        deleted = await async_client.delete(f"call_context:{call_id}")
        return bool(deleted)
    except Exception as e:
        logger.error(f"Error deleting call context for {call_id}: {e}")
        return False

async def get_all_hospitals():
    """
    Get all hospitals from database
    """
    try:
        db = get_firestore_db()
        hospitals_data = []

        # Get all hospital documents from Firebase
        hospitals_collection = db.collection('hospitals')
        loop = asyncio.get_running_loop()
        hospital_docs = await loop.run_in_executor(None, hospitals_collection.stream)

        for hospital_doc in hospital_docs:
            hospital_config = hospital_doc.to_dict()
            # Extract hospital ID from document ID
            doc_id = hospital_doc.id
            if doc_id.startswith('hospital_') and doc_id.endswith('_data'):
                hospital_id = doc_id.replace('hospital_', '').replace('_data', '')
                hospitals_data.append({
                    "id": hospital_id,
                    "name": hospital_config.get('name', f'Hospital {hospital_id}'),
                    "languages": hospital_config.get('languages', ["hi", "bn", "en"]),
                    "services": hospital_config.get('services', ["doctor_booking", "test_booking"])
                })

        return hospitals_data
    except Exception as e:
        logger.error(f"Error getting all hospitals: {e}")
        return []

# Root endpoint

@app.get("/")
async def root():

    """
    Root endpoint showing WebSocket-only mode
    """
    return {
        "message": "Megha Voice Agent - WebSocket Mode",
        "mode": "websocket_only",
        "webhook_support": False,
        "websocket_support": True,
        "version": "2.0.0",
        "features": [
            "WebSocket-based voice calls",
            "Concurrent call handling",
            "Real-time metrics",
            "Multi-hospital support",
            "Semantic caching",
            "LLM integration"
        ],
        "endpoints": {
            "websocket_status": "/websocket/status",
            "health_check": "/health",
            "metrics": "/websocket/metrics",
            "active_calls": "/api/websocket/calls/active",
            "hospitals": "/api/hospitals"
        },
        "websocket_url_template": f"wss://{websocket_config.host}:{websocket_config.port}/ws/jambonz/{{hospital_id}}",
        "timestamp": datetime.now().isoformat()
    }

# All webhook endpoints removed - WebSocket-only implementation

# Legacy API endpoints for backward compatibility (return WebSocket info)



@app.get("/api/performance-metrics")
async def get_performance_metrics():
    """
    Get performance metrics for the semantic integration.
    """
    try:
        metrics = await semantic_integration.get_performance_metrics()
        return metrics
    except Exception as e:
        logger.error(f"Error getting performance metrics: {e}")
        return {"error": str(e)}

@app.post("/api/preload-hospital/{hospital_id}")
async def preload_hospital_cache_endpoint(hospital_id: str, request: Request):

    """
    Preload semantic cache for a specific hospital.
    """
    try:
        hospital_data = await request.json()
        success = await semantic_integration.preload_hospital_cache(hospital_id, hospital_data)

        return {
            "hospital_id": hospital_id,
            "success": success,
            "message": "Cache preloaded successfully" if success else "Failed to preload cache"
        }

    except Exception as e:
        logger.error(f"Error preloading hospital cache: {e}")
        return {"error": str(e)}

@app.post("/api/booking-limits/refresh")
async def manual_booking_limit_refresh(request: Request):
    """
    Manually trigger booking limit refresh for testing or immediate updates.
    """
    try:
        payload = await request.json()
        hospital_id = payload.get("hospital_id")  # Optional: specific hospital

        from .booking_limit_scheduler import booking_scheduler
        result = await booking_scheduler.manual_refresh(hospital_id)

        return result

    except Exception as e:
        logger.error(f"Error in manual booking limit refresh: {e}")
        return {"success": False, "error": str(e)}

@app.get("/api/booking-limits/status/{hospital_id}/{doctor_id}")
async def get_booking_limit_status(hospital_id: str, doctor_id: str, date: str = None):
    """
    Get current booking limit status for a doctor.
    """
    try:
        from datetime import datetime
        if not date:
            date = datetime.now().strftime('%Y-%m-%d')

        from shared.redis.migration_helper import get_shared_redis_manager
        redis_manager = get_shared_redis_manager()
        availability = await redis_manager.check_booking_availability_redis_async(
            hospital_id, doctor_id, date
        )

        return {
            "hospital_id": hospital_id,
            "doctor_id": doctor_id,
            "date": date,
            "availability": availability,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error getting booking limit status: {e}")
        return {"error": str(e)}




@app.post("/api/availability/refresh")
async def manual_availability_refresh(request: Request):
    """
    Manually trigger availability refresh for testing or immediate updates.
    """
    try:
        payload = await request.json()
        hospital_id = payload.get("hospital_id")  # Optional: specific hospital
        date = payload.get("date")  # Optional: specific date

        logger.info(f"Manual availability refresh requested for hospital: {hospital_id or 'all'}, date: {date or 'next 7 days'}")

        from .booking_limit_scheduler import booking_scheduler

        if hospital_id:
            # Refresh specific hospital
            doctors_refreshed = await booking_scheduler._refresh_hospital_booking_limits(hospital_id)
            await booking_scheduler._refresh_hospital_test_availability(hospital_id)

            return {
                "success": True,
                "hospital_id": hospital_id,
                "doctors_refreshed": doctors_refreshed,
                "message": f"Refreshed availability for {doctors_refreshed} doctors and all tests"
            }
        else:
            # Refresh all hospitals
            await booking_scheduler._refresh_all_booking_limits()
            return {
                "success": True,
                "message": "Manual availability refresh completed for all hospitals"
            }

    except Exception as e:
        logger.error(f"Error in manual availability refresh: {e}")
        return {"success": False, "error": str(e)}

@app.post("/api/availability/sync-from-staff-portal")
async def sync_availability_from_staff_portal(request: Request):
    """
    Sync availability data from staff portal to date-aware Redis cache.
    This endpoint is called by the staff portal when availability changes.
    """
    try:
        payload = await request.json()
        hospital_id = payload.get("hospital_id")
        availability_data = payload.get("availability_data", {})

        if not hospital_id:
            raise HTTPException(status_code=400, detail="hospital_id is required")

        if not availability_data:
            raise HTTPException(status_code=400, detail="availability_data is required")

        logger.info(f"Syncing availability data from staff portal for hospital {hospital_id}")

        # Import date-aware functions
        from .semantic_integration import sync_hospital_availability, is_date_aware_enabled, get_date_aware_hospital

        # Check if date-aware integration is available for this hospital
        if not is_date_aware_enabled():
            logger.warning(f"Date-aware integration not enabled, falling back to standard availability refresh")

            # Fallback to standard availability refresh
            from .booking_limit_scheduler import booking_scheduler
            doctors_refreshed = await booking_scheduler._refresh_hospital_booking_limits(hospital_id)

            return {
                "success": True,
                "hospital_id": hospital_id,
                "method": "standard_refresh",
                "doctors_refreshed": doctors_refreshed,
                "message": "Availability refreshed using standard method (date-aware not available)"
            }

        current_hospital = get_date_aware_hospital()
        if current_hospital != hospital_id:
            logger.warning(f"Date-aware integration is for hospital {current_hospital}, but request is for {hospital_id}")

            # Fallback to standard refresh
            from .booking_limit_scheduler import booking_scheduler
            doctors_refreshed = await booking_scheduler._refresh_hospital_booking_limits(hospital_id)

            return {
                "success": True,
                "hospital_id": hospital_id,
                "method": "standard_refresh",
                "doctors_refreshed": doctors_refreshed,
                "message": f"Availability refreshed using standard method (date-aware is for different hospital)"
            }

        # Use date-aware sync
        success = await sync_hospital_availability(hospital_id, availability_data)

        if success:
            logger.info(f"✅ Successfully synced availability data for hospital {hospital_id}")
            return {
                "success": True,
                "hospital_id": hospital_id,
                "method": "date_aware_sync",
                "message": "Availability data synced successfully with date-aware cache"
            }
        else:
            logger.error(f"❌ Failed to sync availability data for hospital {hospital_id}")
            return {
                "success": False,
                "hospital_id": hospital_id,
                "error": "Failed to sync availability data"
            }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error syncing availability from staff portal: {e}")
        raise HTTPException(status_code=500, detail=f"Error syncing availability: {str(e)}")

def extract_hospital_id_from_did(did: str) -> Optional[str]:
    """
    Extracts the hospital ID from the DID (phone number).
    The hospital ID is assumed to be the last 10 digits of the numeric part of the DID.
    """
    logger.info(f"Attempting to extract hospital ID from DID: {did}")

    if not did:
        logger.warning("DID is None or empty, cannot extract hospital ID.")
        return None

    # Clean the DID to get only numeric characters
    cleaned_did = ''.join(filter(str.isdigit, did))
    logger.info(f"Cleaned DID for hospital ID extraction: {cleaned_did}")

    # Check if the cleaned DID has at least 10 digits
    if len(cleaned_did) >= 10:
        # Take the last 10 digits as the hospital_id
        hospital_id = cleaned_did[-10:]
        logger.info(f"Extracted 10-digit hospital ID: {hospital_id} from DID: {did} (cleaned: {cleaned_did})")
        return hospital_id
    else:
        logger.warning(
            f"Cleaned DID '{cleaned_did}' (from original DID '{did}') is too short "
            f"to extract a 10-digit hospital ID. A minimum of 10 digits is required."
        )
        return None

# Note: get_speech_code and get_language_name are imported from language_config
# No need to redefine them locally as they are already available

async def get_translated_text(text: str, language: str) -> str:
    """
    Get translated text for the selected language
    Fetches translations from Firebase if available, otherwise uses Google Translate API
    """
    if language == 'en':
        # No need to translate if the language is English
        return text
        
    try:
        # Try to fetch translation from Firebase first
        db = get_firestore_db()
        loop = asyncio.get_running_loop()
        # Off-load the blocking Firestore call to the default executor
        translations_ref = await loop.run_in_executor(None, db.collection('translations').document(language).get)
        
        if translations_ref.exists:
            translations = translations_ref.to_dict()
            # Check if this specific text has a translation
            if text in translations:
                return translations[text]
        
        # If we reach here, no translation was found in Firebase
        # Use Google Translate API instead (or your preferred translation service)
        # This section would integrate with a translation API
        # For now, we'll use a fallback with some common translations
    
    except Exception as e:
        logger.error(f"Error in translation: {e}")
    
    # If all translation attempts fail, return original text
    return text

def determine_menu_selection(speech: str, dtmf: str) -> int:
    """
    Determine menu selection from speech or DTMF input
    """
    if dtmf == '1':
        return 1
    elif dtmf == '2':
        return 2

    speech = speech.lower()
    if '1' in speech or 'one' in speech or 'doctor' in speech or 'appointment' in speech:
        return 1
    elif '2' in speech or 'two' in speech or 'test' in speech:
        return 2

    # Default to 0 for invalid selection
    return 0

def determine_booking_limit_choice(speech: str, dtmf: str) -> int:
    """
    Determine choice when booking limit is reached:
    1 = Choose different doctor
    2 = Book on next available date
    """
    if dtmf == '1':
        return 1
    elif dtmf == '2':
        return 2

    speech = speech.lower()
    if '1' in speech or 'one' in speech or 'different' in speech or 'other' in speech or 'another' in speech:
        return 1
    elif '2' in speech or 'two' in speech or 'next' in speech or 'available' in speech or 'date' in speech:
        return 2

    # Default to 0 for invalid selection
    return 0





async def format_doctor_options(doctors: list, language: str) -> str:
    """
    Format doctor options for speech output
    """
    if not doctors:
        return await get_translated_text("No doctors are currently available.", language)
    
    # Create a string with doctor options
    options = []
    for doctor in doctors:
        option = f"{doctor['number']} for {doctor['name']}, {doctor.get('specialty', '')}"
        options.append(option)
    
    return ", ".join(options)

async def format_test_options(tests: list, language: str) -> str:
    """
    Format test options for speech output
    """
    if not tests:
        return await get_translated_text("No tests are currently available.", language)
    
    # Create a string with test options
    options = []
    for test in tests:
        option = f"{test['number']} for {test['name']}"
        options.append(option)
    
    return ", ".join(options)

def match_doctor_selection(speech: str, dtmf: str, doctors: list) -> Optional[dict]:
    """
    Match user input to a doctor from the list
    """
    if not doctors:
        return None
    
    # Check DTMF input first
    if dtmf:
        try:
            selection = int(dtmf)
            for doctor in doctors:
                if doctor['number'] == selection:
                    return doctor
        except ValueError:
            pass
    
    # Check speech input
    speech = speech.lower()
    best_match = None
    best_score = 0
    
    for doctor in doctors:
        # Check for doctor name match
        name_score = fuzzy_match_score(speech, doctor['name'].lower())
        # Check for specialty match
        specialty_score = fuzzy_match_score(speech, doctor.get('specialty', '').lower())
        
        # Use the higher of the two scores
        score = max(name_score, specialty_score)
        if score > best_score and score > 0.7:  # 70% match threshold
            best_score = score
            best_match = doctor
    
    return best_match

def match_test_selection(speech: str, dtmf: str, tests: list) -> Optional[dict]:
    """
    Match user input to a test from the list
    """
    if not tests:
        return None
    
    # Check DTMF input first
    if dtmf:
        try:
            selection = int(dtmf)
            for test in tests:
                if test['number'] == selection:
                    return test
        except ValueError:
            pass
    
    # Check speech input
    speech = speech.lower()
    best_match = None
    best_score = 0
    
    for test in tests:
        score = fuzzy_match_score(speech, test['name'].lower())
        if score > best_score and score > 0.7:  # 70% match threshold
            best_score = score
            best_match = test
    
    return best_match

def fuzzy_match_score(input_text: str, target_text: str) -> float:
    """
    Calculate a similarity score between input and target text
    Uses a simple method - in a real system you would use a proper fuzzy matching algorithm
    """
    input_words = set(input_text.split())
    target_words = set(target_text.split())
    
    # If either set is empty, return 0
    if not input_words or not target_words:
        return 0
    
    # Calculate Jaccard similarity
    intersection = len(input_words.intersection(target_words))
    union = len(input_words.union(target_words))
    
    return intersection / union if union > 0 else 0

async def get_available_time_slots(doctor: dict, hospital_id: str) -> list:
    """
    Get available time slots for a doctor by checking their schedule in Firebase
    and existing appointments in PostgreSQL
    """
    if not doctor or not hospital_id:
        logger.warning("Cannot get time slots: missing doctor data or hospital ID")
        return []
    
    try:
        # 1. Get doctor's schedule from Firebase
        db = get_firestore_db()
        doctor_id = doctor.get('id')
        
        # Check schedule in hospital_XXX_data/doctors/doctors/{doctor_id}/schedule
        schedule_doc = None
        try:
            loop = asyncio.get_running_loop()
            # Off-load the blocking Firestore call to the default executor
            schedule_doc = await loop.run_in_executor(None, db.collection(f'hospital_{hospital_id}_data').document('doctors').collection('doctors').document(doctor_id).collection('schedule').document('weekly').get)
        except Exception as e:
            logger.warning(f"Error getting doctor schedule from first path: {e}")
            
        # Try alternative path if first attempt failed
        if not schedule_doc or not schedule_doc.exists:
            try:
                # Alternative path for doctor schedule, aligned with hospital_XXX_data structure
                loop = asyncio.get_running_loop()
                # Off-load the blocking Firestore call to the default executor
                schedule_doc = await loop.run_in_executor(None, db.collection(f'hospital_{hospital_id}_data').document('doctors').collection('doctors').document(doctor_id).collection('schedule').document('weekly').get)
            except Exception as e:
                logger.warning(f"Error getting doctor schedule from alternative path: {e}")
        
        # 2. Get booked appointments from PostgreSQL
        conn = None
        booked_slots = []
        try:
            # Get PostgreSQL connection for this hospital
            conn = await get_postgres_connection(hospital_id)
            if conn:
                # Query appointments for this doctor for tomorrow
                tomorrow = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
                tomorrow_end = tomorrow.replace(hour=23, minute=59, second=59)
                
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT start_time, end_time FROM appointments WHERE doctor_id = %s AND start_time BETWEEN %s AND %s AND status = 'scheduled'",
                    (doctor_id, tomorrow, tomorrow_end)
                )
                booked_slots = cursor.fetchall()
                cursor.close()
        except Exception as e:
            logger.error(f"Error fetching booked appointments: {e}")
        finally:
            if conn and hasattr(conn, 'close'):
                conn.close()
        
        # 3. Determine available slots based on schedule and booked appointments
        # Default working hours if no schedule found
        working_hours = {
            "start": "09:00", 
            "end": "17:00"
        }
        
        # Extract actual working hours from doctor's schedule if available
        if schedule_doc and schedule_doc.exists:
            schedule_data = schedule_doc.to_dict()
            tomorrow_day = (datetime.now() + timedelta(days=1)).strftime("%A").lower()
            if tomorrow_day in schedule_data and schedule_data[tomorrow_day].get("available", False):
                day_schedule = schedule_data[tomorrow_day]
                working_hours = {
                    "start": day_schedule.get("start_time", "09:00"),
                    "end": day_schedule.get("end_time", "17:00")
                }
            elif "default" in schedule_data:
                default_schedule = schedule_data["default"]
                working_hours = {
                    "start": default_schedule.get("start_time", "09:00"),
                    "end": default_schedule.get("end_time", "17:00")
                }
        
        # Generate potential slots based on working hours
        start_hour = int(working_hours["start"].split(":")[0])
        end_hour = int(working_hours["end"].split(":")[0])
        
        # Generate hourly slots
        all_slots = []
        for hour in range(start_hour, end_hour):
            period = "AM" if hour < 12 else "PM"
            display_hour = hour if hour <= 12 else hour - 12
            if display_hour == 0:
                display_hour = 12
            slot = f"{display_hour} {period} tomorrow"
            all_slots.append(slot)
        
        # Remove booked slots (simplified approach)
        # In a real implementation, you would convert the slot strings to datetime objects for comparison
        available_slots = all_slots
        
        # If no slots available, provide a helpful message
        if not available_slots:
            return ["No slots available tomorrow"]
            
        return available_slots
        
    except Exception as e:
        logger.error(f"Error getting available time slots: {e}")
        # Return empty list instead of using hardcoded fallbacks
        return []

async def get_available_test_slots(hospital_id: str, test_id: str = None) -> list:
    """
    Get available time slots for tests by checking test schedule in Firebase
    and existing test bookings in PostgreSQL
    """
    if not hospital_id:
        logger.warning("Cannot get test slots: missing hospital ID")
        return []
    
    try:
        # 1. Get test schedule from Firebase
        db = get_firestore_db()
        test_schedule = None
        
        try:
            # Try to get test schedule from test_info document
            loop = asyncio.get_running_loop()
            # Off-load the blocking Firestore call to the default executor
            test_info_doc = await loop.run_in_executor(None, db.collection(f'hospital_{hospital_id}_data').document('test_info').get)
            if test_info_doc.exists:
                test_info = test_info_doc.to_dict()
                test_schedule = test_info.get('schedule', {})
        except Exception as e:
            logger.warning(f"Error getting test schedule from first path: {e}")
        
        # Try alternative path if first attempt failed
        if not test_schedule:
            try:
                # Alternative path for test schedule, aligned with hospital_XXX_data structure
                loop = asyncio.get_running_loop()
                # Off-load the blocking Firestore call to the default executor
                test_info_doc_alt = await loop.run_in_executor(None, db.collection(f'hospital_{hospital_id}_data').document('test_info').get)
                if test_info_doc_alt.exists:
                    test_info_alt = test_info_doc_alt.to_dict()
                    test_schedule = test_info_alt.get('schedule', {}) # Access schedule field as in primary path
            except Exception as e:
                logger.warning(f"Error getting test schedule from alternative path: {e}")
        
        # 2. Get booked test slots from PostgreSQL
        conn = None
        booked_slots = []
        try:
            # Get PostgreSQL connection for this hospital
            conn = await get_postgres_connection(hospital_id)
            if conn:
                # Query test bookings for tomorrow
                tomorrow = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
                tomorrow_end = tomorrow.replace(hour=23, minute=59, second=59)
                
                cursor = conn.cursor()
                query = "SELECT booking_time FROM test_bookings WHERE hospital_id = %s AND booking_time BETWEEN %s AND %s AND status = 'scheduled'"
                params = [hospital_id, tomorrow, tomorrow_end]
                
                # Add test_id filter if provided
                if test_id:
                    query += " AND test_type_id = %s"
                    params.append(test_id)
                    
                cursor.execute(query, params)
                booked_slots = cursor.fetchall()
                cursor.close()
        except Exception as e:
            logger.error(f"Error fetching booked test slots: {e}")
        finally:
            if conn and hasattr(conn, 'close'):
                conn.close()
        
        # 3. Determine available slots based on schedule and booked slots
        # Default testing hours
        testing_hours = {
            "start": "09:00", 
            "end": "17:00"
        }
        
        # Extract actual testing hours from test schedule if available
        if test_schedule:
            tomorrow_day = (datetime.now() + timedelta(days=1)).strftime("%A").lower()
            if tomorrow_day in test_schedule and test_schedule[tomorrow_day].get("available", False):
                day_schedule = test_schedule[tomorrow_day]
                testing_hours = {
                    "start": day_schedule.get("start_time", "09:00"),
                    "end": day_schedule.get("end_time", "17:00")
                }
            elif "default" in test_schedule:
                default_schedule = test_schedule["default"]
                testing_hours = {
                    "start": default_schedule.get("start_time", "09:00"),
                    "end": default_schedule.get("end_time", "17:00")
                }
        
        # Generate potential slots based on testing hours
        start_hour = int(testing_hours["start"].split(":")[0])
        end_hour = int(testing_hours["end"].split(":")[0])
        
        # Generate hourly slots
        all_slots = []
        for hour in range(start_hour, end_hour):
            period = "AM" if hour < 12 else "PM"
            display_hour = hour if hour <= 12 else hour - 12
            if display_hour == 0:
                display_hour = 12
            slot = f"{display_hour} {period} tomorrow"
            all_slots.append(slot)
        
        # Remove booked slots (simplified approach)
        # In a real implementation, you would convert the slot strings to datetime objects for comparison
        available_slots = all_slots
        
        # If no slots available, provide a helpful message
        if not available_slots:
            return ["No slots available tomorrow"]
            
        return available_slots
        
    except Exception as e:
        logger.error(f"Error getting available test slots: {e}")
        # Return empty list instead of using hardcoded fallbacks
        return []

async def format_time_slots(slots: list, language: str) -> str:
    """
    Format time slots for speech output
    """
    if not slots:
        return await get_translated_text("No time slots are currently available.", language)
    
    # Create a string with time slot options
    options = []
    for i, slot in enumerate(slots):
        option = f"{i+1} for {slot}"
        options.append(option)
    
    return ", ".join(options)

def match_time_selection(speech: str, dtmf: str, time_slots: list) -> Optional[str]:
    """
    Match user input to a time slot from the list
    """
    if not time_slots:
        return None
    
    # Check DTMF input first
    if dtmf:
        try:
            selection = int(dtmf)
            if 1 <= selection <= len(time_slots):
                return time_slots[selection - 1]
        except ValueError:
            pass
    
    # Check speech input
    speech = speech.lower()
    for slot in time_slots:
        if slot.lower() in speech:
            return slot
    
    # If no exact match, try fuzzy matching
    best_match = None
    best_score = 0
    
    for slot in time_slots:
        score = fuzzy_match_score(speech, slot.lower())
        if score > best_score and score > 0.7:  # 70% match threshold
            best_score = score
            best_match = slot
    
    return best_match

def is_confirmation_positive(speech: str, dtmf: str) -> bool:
    """
    Check if the user confirmed the appointment
    """
    if dtmf == '1':
        return True
    elif dtmf == '2':
        return False
    
    speech = speech.lower()
    positive_responses = ['yes', 'correct', 'right', 'confirm', 'book', 'ok', 'okay']
    negative_responses = ['no', 'not', 'wrong', 'cancel', 'don\'t']
    
    for response in positive_responses:
        if response in speech:
            return True
    
    for response in negative_responses:
        if response in speech:
            return False
    
    # Default to False if confirmation is unclear
    return False

async def save_appointment(hospital_id: str, patient_name: str, phone: str, doctor_id: str, time: str):
    """
    Save appointment to PostgreSQL and update booking counters in both Firebase and Redis
    """
    try:
        # Use the create_appointment function from database.py which handles Firebase counter increment
        from .database import create_appointment
        appointment_id = await create_appointment(hospital_id, patient_name, phone, doctor_id, time)

        # Also increment Redis counter for real-time tracking
        from datetime import datetime
        if isinstance(time, str) and ' ' in time:
            date_part = time.split(' ')[0]
        else:
            date_part = datetime.now().strftime('%Y-%m-%d')

        from shared.redis.migration_helper import get_shared_redis_manager
        redis_manager = get_shared_redis_manager()
        new_count = await redis_manager.increment_booking_counter_async(hospital_id, doctor_id, date_part)

        if new_count > 0:
            logger.info(f"Updated Redis booking counter for doctor {doctor_id} on {date_part}: {new_count}")

        logger.info(f"Successfully created appointment {appointment_id} for patient {patient_name}")
        return appointment_id
    except Exception as e:
        logger.error(f"Error saving appointment: {e}")
        raise

async def save_test_booking(hospital_id: str, patient_name: str, phone: str, test_type_id: str, time: str):
    """
    Save test booking to PostgreSQL
    """
    # Get PostgreSQL connection for the hospital
    conn = await get_postgres_connection(hospital_id)
    
    try:
        cursor = conn.cursor()
        
        # Insert test booking
        query = """
        INSERT INTO test_bookings (patient_name, phone, test_type_id, "time", status, created_at)
        VALUES (%s, %s, %s, %s, 'scheduled', CURRENT_DATE)
        """
        cursor.execute(query, (patient_name, phone, test_type_id, time))
        
        conn.commit()
        cursor.close()
        conn.close()
    except Exception as e:
        logger.error(f"Error saving test booking: {e}")
        if conn:
            conn.close()
        raise

async def send_confirmation_sms(phone: str, appointment_type: str, details: dict, language: str, hospital_id: str):
    """
    Send confirmation SMS to the patient using Jambonz service.
    """
    logger.info(f"Attempting to send confirmation SMS to {phone} for {appointment_type} for hospital {hospital_id}")

    try:
        # Fetch hospital_config to get hospital_name
        hospital_config = await get_hospital_config(hospital_id)
        hospital_name = hospital_config.name if hospital_config else "our facility"

        patient_name = details.get("patient_name", "Patient")
        appointment_time = details.get("time", "the scheduled time")

        if appointment_type == "test":
            test_name = details.get("test_name", "your test")
            sms_body = f"Hello {patient_name}, your {test_name} at {hospital_name} has been confirmed for {appointment_time}. Reply HELP for assistance."
        else: # doctor appointment
            doctor_name = details.get("doctor_name", "your doctor")
            sms_body = f"Hello {patient_name}, your appointment with Dr. {doctor_name} at {hospital_name} has been confirmed for {appointment_time}. Please arrive 15 minutes early. Reply HELP for assistance."

        # Determine the from_number (placeholder for now)
        from_number_placeholder = "+10000000000" # Or fetch from hospital_config if available later
        logger.info(f"Using placeholder from_number: {from_number_placeholder} for SMS to {phone}")

        # Call jambonz_service.send_sms
        sms_result = jambonz_service.send_sms(
            to_number=phone,
            from_number=from_number_placeholder,
            message=sms_body,
            hospital_id=hospital_id
        )

        if sms_result.get("success"):
            logger.info(f"Confirmation SMS dispatch attempted successfully for {phone}: {sms_result.get('message')}")
        else:
            logger.error(f"Failed to dispatch confirmation SMS for {phone}: {sms_result.get('message')}")

    except Exception as e:
        logger.error(f"Error in send_confirmation_sms for {phone}, hospital {hospital_id}: {str(e)}")
        # Optionally, re-raise or handle more gracefully depending on desired behavior

@app.get("/health")
async def health_check():
    """Health check endpoint with semantic cache status"""
    try:
        # Get semantic cache performance metrics
        metrics = await semantic_integration.get_performance_metrics()

        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "semantic_cache": {
                "total_queries": metrics.get("total_queries", 0),
                "cache_hit_rate_percent": metrics.get("cache_hit_rate_percent", 0),
                "avg_response_time_ms": metrics.get("avg_response_time_ms", 0)
            }
        }
    except Exception as e:
        logger.error(f"Error getting health check metrics: {e}")
        return {
            "status": "error",
            "timestamp": datetime.now().isoformat(),
            "semantic_cache": {"status": "metrics_unavailable"}
        }

@app.get("/semantic-cache/status")
async def semantic_cache_status():
    """Get detailed semantic cache status and statistics"""
    try:
        metrics = await semantic_integration.get_performance_metrics()
        return {
            "status": "active",
            "metrics": metrics,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Error getting semantic cache status: {e}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

if __name__ == "__main__":
    import uvicorn
    
    # Run the FastAPI app
    uvicorn.run(app, host="0.0.0.0", port=8000)
