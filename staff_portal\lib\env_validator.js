/**
 * Environment Variable Validator for Staff Portal
 * Ensures all required environment variables are set for production deployment
 */

/**
 * Required environment variables for production
 */
const REQUIRED_ENV_VARS = {
  // Voice Agent Integration
  VOICE_AGENT_URL: {
    description: 'URL of the voice agent service',
    example: 'https://voice-agent.yourdomain.com',
    validation: (value) => {
      if (!value) return 'VOICE_AGENT_URL is required';
      if (process.env.NODE_ENV === 'production' && value.includes('localhost')) {
        return 'VOICE_AGENT_URL cannot use localhost in production';
      }
      if (!value.startsWith('http://') && !value.startsWith('https://')) {
        return 'VOICE_AGENT_URL must be a valid URL';
      }
      return null;
    }
  },

  INTERNAL_API_KEY: {
    description: 'Internal API key for secure communication with voice agent',
    example: 'your_secure_internal_api_key_here',
    validation: (value) => {
      if (!value) return 'INTERNAL_API_KEY is required';
      if (value.length < 16) return 'INTERNAL_API_KEY must be at least 16 characters';
      if (value === 'your_secure_internal_api_key_here') {
        return 'INTERNAL_API_KEY must not be the default example value';
      }
      return null;
    }
  },

  // Firebase Configuration
  NEXT_PUBLIC_FIREBASE_API_KEY: {
    description: 'Firebase API key',
    example: 'your_firebase_api_key',
    validation: (value) => !value ? 'NEXT_PUBLIC_FIREBASE_API_KEY is required' : null
  },

  NEXT_PUBLIC_FIREBASE_PROJECT_ID: {
    description: 'Firebase project ID',
    example: 'your_project_id',
    validation: (value) => !value ? 'NEXT_PUBLIC_FIREBASE_PROJECT_ID is required' : null
  },

  // Redis Configuration
  REDIS_URL: {
    description: 'Redis connection URL',
    example: 'redis://localhost:6379',
    validation: (value) => {
      if (!value) return 'REDIS_URL is required';
      if (!value.startsWith('redis://') && !value.startsWith('rediss://')) {
        return 'REDIS_URL must be a valid Redis URL';
      }
      return null;
    }
  }
};

/**
 * Optional environment variables with defaults
 */
const OPTIONAL_ENV_VARS = {
  NODE_ENV: 'development',
  LOG_LEVEL: 'info',
  ENABLE_PERFORMANCE_MONITORING: 'false'
};

/**
 * Validate all environment variables
 * @returns {Object} Validation result with success status and any errors
 */
function validateEnvironment() {
  const errors = [];
  const warnings = [];
  const missing = [];

  // Check required variables
  for (const [varName, config] of Object.entries(REQUIRED_ENV_VARS)) {
    const value = process.env[varName];
    
    if (!value) {
      missing.push({
        name: varName,
        description: config.description,
        example: config.example
      });
    } else if (config.validation) {
      const error = config.validation(value);
      if (error) {
        errors.push({
          name: varName,
          error: error,
          current: value
        });
      }
    }
  }

  // Check for production-specific issues
  if (process.env.NODE_ENV === 'production') {
    // Check for development-only values in production
    if (process.env.VOICE_AGENT_URL && process.env.VOICE_AGENT_URL.includes('localhost')) {
      errors.push({
        name: 'VOICE_AGENT_URL',
        error: 'Cannot use localhost URL in production environment',
        current: process.env.VOICE_AGENT_URL
      });
    }

    // Check for weak secrets
    if (process.env.JWT_SECRET && process.env.JWT_SECRET.length < 32) {
      warnings.push({
        name: 'JWT_SECRET',
        warning: 'JWT_SECRET should be at least 32 characters in production',
        current: `${process.env.JWT_SECRET.substring(0, 8)}...`
      });
    }
  }

  return {
    success: errors.length === 0 && missing.length === 0,
    errors,
    warnings,
    missing,
    environment: process.env.NODE_ENV || 'development'
  };
}

/**
 * Print validation results to console
 * @param {Object} result - Validation result from validateEnvironment()
 */
function printValidationResults(result) {
  console.log('\n🔍 Environment Variable Validation');
  console.log('=====================================');
  console.log(`Environment: ${result.environment}`);
  
  if (result.success) {
    console.log('✅ All required environment variables are properly configured!');
  } else {
    console.log('❌ Environment validation failed!');
  }

  // Print missing variables
  if (result.missing.length > 0) {
    console.log('\n📋 Missing Required Variables:');
    result.missing.forEach(item => {
      console.log(`  ❌ ${item.name}`);
      console.log(`     Description: ${item.description}`);
      console.log(`     Example: ${item.example}`);
    });
  }

  // Print errors
  if (result.errors.length > 0) {
    console.log('\n🚨 Configuration Errors:');
    result.errors.forEach(item => {
      console.log(`  ❌ ${item.name}: ${item.error}`);
      if (item.current) {
        console.log(`     Current: ${item.current}`);
      }
    });
  }

  // Print warnings
  if (result.warnings.length > 0) {
    console.log('\n⚠️  Warnings:');
    result.warnings.forEach(item => {
      console.log(`  ⚠️  ${item.name}: ${item.warning}`);
      if (item.current) {
        console.log(`     Current: ${item.current}`);
      }
    });
  }

  if (!result.success) {
    console.log('\n📖 Please check .env.example for configuration guidance');
    console.log('🔧 Fix the issues above before deploying to production');
  }

  console.log('=====================================\n');
}

/**
 * Validate environment and exit if critical errors found
 * Use this in production startup scripts
 */
function validateEnvironmentOrExit() {
  const result = validateEnvironment();
  printValidationResults(result);
  
  if (!result.success) {
    console.error('❌ Environment validation failed. Exiting...');
    process.exit(1);
  }
  
  return result;
}

module.exports = {
  validateEnvironment,
  printValidationResults,
  validateEnvironmentOrExit,
  REQUIRED_ENV_VARS,
  OPTIONAL_ENV_VARS
};
