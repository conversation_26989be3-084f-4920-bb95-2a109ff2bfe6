"""
Enhanced Firebase data loader for shared Redis implementation.
Loads hospital data (doctors, tests) from Firebase Firestore with improved error handling and caching.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)


class FirebaseDataLoader:
    """
    Production-ready Firebase data loader for hospital information.
    Enhanced version with better error handling, retry logic, and caching integration.
    """

    def __init__(self):
        """Initialize Firebase data loader."""
        self.db = None
        self._initialize_db()

    def _initialize_db(self):
        """Initialize Firestore database connection with error handling."""
        try:
            # Import here to avoid circular imports
            from voice_agent.database import get_firestore_db
            self.db = get_firestore_db()
            logger.info("Enhanced Firebase data loader initialized successfully")
        except ImportError:
            logger.error("Failed to import voice_agent.database - ensure voice_agent is available")
            self.db = None
        except Exception as e:
            logger.error(f"Failed to initialize Firebase data loader: {e}")
            self.db = None

    async def load_hospital_data(self, hospital_id: str) -> Dict[str, Any]:
        """
        Load complete hospital data from Firebase with enhanced error handling.

        Args:
            hospital_id: Hospital identifier

        Returns:
            Dict containing doctors, tests, and hospital info
        """
        if not self.db:
            logger.error("Firebase database not initialized")
            return {}

        try:
            hospital_data = {
                "hospital_id": hospital_id,
                "doctors": await self._load_doctors(hospital_id),
                "tests": await self._load_tests(hospital_id),
                "hospital_info": await self._load_hospital_info(hospital_id),
                "languages": ["hi", "bn", "en"],  # Default supported languages
                "metadata": {
                    "loaded_at": asyncio.get_event_loop().time(),
                    "source": "firebase",
                    "version": "1.0"
                }
            }

            logger.info(f"Loaded hospital data for {hospital_id}: "
                       f"{len(hospital_data['doctors'])} doctors, "
                       f"{len(hospital_data['tests'])} tests")

            return hospital_data

        except Exception as e:
            logger.error(f"Error loading hospital data for {hospital_id}: {e}")
            return {}

    async def _load_doctors(self, hospital_id: str) -> List[Dict[str, Any]]:
        """Load doctors data from Firebase with multiple collection path support."""
        try:
            # Try multiple collection paths for compatibility
            collection_paths = [
                f'hospital_{hospital_id}_data',
                f'hospitals/{hospital_id}/data',
                f'hospital_data/{hospital_id}'
            ]
            
            for collection_path in collection_paths:
                try:
                    doctors_ref = self.db.collection(collection_path).document('doctors').collection('doctors')
                    
                    loop = asyncio.get_running_loop()
                    docs = await loop.run_in_executor(None, doctors_ref.get)
                    
                    if docs:
                        doctors = []
                        for doc in docs:
                            doctor_data = doc.to_dict()
                            if doctor_data:
                                doctor_data['id'] = doc.id
                                # Ensure required fields
                                doctor_data.setdefault('name', 'Unknown Doctor')
                                doctor_data.setdefault('specialty', 'General Medicine')
                                doctor_data.setdefault('schedule', 'Contact hospital for schedule')
                                doctors.append(doctor_data)
                        
                        if doctors:
                            logger.info(f"Loaded {len(doctors)} doctors from {collection_path}")
                            return doctors
                            
                except Exception as e:
                    logger.debug(f"Failed to load doctors from {collection_path}: {e}")
                    continue
            
            logger.warning(f"No doctors found for hospital {hospital_id} in any collection path")
            return []

        except Exception as e:
            logger.error(f"Error loading doctors for hospital {hospital_id}: {e}")
            return []

    async def _load_tests(self, hospital_id: str) -> List[Dict[str, Any]]:
        """Load tests data from Firebase with multiple collection path support."""
        try:
            # Try multiple collection paths for compatibility
            collection_paths = [
                f'hospital_{hospital_id}_data',
                f'hospitals/{hospital_id}/data',
                f'hospital_data/{hospital_id}'
            ]
            
            for collection_path in collection_paths:
                try:
                    tests_ref = self.db.collection(collection_path).document('test_info').collection('tests')
                    
                    loop = asyncio.get_running_loop()
                    docs = await loop.run_in_executor(None, tests_ref.get)
                    
                    if docs:
                        tests = []
                        for doc in docs:
                            test_data = doc.to_dict()
                            if test_data:
                                test_data['id'] = doc.id
                                # Ensure required fields
                                test_data.setdefault('name', 'Unknown Test')
                                test_data.setdefault('category', 'General')
                                test_data.setdefault('price', 'Contact hospital for pricing')
                                tests.append(test_data)
                        
                        if tests:
                            logger.info(f"Loaded {len(tests)} tests from {collection_path}")
                            return tests
                            
                except Exception as e:
                    logger.debug(f"Failed to load tests from {collection_path}: {e}")
                    continue
            
            logger.warning(f"No tests found for hospital {hospital_id} in any collection path")
            return []

        except Exception as e:
            logger.error(f"Error loading tests for hospital {hospital_id}: {e}")
            return []

    async def _load_hospital_info(self, hospital_id: str) -> Dict[str, Any]:
        """Load hospital information from Firebase."""
        try:
            # Try to load hospital info from multiple possible locations
            collection_paths = [
                f'hospitals/{hospital_id}',
                f'hospital_{hospital_id}_data/info',
                f'hospital_info/{hospital_id}'
            ]
            
            for collection_path in collection_paths:
                try:
                    loop = asyncio.get_running_loop()
                    doc_ref = self.db.document(collection_path)
                    doc = await loop.run_in_executor(None, doc_ref.get)
                    
                    if doc.exists:
                        hospital_info = doc.to_dict()
                        logger.info(f"Loaded hospital info from {collection_path}")
                        return hospital_info
                        
                except Exception as e:
                    logger.debug(f"Failed to load hospital info from {collection_path}: {e}")
                    continue
            
            # Return default hospital info if not found
            logger.warning(f"No hospital info found for {hospital_id}, using defaults")
            return {
                "name": f"Hospital {hospital_id}",
                "languages": ["hi", "bn", "en"],
                "timezone": "Asia/Kolkata"
            }

        except Exception as e:
            logger.error(f"Error loading hospital info for {hospital_id}: {e}")
            return {
                "name": f"Hospital {hospital_id}",
                "languages": ["hi", "bn", "en"],
                "timezone": "Asia/Kolkata"
            }

    async def check_hospital_exists(self, hospital_id: str) -> bool:
        """
        Check if hospital data exists in Firebase.
        
        Args:
            hospital_id: Hospital identifier
            
        Returns:
            bool: True if hospital data exists
        """
        if not self.db:
            return False
            
        try:
            hospital_data = await self.load_hospital_data(hospital_id)
            return bool(hospital_data.get('doctors') or hospital_data.get('tests'))
        except Exception as e:
            logger.error(f"Error checking hospital existence for {hospital_id}: {e}")
            return False

    def get_supported_hospitals(self) -> List[str]:
        """
        Get list of supported hospital IDs from Firebase.
        
        Returns:
            List of hospital IDs
        """
        if not self.db:
            return []
            
        try:
            # This would need to be implemented based on your Firebase structure
            # For now, return empty list as this requires specific Firebase schema knowledge
            logger.warning("get_supported_hospitals not implemented - requires specific Firebase schema")
            return []
        except Exception as e:
            logger.error(f"Error getting supported hospitals: {e}")
            return []
