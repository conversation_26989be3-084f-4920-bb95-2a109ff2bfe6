"""
Example Integration for Voice Agent with Shared Redis Implementation

This file shows how to integrate the voice_agent with the new shared Redis implementation.
It provides examples of how to replace existing Redis usage with the shared implementation.
"""

# Example 1: Simple replacement of existing RedisManager
# OLD CODE (in voice_agent/cache_manager.py):
# from .cache_manager import redis_manager
# result = redis_manager.set("key", "value", expiry=3600)

# NEW CODE:
from shared.redis.migration_helper import get_shared_redis_manager

# Drop-in replacement
redis_manager = get_shared_redis_manager()
# All existing code continues to work
result = redis_manager.set("key", "value", expiry=3600)


# Example 2: Using the new semantic caching features
async def example_semantic_caching():
    """Example of using enhanced semantic caching with IndicBERT."""
    
    # Get the shared Redis adapter
    from shared.redis.adapters.python_adapter import get_python_adapter
    redis_adapter = get_python_adapter()
    
    # Cache a response with semantic matching
    query = "डॉक्टर शर्मा कब आएंगे?"  # Hindi: "When will <PERSON><PERSON> come?"
    response = "डॉ. शर्मा शाम 5 बजे आएंगे।"  # Hindi: "Dr<PERSON> will come at 5 PM."
    hospital_id = "hospital_123"
    
    # Cache with IndicBERT semantic matching
    success = await redis_adapter.cache_semantic_response_async(
        query, response, hospital_id, "doctor_schedule"
    )
    
    if success:
        print("Response cached with semantic matching")
    
    # Search for similar queries
    similar_query = "शर्मा डॉक्टर का समय क्या है?"  # "What is Dr. Sharma's time?"
    matches = await redis_adapter.semantic_search_async(
        similar_query, hospital_id, "doctor_schedule", limit=3
    )
    
    for match in matches:
        print(f"Found match: {match['query']} -> {match['response']} (similarity: {match['similarity']:.2f})")


# Example 3: Call context management (compatible with existing code)
async def example_call_context():
    """Example of call context management."""
    
    redis_adapter = get_python_adapter()
    
    call_id = "call_12345"
    context_data = {
        "hospital_id": "hospital_123",
        "caller_number": "+91XXXXXXXXXX",
        "language": "hi",
        "state": "appointment_booking",
        "entities": {"doctor_name": "Dr. Sharma"}
    }
    
    # Save call context (automatically uses optimal TTL)
    await redis_adapter.save_call_context(call_id, context_data)
    
    # Load call context
    loaded_context = await redis_adapter.load_call_context(call_id)
    print(f"Loaded context: {loaded_context}")


# Example 4: Hospital data caching with optimization
async def example_hospital_data_caching():
    """Example of hospital data caching with automatic optimization."""
    
    redis_adapter = get_python_adapter()
    hospital_id = "hospital_123"
    
    # Cache doctors data
    doctors_data = [
        {"id": "doc1", "name": "Dr. Sharma", "specialty": "Cardiology"},
        {"id": "doc2", "name": "Dr. Patel", "specialty": "Neurology"}
    ]
    
    await redis_adapter.cache_hospital_data_async(hospital_id, "doctors", doctors_data)
    
    # Cache tests data
    tests_data = [
        {"id": "test1", "name": "Blood Test", "cost": 500},
        {"id": "test2", "name": "X-Ray", "cost": 800}
    ]
    
    await redis_adapter.cache_hospital_data_async(hospital_id, "tests", tests_data)
    
    # Retrieve cached data
    cached_doctors = await redis_adapter.get_cached_hospital_data_async(hospital_id, "doctors")
    print(f"Cached doctors: {cached_doctors}")


# Example 5: Availability caching
async def example_availability_caching():
    """Example of availability caching."""
    
    redis_adapter = get_python_adapter()
    hospital_id = "hospital_123"
    
    # Cache doctor availability
    await redis_adapter.cache_availability_async(
        hospital_id, "doc1", "doctor", "2024-01-15", True
    )
    
    # Check availability
    is_available = await redis_adapter.get_availability_async(
        hospital_id, "doc1", "doctor", "2024-01-15"
    )
    print(f"Doctor available: {is_available}")


# Example 6: Cache monitoring and optimization
async def example_monitoring_and_optimization():
    """Example of cache monitoring and optimization."""
    
    redis_adapter = get_python_adapter()
    hospital_id = "hospital_123"
    
    # Get comprehensive cache statistics
    stats = await redis_adapter.get_cache_stats_async()
    print(f"Cache statistics: {stats}")
    
    # Optimize cache for better performance
    optimization_result = redis_adapter.optimize_cache(hospital_id)
    print(f"Optimization result: {optimization_result}")
    
    # Get optimization recommendations
    recommendations = redis_adapter.optimizer.get_optimization_recommendations(hospital_id)
    print(f"Recommendations: {recommendations}")


# Example 7: Migration from existing Redis implementation
async def example_migration():
    """Example of migrating from existing Redis implementation."""
    
    from shared.redis.migration_helper import VoiceAgentMigrationHelper
    import redis  # Old Redis client
    
    # Create migration helper
    migration_helper = VoiceAgentMigrationHelper()
    
    # Connect to old Redis instance
    old_redis = redis.Redis(host='localhost', port=6379, db=0)
    
    # Migrate data for specific hospitals
    hospital_ids = ["hospital_123", "hospital_456"]
    migration_result = await migration_helper.migrate_existing_data(old_redis, hospital_ids)
    
    print(f"Migration completed: {migration_result}")


# Example 8: Using with existing voice_agent code patterns
class ExampleVoiceAgentIntegration:
    """Example of integrating with existing voice_agent patterns."""
    
    def __init__(self):
        # Use the compatibility layer for seamless integration
        self.redis_manager = get_shared_redis_manager()
    
    async def process_user_query(self, query: str, hospital_id: str, language: str):
        """Process user query with semantic caching."""
        
        # First, try to find a semantic match
        matches = await self.redis_manager.semantic_search_async(
            query, hospital_id, "general", limit=1
        )
        
        if matches and matches[0]["similarity"] > 0.8:
            # High confidence match found
            return matches[0]["response"]
        
        # If no good match, process query normally and cache the result
        # (This would be your existing query processing logic)
        response = await self._process_query_with_llm(query, hospital_id, language)
        
        # Cache the new response for future semantic matching
        await self.redis_manager.cache_semantic_response_async(
            query, response, hospital_id, "general"
        )
        
        return response
    
    async def _process_query_with_llm(self, query: str, hospital_id: str, language: str):
        """Placeholder for existing LLM processing logic."""
        # This would be your existing query processing logic
        return f"Processed response for: {query}"
    
    async def save_call_state(self, call_id: str, state_data: dict):
        """Save call state using optimized TTL."""
        return await self.redis_manager.set_async(
            f"call:state:{call_id}", 
            state_data, 
            expiry=600  # 10 minutes
        )
    
    async def get_doctor_info(self, hospital_id: str, doctor_name: str):
        """Get doctor info with caching."""
        cache_key = f"doctor_info:{hospital_id}:{doctor_name}"
        
        # Try cache first
        cached_info = await self.redis_manager.get_async(cache_key)
        if cached_info:
            return cached_info
        
        # If not cached, fetch from database and cache
        # (This would be your existing database logic)
        doctor_info = await self._fetch_doctor_from_db(hospital_id, doctor_name)
        
        if doctor_info:
            await self.redis_manager.set_async(cache_key, doctor_info, expiry=7200)  # 2 hours
        
        return doctor_info
    
    async def _fetch_doctor_from_db(self, hospital_id: str, doctor_name: str):
        """Placeholder for database fetch logic."""
        # This would be your existing database logic
        return {"name": doctor_name, "specialty": "General Medicine"}


# Example usage
async def main():
    """Main example function."""
    print("=== Voice Agent Shared Redis Integration Examples ===")
    
    # Example 1: Basic semantic caching
    print("\n1. Semantic Caching Example:")
    await example_semantic_caching()
    
    # Example 2: Call context management
    print("\n2. Call Context Example:")
    await example_call_context()
    
    # Example 3: Hospital data caching
    print("\n3. Hospital Data Caching Example:")
    await example_hospital_data_caching()
    
    # Example 4: Availability caching
    print("\n4. Availability Caching Example:")
    await example_availability_caching()
    
    # Example 5: Monitoring and optimization
    print("\n5. Monitoring and Optimization Example:")
    await example_monitoring_and_optimization()
    
    # Example 6: Voice agent integration
    print("\n6. Voice Agent Integration Example:")
    integration = ExampleVoiceAgentIntegration()
    response = await integration.process_user_query(
        "डॉक्टर की जानकारी चाहिए", "hospital_123", "hi"
    )
    print(f"Response: {response}")


if __name__ == "__main__":
    # Run examples
    asyncio.run(main())
