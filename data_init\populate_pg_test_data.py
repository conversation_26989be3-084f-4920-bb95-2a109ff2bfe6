"""
<PERSON><PERSON><PERSON> to populate PostgreSQL databases for each hospital with test appointment data for today.
"""
import os
import logging
import firebase_admin
from firebase_admin import credentials, firestore
import psycopg2
from psycopg2 import extras # For RealDictCursor
from datetime import datetime, date, time, timedelta
import uuid

# --- Logging Setup ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s')
logger = logging.getLogger(__name__)

# --- Global Variables ---
# Path to your Firebase service account key JSON file
# Try to get from environment variable first, then fall back to hardcoded path if needed
SERVICE_ACCOUNT_KEY_PATH = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')

# Fallback path if environment variable is not set
FALLBACK_KEY_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                               'staff_portal', '.firebase-service-account.json')

# Check if the service account key file exists in the downloads folder
DOWNLOADS_KEY_PATH = os.path.join(os.path.expanduser('~'), 'Downloads', 'VoiceHealthPortal', 'healthrepl-64fc8d6938f3.json')

# Dictionary to hold PostgreSQL connections: {hospital_numeric_id: conn_object}
postgres_connections = {}

# --- Firebase Initialization ---
def init_firebase():
    """Initialize Firebase Admin SDK if not already initialized."""
    if not firebase_admin._apps:
        try:
            # Try with the environment variable path first
            if SERVICE_ACCOUNT_KEY_PATH and os.path.exists(SERVICE_ACCOUNT_KEY_PATH):
                cred = credentials.Certificate(SERVICE_ACCOUNT_KEY_PATH)
                firebase_admin.initialize_app(cred)
                logger.info(f"Firebase initialized successfully with service account key from env var: {SERVICE_ACCOUNT_KEY_PATH}")
                return True
            
            # Try with the downloads folder path
            elif os.path.exists(DOWNLOADS_KEY_PATH):
                cred = credentials.Certificate(DOWNLOADS_KEY_PATH)
                firebase_admin.initialize_app(cred)
                logger.info(f"Firebase initialized successfully with service account key from downloads: {DOWNLOADS_KEY_PATH}")
                return True
                
            # Try with the fallback path
            elif os.path.exists(FALLBACK_KEY_PATH):
                cred = credentials.Certificate(FALLBACK_KEY_PATH)
                firebase_admin.initialize_app(cred)
                logger.info(f"Firebase initialized successfully with service account key from fallback: {FALLBACK_KEY_PATH}")
                return True
                
            else:
                # Last resort: try with Application Default Credentials
                logger.warning("No service account key file found. Attempting to use Application Default Credentials.")
                firebase_admin.initialize_app()
                logger.info("Firebase initialized successfully with default credentials (ADC).")
                return True
        except Exception as e:
            logger.error(f"Error initializing Firebase: {e}")
            return False
    return True

# --- PostgreSQL Connection Management ---
def init_postgres_for_hospital(hospital_numeric_id, db_url):
    """Initialize and return a PostgreSQL connection for a specific hospital."""
    if hospital_numeric_id in postgres_connections:
        # Ping the existing connection to ensure it's alive
        try:
            with postgres_connections[hospital_numeric_id].cursor() as cur:
                cur.execute("SELECT 1")
            return postgres_connections[hospital_numeric_id]
        except (psycopg2.OperationalError, psycopg2.InterfaceError):
            logger.warning(f"Connection for hospital {hospital_numeric_id} lost. Reconnecting.")
            try:
                postgres_connections[hospital_numeric_id].close()
            except Exception:
                pass # Ignore errors during close if already broken
    try:
        conn = psycopg2.connect(db_url)
        conn.autocommit = False # We will manage transactions explicitly
        postgres_connections[hospital_numeric_id] = conn
        logger.info(f"PostgreSQL connection established for hospital {hospital_numeric_id}.")
        return conn
    except psycopg2.Error as e:
        logger.error(f"Error connecting to PostgreSQL for hospital {hospital_numeric_id} ({db_url}): {e}")
        return None

def close_all_postgres_connections():
    """Close all active PostgreSQL connections."""
    for hospital_id, conn in postgres_connections.items():
        try:
            conn.close()
            logger.info(f"Closed PostgreSQL connection for hospital {hospital_id}.")
        except Exception as e:
            logger.error(f"Error closing connection for hospital {hospital_id}: {e}")
    postgres_connections.clear()

# --- Data Fetching from Firestore ---
def get_hospitals_from_firestore(db):
    """Fetch all hospital configurations from Firestore."""
    try:
        hospitals_ref = db.collection('hospitals')
        hospital_docs = hospitals_ref.stream()
        hospitals_list = []
        for doc in hospital_docs:
            hospital_data = doc.to_dict()
            # Extract numeric ID from document ID like 'hospital_123_data'
            parts = doc.id.split('_')
            if len(parts) == 3 and parts[0] == 'hospital' and parts[2] == 'data':
                hospital_data['numeric_id'] = parts[1]
                hospitals_list.append(hospital_data)
            else:
                logger.warning(f"Skipping hospital document with unexpected ID format: {doc.id}")
        return hospitals_list
    except Exception as e:
        logger.error(f"Error fetching hospitals from Firestore: {e}")
        return []

def get_doctors_for_hospital_from_firestore(db, hospital_numeric_id, limit=3):
    """Fetch doctors for a specific hospital from Firestore."""
    try:
        doctors_ref = db.collection(f'hospital_{hospital_numeric_id}_data') \
                        .document('doctors') \
                        .collection('doctors')
        doctor_docs = doctors_ref.limit(limit).stream()
        doctors_list = []
        for doc in doctor_docs:
            doctor_data = doc.to_dict()
            doctor_data['id'] = doc.id # Ensure doctor ID is included
            doctors_list.append(doctor_data)
        if not doctors_list:
            logger.warning(f"No doctors found in Firestore for hospital {hospital_numeric_id}.")
        return doctors_list
    except Exception as e:
        logger.error(f"Error fetching doctors for hospital {hospital_numeric_id} from Firestore: {e}")
        return []

# --- Data Insertion into PostgreSQL ---
def insert_dummy_patients_for_hospital(pg_conn, hospital_numeric_id):
    """Insert dummy patients for the hospital if they don't exist and return their IDs."""
    patient_ids = []
    dummy_patient_names = ["Test Patient Alpha", "Test Patient Beta", "Test Patient Gamma"]
    with pg_conn.cursor() as cur:
        for name in dummy_patient_names:
            patient_uuid = str(uuid.uuid4())[:10] # Truncate to 10 characters
            try:
                # Check if patient with this name already exists for this hospital to avoid duplicates if script is re-run
                # This assumes a patients table structure like: id, name, hospital_id, created_at, updated_at
                # and that (name, hospital_id) should ideally be unique, or we just create new UUIDs each time.
                # For simplicity here, we'll attempt to insert with a UUID and rely on ON CONFLICT for the ID.
                # A more robust approach might check by name AND hospital_id first.
                
                cur.execute("""
                    INSERT INTO patients (id, name, hospital_id, created_at, updated_at, email, phone, date_of_birth, address, medical_history)
                    VALUES (%s, %s, %s, NOW(), NOW(), %s, %s, %s, %s, %s)
                    ON CONFLICT (id) DO NOTHING
                    RETURNING id; 
                    """, (
                        patient_uuid, 
                        name, 
                        hospital_numeric_id,
                        f"{name.lower().replace(' ', '.')}@testpatient.com", # dummy email
                        "555-0101", # dummy phone
                        date(1980, 1, 1), # dummy DOB
                        "123 Test Lane", # dummy address
                        '{"conditions": ["test condition"]}' # dummy medical history (JSONB)
                    )
                )
                # If ON CONFLICT DO NOTHING and row already exists, RETURNING id won't return if nothing was inserted.
                # We need to ensure we get an ID. So, if nothing returned, query for it.
                inserted_id_row = cur.fetchone()
                if inserted_id_row:
                    patient_ids.append(inserted_id_row[0])
                    logger.info(f"Ensured/Inserted dummy patient '{name}' with ID {inserted_id_row[0]} for hospital {hospital_numeric_id}.")
                else:
                    # If insert was skipped due to conflict and nothing returned, select the ID by name and hospital ID
                    # This part is a bit more complex and assumes name+hospital_id is a good way to find the existing one.
                    # For simplicity for this test script, we'll just use the generated UUID if the insert was clean or we assume it's there.
                    # A robust solution would be more involved here if names are not unique per hospital.
                    # For now, we'll assume the UUID based insert is sufficient or a simple re-query if needed.
                    cur.execute("SELECT id FROM patients WHERE name = %s AND hospital_id = %s LIMIT 1", (name, hospital_numeric_id))
                    existing_patient_row = cur.fetchone()
                    if existing_patient_row:
                        patient_ids.append(existing_patient_row[0])
                        logger.info(f"Found existing dummy patient '{name}' with ID {existing_patient_row[0]} for hospital {hospital_numeric_id}.")
                    else:
                        # This case should ideally not be hit if ON CONFLICT is on ID and UUIDs are unique.
                        # If names can be non-unique, this logic needs refinement.
                        logger.warning(f"Could not retrieve ID for dummy patient '{name}' for hospital {hospital_numeric_id} after insert/conflict.")
                        # Fallback to using the generated UUID, assuming it was inserted successfully even if RETURNING didn't fire as expected in all PG versions/configs.
                        patient_ids.append(patient_uuid) 

            except psycopg2.Error as e:
                logger.error(f"Error ensuring/inserting dummy patient {name} for hospital {hospital_numeric_id}: {e}")
                pg_conn.rollback()
                # Continue to next patient, or re-raise if critical
        pg_conn.commit() # Commit all patient insertions
    return patient_ids

def insert_test_appointments_for_hospital(pg_conn, hospital_numeric_id, doctors_list, patient_ids_list):
    """Insert test appointments for today into the given hospital's PostgreSQL DB."""
    if not doctors_list:
        logger.info(f"No doctors available for hospital {hospital_numeric_id}, skipping appointment insertion.")
        return 0
    if not patient_ids_list:
        logger.info(f"No dummy patient IDs available for hospital {hospital_numeric_id}, skipping appointment insertion.")
        return 0

    inserted_count = 0
    today_date = date.today()
    # Use fewer appointment times to ensure enough patients per doctor if lists are small
    appointment_start_times = [time(9, 0), time(10, 30)] # Sample start times
    appointment_duration = timedelta(minutes=30) # Duration of each appointment

    with pg_conn.cursor() as cur:
        patient_idx = 0 # Cycle through patient IDs
        for doctor in doctors_list:
            doctor_id_original = doctor.get('id')
            if not doctor_id_original:
                logger.warning(f"Skipping appointments for a doctor due to missing doctor ID in hospital {hospital_numeric_id}")
                continue

            doctor_id_to_insert = doctor_id_original
            if len(doctor_id_original) > 10:
                logger.warning(f"Doctor ID '{doctor_id_original}' for hospital {hospital_numeric_id} is longer than 10 characters. Truncating to '{doctor_id_original[:10]}'. This may cause issues if the 'staff' table uses full-length IDs.")
                doctor_id_to_insert = doctor_id_original[:10]

            for appt_start_time in appointment_start_times:
                if patient_idx >= len(patient_ids_list):
                    logger.warning(f"Ran out of patient IDs for hospital {hospital_numeric_id}. Stopping appointment creation for this hospital.")
                    pg_conn.commit() # Commit what has been inserted so far
                    return inserted_count
                
                current_patient_id = patient_ids_list[patient_idx]
                patient_idx += 1

                appointment_start_datetime = datetime.combine(today_date, appt_start_time)
                appointment_end_datetime = appointment_start_datetime + appointment_duration
                appt_uuid = str(uuid.uuid4())[:10] # Truncate to 10 characters. This will be the 'id' for the appointments table
                
                sql = """
                INSERT INTO appointments 
                (id, patient_id, doctor_id, hospital_id, start_time, end_time, status, notes, source, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
                ON CONFLICT (id) DO NOTHING;
                """

                try:
                    cur.execute(sql, (
                        appt_uuid,
                        current_patient_id,
                        doctor_id_to_insert, # Use potentially truncated doctor_id
                        hospital_numeric_id, 
                        appointment_start_datetime,
                        appointment_end_datetime,
                        'Scheduled', 
                        'Test appointment generated by script.',
                        'voice' # Source column, using an allowed value from check constraint
                    ))
                    if cur.rowcount > 0:
                        inserted_count += 1
                        logger.info(f"Inserted test appointment {appt_uuid} for Dr. {doctor_id_to_insert} (original: {doctor_id_original}) with patient {current_patient_id} at {appointment_start_datetime} in hospital {hospital_numeric_id}.")
                    else:
                        logger.info(f"Test appointment {appt_uuid} likely already exists for Dr. {doctor_id_to_insert} in hospital {hospital_numeric_id}.")
                except psycopg2.Error as e:
                    logger.error(f"Error inserting appointment {appt_uuid} for hospital {hospital_numeric_id}: {e}")
                    pg_conn.rollback()
                    return inserted_count 
        pg_conn.commit() 
    return inserted_count

# --- Main Orchestration ---
def main():
    logger.info("--- Starting Phase 2: Populate PostgreSQL with Test Appointment Data for Today ---")
    
    if not init_firebase():
        logger.error("Firebase initialization failed. Exiting script.")
        return

    db = firestore.client()
    hospitals = get_hospitals_from_firestore(db)

    if not hospitals:
        logger.warning("No hospitals found in Firestore. Nothing to populate.")
        close_all_postgres_connections()
        return

    total_appointments_inserted_all_hospitals = 0

    for hospital_config in hospitals:
        hospital_numeric_id = hospital_config.get('numeric_id')
        db_connection_string = hospital_config.get('db_connection_string')

        if not hospital_numeric_id or not db_connection_string:
            logger.warning(f"Skipping hospital due to missing numeric_id or db_connection_string: {hospital_config.get('name', 'N/A')}")
            continue
        
        logger.info(f"--- Processing hospital: {hospital_config.get('name', 'N/A')} (ID: {hospital_numeric_id}) ---")

        pg_conn = init_postgres_for_hospital(hospital_numeric_id, db_connection_string)
        if not pg_conn:
            logger.warning(f"Could not connect to PostgreSQL for hospital {hospital_numeric_id}. Skipping.")
            continue

        # Insert/Ensure dummy patients exist for this hospital
        dummy_patient_ids = insert_dummy_patients_for_hospital(pg_conn, hospital_numeric_id)
        if not dummy_patient_ids:
            logger.warning(f"Could not create/retrieve dummy patient IDs for hospital {hospital_numeric_id}. Skipping appointment creation.")
            # Continue to the next hospital, as this isn't a fatal error for the whole script

        doctors = get_doctors_for_hospital_from_firestore(db, hospital_numeric_id, limit=2) 
        if not doctors:
            logger.info(f"No doctors found for hospital {hospital_numeric_id}. No appointments will be created.")
            
        appointments_inserted_this_hospital = insert_test_appointments_for_hospital(pg_conn, hospital_numeric_id, doctors, dummy_patient_ids)
        logger.info(f"Inserted {appointments_inserted_this_hospital} test appointments for hospital {hospital_numeric_id}.")
        total_appointments_inserted_all_hospitals += appointments_inserted_this_hospital

    logger.info(f"--- Phase 2 Finished: Total test appointments inserted across all hospitals: {total_appointments_inserted_all_hospitals} ---")
    close_all_postgres_connections()

if __name__ == "__main__":
    main()
