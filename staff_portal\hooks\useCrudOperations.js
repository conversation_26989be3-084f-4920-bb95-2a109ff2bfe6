import { useState, useCallback } from 'react';
import { useNotifications } from './useNotifications';
import { validateApiResponse, formatErrorMessage } from '../utils/apiUtils';
import { sanitizeEntity } from '../utils/validationSchemas';

/**
 * Helper function to get singular form of entity type
 * Handles special cases like 'staff' -> 'staff member'
 */
const getEntitySingular = (entityType, customSingular) => {
  if (customSingular) return customSingular;

  // Handle special cases
  const specialCases = {
    'staff': 'staff member',
    'data': 'data item',
    'media': 'media item'
  };

  return specialCases[entityType] || entityType.slice(0, -1);
};

/**
 * Generic CRUD operations hook for consistent API interactions
 * @param {string} entityType - The entity type (e.g., 'staff', 'doctors')
 * @param {Function} fetchFunction - Function to refresh the entity list
 * @param {Object} options - Configuration options
 */
export function useCrudOperations(entityType, fetchFunction, options = {}) {
  const [loading, setLoading] = useState(false);
  const { showSuccess, showError } = useNotifications();

  const {
    baseUrl = `/api/admin/${entityType}`,
    requiresHospitalId = true,
    entitySingular = getEntitySingular(entityType, options.entitySingular),
    successMessages = {
      create: `${entitySingular} created successfully`,
      update: `${entitySingular} updated successfully`,
      delete: `${entitySingular} deleted successfully`
    },
    errorMessages = {
      create: `Failed to create ${entitySingular}`,
      update: `Failed to update ${entitySingular}`,
      delete: `Failed to delete ${entitySingular}`
    }
  } = options;

  /**
   * Enhanced API request handler with validation
   */
  const makeApiRequest = useCallback(async (url, method, body = null) => {
    try {
      const config = {
        method,
        headers: {
          'Content-Type': 'application/json'
        }
      };

      if (body) {
        config.body = JSON.stringify(body);
      }

      const response = await fetch(url, config);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      // Validate API response structure
      const validation = validateApiResponse(data, ['success']);
      if (!validation.valid) {
        throw new Error(`Invalid API response: ${validation.error}`);
      }

      return { response, data, success: true };
    } catch (error) {
      return {
        response: null,
        data: null,
        success: false,
        error: formatErrorMessage(error, 'Network request failed')
      };
    }
  }, []);

  /**
   * Create operation with enhanced validation
   */
  const create = useCallback(async (entityData, user) => {
    if (!user) {
      showError('Error', 'User authentication required');
      return { success: false, error: 'User authentication required' };
    }

    setLoading(true);
    try {
      // Sanitize input data
      const sanitizedData = sanitizeEntity(entityData, entitySingular);

      const requestBody = requiresHospitalId
        ? {
            hospitalId: user.hospital_id,
            [`${entitySingular.replace(' ', '')}Data`]: {
              ...sanitizedData,
              hospital_id: user.hospital_id
            }
          }
        : sanitizedData;

      const result = await makeApiRequest(baseUrl, 'POST', requestBody);

      if (!result.success) {
        showError('Network Error', result.error);
        return { success: false, error: result.error };
      }

      const { data } = result;

      if (data.success) {
        showSuccess('Success', successMessages.create);
        if (fetchFunction) {
          await fetchFunction(user.hospital_id);
        }
        return { success: true, data: data.data };
      } else {
        const errorMsg = formatErrorMessage(data.message, errorMessages.create);
        showError('Creation Failed', errorMsg);
        return { success: false, error: data.message };
      }
    } catch (error) {
      console.error(`Create ${entityType} error:`, error);
      const errorMsg = formatErrorMessage(error, `An error occurred while creating ${entitySingular}`);
      showError('Error', errorMsg);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  }, [entityType, baseUrl, requiresHospitalId, entitySingular, successMessages, errorMessages, makeApiRequest, fetchFunction, showSuccess, showError]);

  /**
   * Update operation
   */
  const update = useCallback(async (entityId, entityData, user) => {
    if (!user || !entityId) {
      showError('Error', 'User authentication and entity ID required');
      return { success: false, error: 'User authentication and entity ID required' };
    }

    setLoading(true);
    try {
      const requestBody = requiresHospitalId
        ? {
            hospitalId: user.hospital_id,
            [`${entitySingular.replace(' ', '')}Data`]: {
              ...entityData,
              hospital_id: user.hospital_id
            }
          }
        : entityData;

      const result = await makeApiRequest(`${baseUrl}/${entityId}`, 'PUT', requestBody);

      if (!result.success) {
        showError('Network Error', result.error);
        return { success: false, error: result.error };
      }

      const { data } = result;

      if (data.success) {
        showSuccess('Success', successMessages.update);
        if (fetchFunction) {
          await fetchFunction(user.hospital_id);
        }
        return { success: true, data: data.data };
      } else {
        showError('Update Failed', `${errorMessages.update}: ${data.message}`);
        return { success: false, error: data.message };
      }
    } catch (error) {
      console.error(`Update ${entityType} error:`, error);
      showError('Error', `An error occurred while updating ${entitySingular}`);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  }, [entityType, baseUrl, requiresHospitalId, entitySingular, successMessages, errorMessages, makeApiRequest, fetchFunction, showSuccess, showError]);

  /**
   * Delete operation
   */
  const deleteEntity = useCallback(async (entityId, user) => {
    if (!user || !entityId) {
      showError('Error', 'User authentication and entity ID required');
      return { success: false, error: 'User authentication and entity ID required' };
    }

    setLoading(true);
    try {
      const requestBody = requiresHospitalId 
        ? { hospitalId: user.hospital_id }
        : {};

      const result = await makeApiRequest(`${baseUrl}/${entityId}`, 'DELETE', requestBody);

      if (!result.success) {
        showError('Network Error', result.error);
        return { success: false, error: result.error };
      }

      const { data } = result;

      if (data.success) {
        showSuccess('Success', successMessages.delete);
        if (fetchFunction) {
          await fetchFunction(user.hospital_id);
        }
        return { success: true };
      } else {
        showError('Deletion Failed', `${errorMessages.delete}: ${data.message}`);
        return { success: false, error: data.message };
      }
    } catch (error) {
      console.error(`Delete ${entityType} error:`, error);
      showError('Error', `An error occurred while deleting ${entitySingular}`);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  }, [entityType, baseUrl, requiresHospitalId, entitySingular, successMessages, errorMessages, makeApiRequest, fetchFunction, showSuccess, showError]);

  /**
   * Fetch operation (read)
   */
  const fetch = useCallback(async (hospitalId, additionalParams = {}) => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        ...(requiresHospitalId && { hospital_id: hospitalId }),
        ...additionalParams
      });
      
      const url = `${baseUrl.replace('/admin/', '/')}?${params}`;
      const result = await makeApiRequest(url, 'GET');

      if (!result.success) {
        console.error(`Failed to fetch ${entityType}:`, result.error);
        return { success: false, error: result.error };
      }

      const { data } = result;

      if (data.success) {
        return { success: true, data: data.data };
      } else {
        console.error(`Failed to fetch ${entityType}:`, data.message);
        return { success: false, error: data.message };
      }
    } catch (error) {
      console.error(`Fetch ${entityType} error:`, error);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  }, [entityType, baseUrl, requiresHospitalId, makeApiRequest]);

  return {
    create,
    update,
    delete: deleteEntity,
    fetch,
    loading
  };
}
