"""
Enhanced dynamic query generator for shared Redis implementation.
Generates semantic queries from hospital data using predefined templates with improved multilingual support.
"""

import logging
import re
from typing import Dict, Any, List, Tuple, Optional
from .config_loader import ConfigDataLoader

logger = logging.getLogger(__name__)


class QueryGenerator:
    """
    Production-ready query generator for semantic caching.
    Enhanced version with better multilingual support, error handling, and template management.
    """

    def __init__(self, config_loader: ConfigDataLoader = None):
        """
        Initialize query generator.
        
        Args:
            config_loader: Configuration data loader instance
        """
        self.config_loader = config_loader or ConfigDataLoader()
        self.query_patterns = self.config_loader.load_query_patterns()
        self.language_templates = {}
        
        # Load language templates for supported languages
        self._load_language_templates()

    def _load_language_templates(self):
        """Load templates for all supported languages with enhanced error handling."""
        try:
            supported_languages = self.config_loader.get_supported_languages()

            for language in supported_languages:
                try:
                    templates = self.config_loader.load_language_templates(language)
                    if templates:
                        self.language_templates[language] = templates
                        logger.info(f"Loaded templates for language: {language}")
                    else:
                        logger.warning(f"No templates found for language: {language}")
                except Exception as e:
                    logger.error(f"Error loading templates for language {language}: {e}")
                    continue

            if not self.language_templates:
                logger.warning("No language templates loaded, using default patterns")
                self._create_default_templates()

        except Exception as e:
            logger.error(f"Critical error in language template loading: {e}")
            self._create_default_templates()

    def _create_default_templates(self):
        """Create default templates if none are loaded."""
        default_template = {
            "language": "en",
            "doctor_name_queries": [
                "when does Dr. {doctor_name} come",
                "is Dr. {doctor_name} available",
                "Dr. {doctor_name} timings"
            ],
            "doctor_specialty_queries": [
                "when is the {specialty} available",
                "{specialty} doctor timings"
            ],
            "test_name_queries": [
                "what is the price of {test_name}",
                "{test_name} cost"
            ],
            "common_queries": {
                "greeting": ["hello", "hi"],
                "help": ["help me", "can you help"],
                "appointment": ["book appointment", "schedule appointment"]
            }
        }
        self.language_templates["en"] = default_template
        logger.info("Created default English templates")

    def generate_doctor_queries(self, doctors: List[Dict[str, Any]],
                              languages: List[str] = None) -> List[Tuple[str, Dict[str, Any]]]:
        """
        Generate semantic queries for doctors with enhanced multilingual support.

        Args:
            doctors: List of doctor data dictionaries
            languages: List of language codes to generate queries for

        Returns:
            List of (query, response_data) tuples
        """
        if not doctors:
            return []

        if languages is None:
            languages = list(self.language_templates.keys()) or ["en"]

        queries = []

        for doctor in doctors:
            try:
                doctor_name = doctor.get('name', 'Unknown Doctor')
                specialty = doctor.get('specialty', 'General Medicine')
                
                # Create comprehensive response data
                response_data = {
                    "type": "doctor_info",
                    "doctor_id": doctor.get('id', ''),
                    "name": doctor_name,
                    "specialty": specialty,
                    "schedule": doctor.get('schedule', 'Contact hospital for schedule'),
                    "qualifications": doctor.get('qualifications', ''),
                    "experience": doctor.get('experience', ''),
                    "consultation_fee": doctor.get('consultation_fee', 'Contact hospital for fees'),
                    "availability": doctor.get('availability', {}),
                    "contact": doctor.get('contact', {}),
                    "metadata": {
                        "generated_at": "runtime",
                        "source": "query_generator"
                    }
                }

                # Generate queries for each language
                for language in languages:
                    if language not in self.language_templates:
                        logger.warning(f"No templates for language {language}, skipping")
                        continue
                    
                    # Generate name-based queries
                    name_queries = self._generate_name_queries(doctor_name, response_data, language)
                    queries.extend(name_queries)

                    # Generate specialty-based queries
                    specialty_queries = self._generate_specialty_queries(specialty, response_data, language)
                    queries.extend(specialty_queries)

            except Exception as e:
                logger.error(f"Error generating queries for doctor {doctor.get('name', 'Unknown')}: {e}")
                continue

        logger.info(f"Generated {len(queries)} doctor queries for {len(doctors)} doctors")
        return queries

    def _generate_name_queries(self, doctor_name: str, response_data: Dict[str, Any], 
                              language: str) -> List[Tuple[str, Dict[str, Any]]]:
        """Generate name-based queries for a doctor."""
        queries = []
        templates = self.language_templates.get(language, {})
        name_templates = templates.get('doctor_name_queries', [])

        for template in name_templates:
            try:
                # Clean doctor name for template substitution
                clean_name = self._clean_doctor_name(doctor_name)
                query = template.format(doctor_name=clean_name)
                queries.append((query, response_data.copy()))
            except Exception as e:
                logger.debug(f"Error formatting name template '{template}' for {doctor_name}: {e}")
                continue

        return queries

    def _generate_specialty_queries(self, specialty: str, response_data: Dict[str, Any], 
                                   language: str) -> List[Tuple[str, Dict[str, Any]]]:
        """Generate specialty-based queries for a doctor."""
        queries = []
        templates = self.language_templates.get(language, {})
        specialty_templates = templates.get('doctor_specialty_queries', [])

        for template in specialty_templates:
            try:
                query = template.format(specialty=specialty)
                queries.append((query, response_data.copy()))
            except Exception as e:
                logger.debug(f"Error formatting specialty template '{template}' for {specialty}: {e}")
                continue

        return queries

    def _clean_doctor_name(self, name: str) -> str:
        """Clean doctor name for template substitution."""
        # Remove common prefixes and clean the name
        name = re.sub(r'^(Dr\.?|Doctor)\s*', '', name, flags=re.IGNORECASE)
        name = name.strip()
        return name

    def generate_test_queries(self, tests: List[Dict[str, Any]],
                             languages: List[str] = None) -> List[Tuple[str, Dict[str, Any]]]:
        """
        Generate semantic queries for tests with enhanced multilingual support.

        Args:
            tests: List of test data dictionaries
            languages: List of language codes to generate queries for

        Returns:
            List of (query, response_data) tuples
        """
        if not tests:
            return []

        if languages is None:
            languages = list(self.language_templates.keys()) or ["en"]

        queries = []

        for test in tests:
            try:
                test_name = test.get('name', 'Unknown Test')
                
                # Create comprehensive response data
                response_data = {
                    "type": "test_info",
                    "test_id": test.get('id', ''),
                    "name": test_name,
                    "category": test.get('category', 'General'),
                    "price": test.get('price', 'Contact hospital for pricing'),
                    "duration": test.get('duration', 'Contact hospital for duration'),
                    "preparation": test.get('preparation', 'No special preparation required'),
                    "availability": test.get('availability', 'Monday to Saturday'),
                    "description": test.get('description', ''),
                    "metadata": {
                        "generated_at": "runtime",
                        "source": "query_generator"
                    }
                }

                # Generate queries for each language
                for language in languages:
                    if language not in self.language_templates:
                        logger.warning(f"No templates for language {language}, skipping")
                        continue
                    
                    # Generate test name queries
                    test_queries = self._generate_test_name_queries(test_name, response_data, language)
                    queries.extend(test_queries)

            except Exception as e:
                logger.error(f"Error generating queries for test {test.get('name', 'Unknown')}: {e}")
                continue

        logger.info(f"Generated {len(queries)} test queries for {len(tests)} tests")
        return queries

    def _generate_test_name_queries(self, test_name: str, response_data: Dict[str, Any], 
                                   language: str) -> List[Tuple[str, Dict[str, Any]]]:
        """Generate name-based queries for a test."""
        queries = []
        templates = self.language_templates.get(language, {})
        test_templates = templates.get('test_name_queries', [])

        for template in test_templates:
            try:
                query = template.format(test_name=test_name)
                queries.append((query, response_data.copy()))
            except Exception as e:
                logger.debug(f"Error formatting test template '{template}' for {test_name}: {e}")
                continue

        return queries

    def generate_common_queries(self, hospital_id: str, 
                               languages: List[str] = None) -> List[Tuple[str, Dict[str, Any]]]:
        """
        Generate common queries (greetings, help, etc.) for a hospital.

        Args:
            hospital_id: Hospital identifier
            languages: List of language codes to generate queries for

        Returns:
            List of (query, response_data) tuples
        """
        if languages is None:
            languages = list(self.language_templates.keys()) or ["en"]

        queries = []

        for language in languages:
            if language not in self.language_templates:
                continue
                
            templates = self.language_templates[language]
            common_queries = templates.get('common_queries', {})

            for category, query_list in common_queries.items():
                response_data = {
                    "type": "common_response",
                    "category": category,
                    "hospital_id": hospital_id,
                    "language": language,
                    "metadata": {
                        "generated_at": "runtime",
                        "source": "query_generator"
                    }
                }

                for query in query_list:
                    queries.append((query, response_data.copy()))

        logger.info(f"Generated {len(queries)} common queries for hospital {hospital_id}")
        return queries

    def get_query_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about loaded templates and query generation capabilities.
        
        Returns:
            Dictionary with statistics
        """
        stats = {
            "loaded_languages": list(self.language_templates.keys()),
            "total_languages": len(self.language_templates),
            "template_details": {}
        }

        for language, templates in self.language_templates.items():
            stats["template_details"][language] = {
                "doctor_name_templates": len(templates.get('doctor_name_queries', [])),
                "doctor_specialty_templates": len(templates.get('doctor_specialty_queries', [])),
                "test_name_templates": len(templates.get('test_name_queries', [])),
                "common_query_categories": len(templates.get('common_queries', {}))
            }

        return stats

    def reload_templates(self):
        """Reload all language templates from configuration."""
        self.language_templates.clear()
        self.config_loader.clear_cache()
        self._load_language_templates()
        logger.info("Reloaded all language templates")
