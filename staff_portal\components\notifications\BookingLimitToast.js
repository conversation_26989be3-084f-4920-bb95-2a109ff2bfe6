import React, { useEffect } from 'react';
import { CheckCircle, XCircle, AlertCircle, Clock, Calendar, User } from 'react-feather';

/**
 * Specialized Toast notification component for booking limit updates
 * Shows detailed information about booking limit changes and sync status
 */
export function BookingLimitToast({ 
  type, 
  doctorName, 
  oldLimits, 
  newLimits, 
  syncStatus, 
  errorDetails,
  onClose 
}) {
  useEffect(() => {
    // Auto-dismiss success toasts after 8 seconds, errors after 12 seconds
    const dismissTime = type === 'success' ? 8000 : 12000;
    const timer = setTimeout(() => {
      if (onClose) onClose();
    }, dismissTime);

    return () => clearTimeout(timer);
  }, [onClose, type]);

  const getToastStyles = () => {
    switch (type) {
      case 'success':
        return {
          border: 'border-l-4 border-green-500',
          bg: 'bg-green-50',
          icon: <CheckCircle className="w-6 h-6 text-green-500" />,
          titleColor: 'text-green-800',
          messageColor: 'text-green-700'
        };
      case 'error':
        return {
          border: 'border-l-4 border-red-500',
          bg: 'bg-red-50',
          icon: <XCircle className="w-6 h-6 text-red-500" />,
          titleColor: 'text-red-800',
          messageColor: 'text-red-700'
        };
      case 'warning':
        return {
          border: 'border-l-4 border-yellow-500',
          bg: 'bg-yellow-50',
          icon: <AlertCircle className="w-6 h-6 text-yellow-500" />,
          titleColor: 'text-yellow-800',
          messageColor: 'text-yellow-700'
        };
      default:
        return {
          border: 'border-l-4 border-blue-500',
          bg: 'bg-blue-50',
          icon: <Clock className="w-6 h-6 text-blue-500" />,
          titleColor: 'text-blue-800',
          messageColor: 'text-blue-700'
        };
    }
  };

  const styles = getToastStyles();

  const formatLimitsChange = () => {
    if (!oldLimits || !newLimits) return '';
    
    const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    const dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    
    const changes = [];
    days.forEach((day, index) => {
      const oldLimit = oldLimits[day] || 0;
      const newLimit = newLimits[day] || 0;
      
      if (oldLimit !== newLimit) {
        changes.push(`${dayNames[index]}(${oldLimit}→${newLimit})`);
      }
    });
    
    return changes.length > 0 ? changes.join(', ') : 'No changes detected';
  };

  const formatCurrentLimits = () => {
    if (!newLimits) return '';
    
    const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    const dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    
    return days.map((day, index) => `${dayNames[index]}(${newLimits[day] || 0})`).join(', ');
  };

  const getSyncStatusIcon = () => {
    if (!syncStatus) return null;
    
    switch (syncStatus.status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500 animate-spin" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  const getTitle = () => {
    switch (type) {
      case 'success':
        return 'Booking Limits Updated Successfully';
      case 'error':
        return 'Booking Limits Update Failed';
      case 'warning':
        return 'Booking Limits Updated with Warnings';
      default:
        return 'Booking Limits Update';
    }
  };

  return (
    <div 
      className={`fixed bottom-4 right-4 max-w-md w-full ${styles.bg} shadow-lg rounded-lg pointer-events-auto ${styles.border} animate-slide-in-right z-50`}
      role="alert"
    >
      <div className="p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            {styles.icon}
          </div>
          
          <div className="ml-3 w-0 flex-1">
            {/* Title */}
            <div className={`text-sm font-medium ${styles.titleColor} mb-2`}>
              {getTitle()}
            </div>
            
            {/* Doctor Info */}
            {doctorName && (
              <div className={`flex items-center text-sm ${styles.messageColor} mb-2`}>
                <User className="w-4 h-4 mr-1" />
                <span className="font-medium">{doctorName}</span>
              </div>
            )}
            
            {/* Success Details */}
            {type === 'success' && (
              <div className={`text-sm ${styles.messageColor} space-y-1`}>
                {oldLimits && (
                  <div>
                    <span className="font-medium">Changes: </span>
                    <span className="text-xs">{formatLimitsChange()}</span>
                  </div>
                )}
                <div>
                  <span className="font-medium">Current: </span>
                  <span className="text-xs">{formatCurrentLimits()}</span>
                </div>
              </div>
            )}
            
            {/* Error Details */}
            {type === 'error' && errorDetails && (
              <div className={`text-sm ${styles.messageColor} mt-2`}>
                <span className="font-medium">Error: </span>
                <span>{errorDetails}</span>
              </div>
            )}
            
            {/* Sync Status */}
            {syncStatus && (
              <div className={`flex items-center text-xs ${styles.messageColor} mt-2 pt-2 border-t border-gray-200`}>
                {getSyncStatusIcon()}
                <span className="ml-1">
                  Voice Agent Sync: {syncStatus.status === 'success' ? 'Synced' : 
                                   syncStatus.status === 'error' ? 'Failed' : 
                                   syncStatus.status === 'pending' ? 'Syncing...' : 'Unknown'}
                </span>
                {syncStatus.message && (
                  <span className="ml-2 text-gray-500">({syncStatus.message})</span>
                )}
              </div>
            )}
            
            {/* Timestamp */}
            <div className={`flex items-center text-xs ${styles.messageColor} mt-2 opacity-75`}>
              <Calendar className="w-3 h-3 mr-1" />
              <span>{new Date().toLocaleTimeString()}</span>
            </div>
          </div>
          
          {/* Close Button */}
          <div className="ml-4 flex-shrink-0 flex">
            <button
              className={`${styles.bg} rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500`}
              onClick={(e) => {
                e.stopPropagation();
                if (onClose) onClose();
              }}
            >
              <span className="sr-only">Close</span>
              <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default BookingLimitToast;
