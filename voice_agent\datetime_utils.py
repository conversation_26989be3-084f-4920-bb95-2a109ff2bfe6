"""
Datetime Utilities for Voice Health Portal

This module provides robust datetime parsing and manipulation utilities
to handle various datetime formats consistently across the system.

Features:
- Robust datetime parsing with multiple format support
- Safe datetime construction methods
- Timezone handling
- Input validation and error handling
"""

import logging
from datetime import datetime, date, time, timedelta
from typing import Optional, Union, List, Dict, Any
import re
from functools import lru_cache
import threading
from collections import OrderedDict

logger = logging.getLogger(__name__)

# Thread-safe cache for frequently used datetime operations
_datetime_cache_lock = threading.Lock()
_datetime_format_cache: Dict[str, datetime] = {}
_max_cache_size = 1000

# Common datetime formats used in the system
DATETIME_FORMATS = [
    '%Y-%m-%dT%H:%M:%S.%fZ',      # ISO format with microseconds and Z
    '%Y-%m-%dT%H:%M:%SZ',         # ISO format with Z
    '%Y-%m-%dT%H:%M:%S.%f%z',     # ISO format with microseconds and timezone
    '%Y-%m-%dT%H:%M:%S%z',        # ISO format with timezone
    '%Y-%m-%dT%H:%M:%S.%f',       # ISO format with microseconds
    '%Y-%m-%dT%H:%M:%S',          # ISO format basic
    '%Y-%m-%d %H:%M:%S.%f',       # Space separated with microseconds
    '%Y-%m-%d %H:%M:%S',          # Space separated basic
    '%Y-%m-%d %H:%M',             # Space separated without seconds
]

# Time formats for parsing time strings
TIME_FORMATS = [
    '%H:%M:%S.%f',                # With microseconds
    '%H:%M:%S',                   # With seconds
    '%H:%M',                      # Basic HH:MM
    '%I:%M %p',                   # 12-hour format with AM/PM
    '%I:%M:%S %p',                # 12-hour format with seconds and AM/PM
]

# Date formats
DATE_FORMATS = [
    '%Y-%m-%d',                   # ISO date format
    '%m/%d/%Y',                   # US format
    '%d/%m/%Y',                   # European format
    '%Y/%m/%d',                   # Alternative format
]

def parse_datetime_robust(datetime_input: Union[str, datetime, None]) -> Optional[datetime]:
    """
    Parse datetime string with multiple format attempts and caching for performance.

    Args:
        datetime_input: String, datetime object, or None

    Returns:
        Parsed datetime object or None if parsing fails
    """
    if not datetime_input:
        return None

    # If already a datetime object, return as-is
    if isinstance(datetime_input, datetime):
        return datetime_input

    # Must be a string at this point
    if not isinstance(datetime_input, str):
        logger.error(f"Invalid datetime input type: {type(datetime_input)}")
        return None

    datetime_str = datetime_input.strip()
    if not datetime_str:
        return None

    # Check cache first for performance
    cached_result = _manage_parse_cache(datetime_str)
    if cached_result is not None:
        return cached_result

    # Normalize timezone indicators
    normalized_str = datetime_str.replace('Z', '+00:00')

    # Try each format
    for fmt in DATETIME_FORMATS:
        try:
            parsed_dt = datetime.strptime(normalized_str, fmt)
            # Cache the successful parse
            _manage_parse_cache(datetime_str, parsed_dt)
            return parsed_dt
        except ValueError:
            continue

    # Try isoformat as last resort
    try:
        parsed_dt = datetime.fromisoformat(normalized_str)
        # Cache the successful parse
        _manage_parse_cache(datetime_str, parsed_dt)
        return parsed_dt
    except ValueError:
        pass

    logger.error(f"Unable to parse datetime: {datetime_str}")
    return None

def parse_date_robust(date_input: Union[str, date, datetime, None]) -> Optional[date]:
    """
    Parse date string with multiple format attempts.
    
    Args:
        date_input: String, date object, datetime object, or None
        
    Returns:
        Parsed date object or None if parsing fails
    """
    if not date_input:
        return None
        
    # If already a date or datetime object, extract date
    if isinstance(date_input, datetime):
        return date_input.date()
    elif isinstance(date_input, date):
        return date_input
        
    # Must be a string at this point
    if not isinstance(date_input, str):
        logger.error(f"Invalid date input type: {type(date_input)}")
        return None
    
    date_str = date_input.strip()
    if not date_str:
        return None
    
    # Try each date format
    for fmt in DATE_FORMATS:
        try:
            return datetime.strptime(date_str, fmt).date()
        except ValueError:
            continue
    
    logger.error(f"Unable to parse date: {date_str}")
    return None

def parse_time_robust(time_input: Union[str, time, None]) -> Optional[time]:
    """
    Parse time string with multiple format attempts.
    
    Args:
        time_input: String, time object, or None
        
    Returns:
        Parsed time object or None if parsing fails
    """
    if not time_input:
        return None
        
    # If already a time object, return as-is
    if isinstance(time_input, time):
        return time_input
        
    # Must be a string at this point
    if not isinstance(time_input, str):
        logger.error(f"Invalid time input type: {type(time_input)}")
        return None
    
    time_str = time_input.strip()
    if not time_str:
        return None
    
    # Try each time format
    for fmt in TIME_FORMATS:
        try:
            return datetime.strptime(time_str, fmt).time()
        except ValueError:
            continue
    
    logger.error(f"Unable to parse time: {time_str}")
    return None

def combine_date_time_safe(date_input: Union[str, date, datetime], 
                          time_input: Union[str, time]) -> Optional[datetime]:
    """
    Safely combine date and time using datetime.combine().
    
    Args:
        date_input: Date string, date object, or datetime object
        time_input: Time string or time object
        
    Returns:
        Combined datetime object or None if combination fails
    """
    try:
        # Parse date
        parsed_date = parse_date_robust(date_input)
        if not parsed_date:
            logger.error(f"Failed to parse date: {date_input}")
            return None
        
        # Parse time
        parsed_time = parse_time_robust(time_input)
        if not parsed_time:
            logger.error(f"Failed to parse time: {time_input}")
            return None
        
        # Combine safely
        return datetime.combine(parsed_date, parsed_time)
        
    except Exception as e:
        logger.error(f"Error combining date {date_input} and time {time_input}: {e}")
        return None

def validate_time_format(time_str: str) -> bool:
    """
    Validate time string format using regex.
    
    Args:
        time_str: Time string to validate
        
    Returns:
        True if format is valid, False otherwise
    """
    if not isinstance(time_str, str):
        return False
    
    # HH:MM format (24-hour)
    time_pattern = r'^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$'
    return bool(re.match(time_pattern, time_str.strip()))

def validate_date_format(date_str: str) -> bool:
    """
    Validate date string format.
    
    Args:
        date_str: Date string to validate
        
    Returns:
        True if format is valid, False otherwise
    """
    if not isinstance(date_str, str):
        return False
    
    # Try to parse the date
    return parse_date_robust(date_str) is not None

def format_time_display(time_input: Union[str, time, datetime]) -> Optional[str]:
    """
    Format time for display in 12-hour format.
    
    Args:
        time_input: Time string, time object, or datetime object
        
    Returns:
        Formatted time string (e.g., "9:00 AM") or None if formatting fails
    """
    try:
        if isinstance(time_input, datetime):
            time_obj = time_input.time()
        elif isinstance(time_input, time):
            time_obj = time_input
        else:
            time_obj = parse_time_robust(time_input)
            
        if not time_obj:
            return None
        
        # Create a datetime object for formatting
        temp_datetime = datetime.combine(date.today(), time_obj)
        return temp_datetime.strftime('%I:%M %p').lstrip('0')
        
    except Exception as e:
        logger.error(f"Error formatting time {time_input}: {e}")
        return None

def get_time_range_minutes(start_time: Union[str, time],
                          end_time: Union[str, time]) -> Optional[int]:
    """
    Calculate the difference between two times in minutes.

    Args:
        start_time: Start time string or time object
        end_time: End time string or time object

    Returns:
        Difference in minutes or None if calculation fails
    """
    try:
        start_obj = parse_time_robust(start_time)
        end_obj = parse_time_robust(end_time)

        if not start_obj or not end_obj:
            return None

        # Convert to datetime objects for calculation
        base_date = date.today()
        start_dt = datetime.combine(base_date, start_obj)
        end_dt = datetime.combine(base_date, end_obj)

        # Handle case where end time is next day
        if end_dt <= start_dt:
            end_dt = end_dt.replace(day=end_dt.day + 1)

        diff = end_dt - start_dt
        return int(diff.total_seconds() / 60)

    except Exception as e:
        logger.error(f"Error calculating time range {start_time} to {end_time}: {e}")
        return None

# Performance-optimized functions for high-frequency operations

# Cache for frequently parsed datetime strings
_parse_cache: 'OrderedDict[str, datetime]' = OrderedDict()
_parse_cache_hits = 0
_parse_cache_misses = 0

def _manage_parse_cache(key: str, value: datetime = None) -> Optional[datetime]:
    """
    Thread-safe cache management for datetime parsing with optimized eviction.

    Args:
        key: Cache key (datetime string)
        value: Value to cache (None for retrieval)

    Returns:
        Cached datetime object or None
    """
    global _parse_cache_hits, _parse_cache_misses

    with _datetime_cache_lock:
        if value is not None:
            # Store in cache with optimized eviction
            if len(_parse_cache) >= _max_cache_size:
                # Use more efficient eviction strategy
                # Remove 25% of entries using OrderedDict behavior if available
                try:
                    # Try to use OrderedDict-like behavior for better LRU
                    if hasattr(_parse_cache, 'popitem'):
                        eviction_count = _max_cache_size // 4
                        for _ in range(eviction_count):
                            if _parse_cache:
                                _parse_cache.popitem(last=False)  # Remove oldest
                            else:
                                break
                    else:
                        # Fallback to key-based removal
                        oldest_keys = list(_parse_cache.keys())[:_max_cache_size // 4]
                        for old_key in oldest_keys:
                            _parse_cache.pop(old_key, None)
                except Exception:
                    # Fallback to simple key removal
                    oldest_keys = list(_parse_cache.keys())[:_max_cache_size // 4]
                    for old_key in oldest_keys:
                        _parse_cache.pop(old_key, None)

            _parse_cache[key] = value
            return value
        else:
            # Retrieve from cache
            if key in _parse_cache:
                _parse_cache_hits += 1
                return _parse_cache[key]
            else:
                _parse_cache_misses += 1
                return None

def get_current_iso_timestamp() -> str:
    """
    Get current timestamp in ISO format.

    Note: This function was previously cached but caching timestamps
    is counterproductive as they become stale immediately.
    Use this function for all timestamp generation needs.

    Returns:
        Current timestamp in ISO format
    """
    return datetime.now().isoformat()

def get_current_iso_timestamp_fresh() -> str:
    """
    Get current timestamp in ISO format without caching.

    Note: This function is maintained for backward compatibility.
    Both get_current_iso_timestamp() and get_current_iso_timestamp_fresh()
    now return fresh timestamps.

    Returns:
        Current timestamp in ISO format
    """
    return datetime.now().isoformat()

def clear_datetime_cache() -> Dict[str, int]:
    """
    Clear the datetime parsing cache and return statistics.

    Returns:
        Dictionary with cache statistics
    """
    global _parse_cache_hits, _parse_cache_misses

    with _datetime_cache_lock:
        cache_size = len(_parse_cache)
        _parse_cache.clear()

        stats = {
            'cache_size_before_clear': cache_size,
            'cache_hits': _parse_cache_hits,
            'cache_misses': _parse_cache_misses,
            'hit_ratio': _parse_cache_hits / (_parse_cache_hits + _parse_cache_misses) if (_parse_cache_hits + _parse_cache_misses) > 0 else 0
        }

        # Reset counters
        _parse_cache_hits = 0
        _parse_cache_misses = 0

        return stats

def get_cache_stats() -> Dict[str, Any]:
    """
    Get current cache statistics without clearing.

    Returns:
        Dictionary with cache statistics
    """
    with _datetime_cache_lock:
        return {
            'cache_size': len(_parse_cache),
            'cache_hits': _parse_cache_hits,
            'cache_misses': _parse_cache_misses,
            'hit_ratio': _parse_cache_hits / (_parse_cache_hits + _parse_cache_misses) if (_parse_cache_hits + _parse_cache_misses) > 0 else 0,
            'max_cache_size': _max_cache_size
        }

@lru_cache(maxsize=64)
def get_date_string_cached(days_offset: int = 0) -> str:
    """
    Get date string with offset, cached for performance.

    Args:
        days_offset: Number of days to offset from today (can be negative)

    Returns:
        Date string in YYYY-MM-DD format
    """
    target_date = datetime.now().date() + timedelta(days=days_offset)
    return target_date.strftime('%Y-%m-%d')

def get_day_name_from_date(date_input: Union[str, date, datetime]) -> Optional[str]:
    """
    Get day name from date input.

    Args:
        date_input: Date string, date object, or datetime object

    Returns:
        Day name in lowercase (e.g., 'monday') or None if parsing fails
    """
    try:
        parsed_date = parse_date_robust(date_input)
        if not parsed_date:
            return None

        return parsed_date.strftime('%A').lower()

    except Exception as e:
        logger.error(f"Error getting day name from date {date_input}: {e}")
        return None

def calculate_date_offset(start_date: Union[str, date, datetime],
                         days_offset: int) -> Optional[str]:
    """
    Calculate date with offset from start date.

    Args:
        start_date: Starting date
        days_offset: Number of days to add/subtract

    Returns:
        Calculated date in YYYY-MM-DD format or None if calculation fails
    """
    try:
        parsed_date = parse_date_robust(start_date)
        if not parsed_date:
            return None

        target_date = parsed_date + timedelta(days=days_offset)
        return target_date.strftime('%Y-%m-%d')

    except Exception as e:
        logger.error(f"Error calculating date offset from {start_date} + {days_offset}: {e}")
        return None

def is_same_day(date1: Union[str, date, datetime],
                date2: Union[str, date, datetime]) -> bool:
    """
    Check if two dates are the same day.

    Args:
        date1: First date
        date2: Second date

    Returns:
        True if same day, False otherwise
    """
    try:
        parsed_date1 = parse_date_robust(date1)
        parsed_date2 = parse_date_robust(date2)

        if not parsed_date1 or not parsed_date2:
            return False

        return parsed_date1 == parsed_date2

    except Exception as e:
        logger.error(f"Error comparing dates {date1} and {date2}: {e}")
        return False

def get_timestamp_for_filename() -> str:
    """
    Get timestamp formatted for use in filenames.

    Returns:
        Timestamp in YYYYMMDD_HHMMSS format
    """
    return datetime.now().strftime('%Y%m%d_%H%M%S')

def parse_postgres_datetime(datetime_str: str) -> Optional[datetime]:
    """
    Parse PostgreSQL datetime format specifically.

    Args:
        datetime_str: PostgreSQL datetime string

    Returns:
        Parsed datetime object or None if parsing fails
    """
    if not datetime_str or not isinstance(datetime_str, str):
        return None

    # PostgreSQL common formats
    postgres_formats = [
        '%Y-%m-%d %H:%M:%S.%f',  # With microseconds
        '%Y-%m-%d %H:%M:%S',     # Without microseconds
        '%Y-%m-%d %H:%M',        # Without seconds
    ]

    for fmt in postgres_formats:
        try:
            return datetime.strptime(datetime_str.strip(), fmt)
        except ValueError:
            continue

    # Fallback to robust parsing
    return parse_datetime_robust(datetime_str)

def format_for_postgres(datetime_input: Union[str, datetime]) -> Optional[str]:
    """
    Format datetime for PostgreSQL storage.

    Args:
        datetime_input: Datetime to format

    Returns:
        PostgreSQL-compatible datetime string or None if formatting fails
    """
    try:
        if isinstance(datetime_input, str):
            parsed_dt = parse_datetime_robust(datetime_input)
        else:
            parsed_dt = datetime_input

        if not parsed_dt:
            return None

        return parsed_dt.strftime('%Y-%m-%d %H:%M:%S')

    except Exception as e:
        logger.error(f"Error formatting datetime for PostgreSQL {datetime_input}: {e}")
        return None

# Batch processing functions for high-performance operations

def parse_datetime_batch(datetime_inputs: List[Union[str, datetime, None]]) -> List[Optional[datetime]]:
    """
    Parse multiple datetime inputs efficiently with batch processing.

    Args:
        datetime_inputs: List of datetime inputs to parse

    Returns:
        List of parsed datetime objects (None for failed parses)
    """
    results = []

    for datetime_input in datetime_inputs:
        try:
            parsed_dt = parse_datetime_robust(datetime_input)
            results.append(parsed_dt)
        except Exception as e:
            logger.warning(f"Failed to parse datetime in batch: {datetime_input}, error: {e}")
            results.append(None)

    return results

def format_datetime_batch(datetime_inputs: List[Union[str, datetime]],
                         format_string: str = '%Y-%m-%d %H:%M:%S') -> List[Optional[str]]:
    """
    Format multiple datetime inputs efficiently with batch processing.

    Args:
        datetime_inputs: List of datetime inputs to format
        format_string: Format string for output

    Returns:
        List of formatted datetime strings (None for failed formats)
    """
    results = []

    for datetime_input in datetime_inputs:
        try:
            if isinstance(datetime_input, str):
                parsed_dt = parse_datetime_robust(datetime_input)
            else:
                parsed_dt = datetime_input

            if parsed_dt:
                formatted_str = parsed_dt.strftime(format_string)
                results.append(formatted_str)
            else:
                results.append(None)
        except Exception as e:
            logger.warning(f"Failed to format datetime in batch: {datetime_input}, error: {e}")
            results.append(None)

    return results

@lru_cache(maxsize=256)
def get_weekday_cached(date_str: str) -> Optional[str]:
    """
    Get weekday name with caching for frequently accessed dates.

    Args:
        date_str: Date string in YYYY-MM-DD format

    Returns:
        Weekday name in lowercase or None if parsing fails
    """
    try:
        parsed_date = parse_date_robust(date_str)
        if parsed_date:
            return parsed_date.strftime('%A').lower()
        return None
    except Exception as e:
        logger.error(f"Error getting weekday for {date_str}: {e}")
        return None

@lru_cache(maxsize=128)
def calculate_business_days(start_date_str: str, end_date_str: str) -> Optional[int]:
    """
    Calculate business days between two dates with caching.

    Args:
        start_date_str: Start date in YYYY-MM-DD format
        end_date_str: End date in YYYY-MM-DD format

    Returns:
        Number of business days or None if calculation fails
    """
    try:
        start_date = parse_date_robust(start_date_str)
        end_date = parse_date_robust(end_date_str)

        if not start_date or not end_date:
            return None

        # Simple business day calculation (excludes weekends)
        business_days = 0
        current_date = start_date

        while current_date <= end_date:
            if current_date.weekday() < 5:  # Monday = 0, Sunday = 6
                business_days += 1
            current_date += timedelta(days=1)

        return business_days

    except Exception as e:
        logger.error(f"Error calculating business days from {start_date_str} to {end_date_str}: {e}")
        return None

def validate_datetime_range(start_datetime: Union[str, datetime],
                          end_datetime: Union[str, datetime]) -> Dict[str, Any]:
    """
    Validate that a datetime range is logical and return validation results.

    Args:
        start_datetime: Start datetime
        end_datetime: End datetime

    Returns:
        Dictionary with validation results
    """
    result = {
        'valid': False,
        'errors': [],
        'warnings': [],
        'duration_minutes': None,
        'start_parsed': None,
        'end_parsed': None
    }

    try:
        # Parse start datetime
        if isinstance(start_datetime, str):
            start_parsed = parse_datetime_robust(start_datetime)
        else:
            start_parsed = start_datetime

        if not start_parsed:
            result['errors'].append("Invalid start datetime format")
            return result

        # Parse end datetime
        if isinstance(end_datetime, str):
            end_parsed = parse_datetime_robust(end_datetime)
        else:
            end_parsed = end_datetime

        if not end_parsed:
            result['errors'].append("Invalid end datetime format")
            return result

        result['start_parsed'] = start_parsed
        result['end_parsed'] = end_parsed

        # Validate logical order
        if start_parsed >= end_parsed:
            result['errors'].append("Start datetime must be before end datetime")
            return result

        # Calculate duration
        duration = end_parsed - start_parsed
        duration_minutes = int(duration.total_seconds() / 60)
        result['duration_minutes'] = duration_minutes

        # Add warnings for unusual durations
        if duration_minutes < 5:
            result['warnings'].append("Duration is very short (less than 5 minutes)")
        elif duration_minutes > 480:  # 8 hours
            result['warnings'].append("Duration is very long (more than 8 hours)")

        result['valid'] = True
        return result

    except Exception as e:
        result['errors'].append(f"Unexpected error during validation: {str(e)}")
        return result

# Utility function for performance monitoring
def get_performance_stats() -> Dict[str, Any]:
    """
    Get performance statistics for datetime operations.

    Returns:
        Dictionary with performance metrics
    """
    cache_stats = get_cache_stats()

    return {
        'datetime_cache': cache_stats,
        'lru_cache_info': {
            'get_date_string_cached': get_date_string_cached.cache_info()._asdict(),
            'get_weekday_cached': get_weekday_cached.cache_info()._asdict(),
            'calculate_business_days': calculate_business_days.cache_info()._asdict(),
        },
        'notes': {
            'get_current_iso_timestamp': 'No longer cached - timestamps should always be fresh'
        }
    }
