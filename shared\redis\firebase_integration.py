"""
Enhanced Firebase Integration for Shared Redis Implementation

Provides automatic Firebase data synchronization with Redis caching:
- Automatic key generation based on Firebase data structure
- Concurrent processing with batching for optimal performance
- Semantic embedding generation for enhanced search capabilities
- Real-time data synchronization and cache invalidation
"""

import asyncio
import logging
import time
from typing import Dict, Any, List, Optional, Tuple
import json
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from ..base_operations import RedisOperations
from ..semantic.indic_bert_cache import IndicBertCache
from ..semantic.multilingual_cache import MultilingualCache
from ..config import get_redis_config

logger = logging.getLogger(__name__)


@dataclass
class FirebaseSyncResult:
    """Result of Firebase synchronization operation."""
    hospital_id: str
    total_items: int
    cached_items: int
    failed_items: int
    processing_time_ms: float
    errors: List[str]


class FirebaseRedisIntegration:
    """
    Enhanced Firebase integration with automatic Redis caching.
    Handles concurrent data processing and semantic embedding generation.
    """
    
    def __init__(self, redis_ops: Optional[RedisOperations] = None):
        """Initialize Firebase Redis integration."""
        self.redis_ops = redis_ops or RedisOperations()
        self.config = get_redis_config()
        self.indic_bert_cache = IndicBertCache(self.redis_ops)
        self.multilingual_cache = MultilingualCache(self.redis_ops)
        
        # Concurrency settings
        self.max_workers = 10
        self.batch_size = 50
        self.executor = ThreadPoolExecutor(max_workers=self.max_workers)
        
        # Statistics
        self._stats = {
            "sync_operations": 0,
            "total_items_processed": 0,
            "total_items_cached": 0,
            "total_errors": 0,
            "total_time": 0.0
        }
    
    def _generate_redis_key(self, hospital_id: str, data_type: str, item_id: str, 
                           category: Optional[str] = None) -> str:
        """
        Generate standardized Redis keys based on Firebase data structure.
        
        Args:
            hospital_id: Hospital identifier
            data_type: Type of data (doctors, tests, staff, etc.)
            item_id: Unique identifier for the item
            category: Optional category for grouping
            
        Returns:
            Standardized Redis key
        """
        if category:
            return f"hospital:{hospital_id}:{data_type}:{category}:{item_id}"
        else:
            return f"hospital:{hospital_id}:{data_type}:{item_id}"
    
    def _generate_semantic_queries(self, item_data: Dict[str, Any], 
                                 data_type: str) -> List[Dict[str, str]]:
        """
        Generate semantic queries for automatic caching.
        
        Args:
            item_data: Data item from Firebase
            data_type: Type of data (doctors, tests, etc.)
            
        Returns:
            List of query-response pairs for semantic caching
        """
        queries = []
        
        if data_type == "doctors":
            name = item_data.get("name", "")
            specialty = item_data.get("specialty", "")
            schedule = item_data.get("schedule", "regular hours")
            
            if name:
                # English queries
                queries.extend([
                    {
                        "query": f"when does doctor {name} come",
                        "response": f"Dr. {name} is available from {schedule}.",
                        "language": "en"
                    },
                    {
                        "query": f"doctor {name} timing",
                        "response": f"Dr. {name} ({specialty}) is available from {schedule}.",
                        "language": "en"
                    },
                    {
                        "query": f"is doctor {name} available",
                        "response": f"Dr. {name} is available from {schedule}.",
                        "language": "en"
                    }
                ])
                
                # Hindi queries
                queries.extend([
                    {
                        "query": f"डॉक्टर {name} कब आते हैं",
                        "response": f"डॉ. {name} {schedule} उपलब्ध हैं।",
                        "language": "hi"
                    },
                    {
                        "query": f"डॉक्टर {name} का समय",
                        "response": f"डॉ. {name} ({specialty}) {schedule} उपलब्ध हैं।",
                        "language": "hi"
                    },
                    {
                        "query": f"{name} doctor kab aate hain",
                        "response": f"Dr. {name} {schedule} available hain.",
                        "language": "hi"
                    }
                ])
        
        elif data_type == "tests":
            name = item_data.get("name", "")
            price = item_data.get("price", "Contact hospital")
            duration = item_data.get("duration", "Contact hospital")
            
            if name:
                # English queries
                queries.extend([
                    {
                        "query": f"{name} test cost",
                        "response": f"{name} test costs {price}.",
                        "language": "en"
                    },
                    {
                        "query": f"how much is {name} test",
                        "response": f"{name} test costs {price} and takes {duration}.",
                        "language": "en"
                    },
                    {
                        "query": f"{name} test price",
                        "response": f"{name} test is available for {price}.",
                        "language": "en"
                    }
                ])
                
                # Hindi queries
                queries.extend([
                    {
                        "query": f"{name} टेस्ट की कीमत",
                        "response": f"{name} टेस्ट की कीमत {price} है।",
                        "language": "hi"
                    },
                    {
                        "query": f"{name} test kitne ka hai",
                        "response": f"{name} test {price} mein hai aur {duration} lagta hai.",
                        "language": "hi"
                    }
                ])
        
        return queries
    
    async def _cache_item_with_semantics(self, hospital_id: str, data_type: str, 
                                       item_id: str, item_data: Dict[str, Any]) -> bool:
        """
        Cache individual item with semantic queries.
        
        Args:
            hospital_id: Hospital identifier
            data_type: Type of data
            item_id: Item identifier
            item_data: Item data from Firebase
            
        Returns:
            Success status
        """
        try:
            # Cache the raw data
            redis_key = self._generate_redis_key(hospital_id, data_type, item_id)
            success = await self.redis_ops.set_async(
                redis_key, 
                item_data, 
                ttl=self.config.default_ttl
            )
            
            if not success:
                return False
            
            # Generate and cache semantic queries
            semantic_queries = self._generate_semantic_queries(item_data, data_type)
            
            for query_data in semantic_queries:
                if query_data["language"] == "hi":
                    # Use IndicBERT for Hindi queries
                    await self.indic_bert_cache.cache_response_async(
                        query_data["query"],
                        query_data["response"],
                        hospital_id,
                        data_type
                    )
                else:
                    # Use multilingual cache for English queries
                    await self.multilingual_cache.cache_response_async(
                        query_data["query"],
                        query_data["response"],
                        hospital_id,
                        data_type,
                        language_hint="en"
                    )
            
            return True
            
        except Exception as e:
            logger.error(f"Error caching item {item_id}: {e}")
            return False
    
    async def _process_batch(self, hospital_id: str, data_type: str, 
                           batch_items: List[Tuple[str, Dict[str, Any]]]) -> Tuple[int, int]:
        """
        Process a batch of items concurrently.
        
        Args:
            hospital_id: Hospital identifier
            data_type: Type of data
            batch_items: List of (item_id, item_data) tuples
            
        Returns:
            Tuple of (successful_count, failed_count)
        """
        tasks = []
        for item_id, item_data in batch_items:
            task = self._cache_item_with_semantics(hospital_id, data_type, item_id, item_data)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        successful = sum(1 for result in results if result is True)
        failed = len(results) - successful
        
        return successful, failed
    
    async def sync_hospital_data_async(self, hospital_id: str, 
                                     firebase_data: Dict[str, Any]) -> FirebaseSyncResult:
        """
        Synchronize hospital data from Firebase to Redis with concurrent processing.
        
        Args:
            hospital_id: Hospital identifier
            firebase_data: Complete hospital data from Firebase
            
        Returns:
            Synchronization result
        """
        start_time = time.time()
        total_items = 0
        cached_items = 0
        failed_items = 0
        errors = []
        
        try:
            # Process different data types
            data_types = ["doctors", "tests", "staff", "hospital_info"]
            
            for data_type in data_types:
                if data_type not in firebase_data:
                    continue
                
                data_items = firebase_data[data_type]
                if not isinstance(data_items, (list, dict)):
                    continue
                
                # Convert to list of (id, data) tuples
                if isinstance(data_items, dict):
                    items = list(data_items.items())
                else:
                    items = [(str(i), item) for i, item in enumerate(data_items)]
                
                total_items += len(items)
                
                # Process in batches for optimal performance
                for i in range(0, len(items), self.batch_size):
                    batch = items[i:i + self.batch_size]
                    successful, failed = await self._process_batch(hospital_id, data_type, batch)
                    
                    cached_items += successful
                    failed_items += failed
                    
                    if failed > 0:
                        errors.append(f"Failed to cache {failed} {data_type} items")
                
                logger.info(f"Processed {len(items)} {data_type} items for hospital {hospital_id}")
            
            # Update statistics
            self._stats["sync_operations"] += 1
            self._stats["total_items_processed"] += total_items
            self._stats["total_items_cached"] += cached_items
            self._stats["total_errors"] += failed_items
            
            processing_time = (time.time() - start_time) * 1000
            self._stats["total_time"] += processing_time
            
            logger.info(f"Sync completed for hospital {hospital_id}: "
                       f"{cached_items}/{total_items} items cached in {processing_time:.2f}ms")
            
            return FirebaseSyncResult(
                hospital_id=hospital_id,
                total_items=total_items,
                cached_items=cached_items,
                failed_items=failed_items,
                processing_time_ms=processing_time,
                errors=errors
            )
            
        except Exception as e:
            logger.error(f"Error syncing hospital data for {hospital_id}: {e}")
            processing_time = (time.time() - start_time) * 1000
            
            return FirebaseSyncResult(
                hospital_id=hospital_id,
                total_items=total_items,
                cached_items=cached_items,
                failed_items=total_items - cached_items,
                processing_time_ms=processing_time,
                errors=[str(e)]
            )
    
    async def invalidate_hospital_cache_async(self, hospital_id: str) -> int:
        """
        Invalidate all cached data for a hospital.
        
        Args:
            hospital_id: Hospital identifier
            
        Returns:
            Number of keys deleted
        """
        try:
            patterns = [
                f"hospital:{hospital_id}:*",
                f"semantic:*:{hospital_id}:*",
                f"embedding:*:{hospital_id}:*"
            ]
            
            total_deleted = 0
            for pattern in patterns:
                keys = await self.redis_ops.keys_async(pattern)
                if keys:
                    deleted = await self.redis_ops.delete_async(*keys)
                    total_deleted += deleted
            
            logger.info(f"Invalidated {total_deleted} cache entries for hospital {hospital_id}")
            return total_deleted
            
        except Exception as e:
            logger.error(f"Error invalidating cache for hospital {hospital_id}: {e}")
            return 0
    
    def get_stats(self) -> Dict[str, Any]:
        """Get integration statistics."""
        stats = self._stats.copy()
        
        if stats["sync_operations"] > 0:
            stats["average_items_per_sync"] = stats["total_items_processed"] / stats["sync_operations"]
            stats["average_time_per_sync_ms"] = stats["total_time"] / stats["sync_operations"]
            stats["cache_success_rate"] = stats["total_items_cached"] / stats["total_items_processed"]
        else:
            stats["average_items_per_sync"] = 0
            stats["average_time_per_sync_ms"] = 0
            stats["cache_success_rate"] = 0
        
        return stats


# Global Firebase integration instance
_firebase_integration: Optional[FirebaseRedisIntegration] = None


def get_firebase_integration() -> FirebaseRedisIntegration:
    """Get global Firebase integration instance."""
    global _firebase_integration
    
    if _firebase_integration is None:
        _firebase_integration = FirebaseRedisIntegration()
        logger.info("Initialized Firebase Redis integration")
    
    return _firebase_integration
