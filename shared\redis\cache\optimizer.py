"""
Cache Optimizer for Shared Redis Implementation

Provides intelligent cache optimization strategies including:
- Automatic TTL adjustment based on usage patterns
- Cache warming and preloading
- Memory optimization and eviction strategies
- Performance tuning recommendations
"""

import logging
import threading
import time
from typing import Dict, Any, List, Optional, Tuple
from ..base_operations import RedisOperations
from ..config import get_redis_config
from .monitor import get_cache_monitor

logger = logging.getLogger(__name__)


class CacheOptimizer:
    """
    Intelligent cache optimization for shared Redis implementation.
    Analyzes usage patterns and automatically optimizes cache performance.
    """
    
    def __init__(self, redis_ops: Optional[RedisOperations] = None):
        """
        Initialize cache optimizer.
        
        Args:
            redis_ops: Optional Redis operations instance
        """
        self.redis_ops = redis_ops or RedisOperations()
        self.config = get_redis_config()
        self.monitor = get_cache_monitor()
        
        self._lock = threading.Lock()
        self._optimization_history: List[Dict[str, Any]] = []
        self._key_access_patterns: Dict[str, Dict[str, Any]] = {}
        
        # Optimization settings
        self.min_hit_rate_threshold = 0.7
        self.max_latency_threshold_ms = 100
        self.memory_usage_threshold_mb = 1000
        self.optimization_interval = 300  # 5 minutes
        
        # Auto-optimization state
        self._auto_optimization_active = False
        self._optimization_thread: Optional[threading.Thread] = None
    
    def analyze_key_patterns(self, hospital_id: str) -> Dict[str, Any]:
        """
        Analyze access patterns for hospital keys.
        
        Args:
            hospital_id: Hospital identifier
            
        Returns:
            Dict with pattern analysis
        """
        try:
            patterns = {
                "total_keys": 0,
                "semantic_keys": 0,
                "context_keys": 0,
                "availability_keys": 0,
                "hot_keys": [],
                "cold_keys": [],
                "recommendations": []
            }
            
            # Get all keys for hospital
            key_patterns = [
                f"semantic:*:{hospital_id}:*",
                f"call:context:*",
                f"availability:{hospital_id}:*",
                f"hospital:{hospital_id}:*"
            ]
            
            all_keys = []
            for pattern in key_patterns:
                keys = self.redis_ops.keys(pattern)
                all_keys.extend(keys)
            
            patterns["total_keys"] = len(all_keys)
            
            # Categorize keys
            for key in all_keys:
                if "semantic:" in key:
                    patterns["semantic_keys"] += 1
                elif "call:context:" in key:
                    patterns["context_keys"] += 1
                elif "availability:" in key:
                    patterns["availability_keys"] += 1
            
            # Analyze TTL patterns
            ttl_analysis = self._analyze_ttl_patterns(all_keys)
            patterns.update(ttl_analysis)
            
            # Generate recommendations
            if patterns["semantic_keys"] > 1000:
                patterns["recommendations"].append("Consider implementing semantic cache cleanup")
            
            if patterns["context_keys"] > 500:
                patterns["recommendations"].append("High number of call contexts - check TTL settings")
            
            return patterns
            
        except Exception as e:
            logger.error(f"Error analyzing key patterns: {e}")
            return {"error": str(e)}
    
    def _analyze_ttl_patterns(self, keys: List[str]) -> Dict[str, Any]:
        """Analyze TTL patterns for keys."""
        try:
            ttl_data = {
                "keys_with_ttl": 0,
                "keys_without_ttl": 0,
                "average_ttl": 0,
                "ttl_distribution": {"short": 0, "medium": 0, "long": 0}
            }
            
            ttl_values = []
            for key in keys[:100]:  # Sample first 100 keys
                ttl = self.redis_ops.ttl(key)
                if ttl > 0:
                    ttl_data["keys_with_ttl"] += 1
                    ttl_values.append(ttl)
                    
                    # Categorize TTL
                    if ttl < 300:  # 5 minutes
                        ttl_data["ttl_distribution"]["short"] += 1
                    elif ttl < 3600:  # 1 hour
                        ttl_data["ttl_distribution"]["medium"] += 1
                    else:
                        ttl_data["ttl_distribution"]["long"] += 1
                elif ttl == -1:
                    ttl_data["keys_without_ttl"] += 1
            
            if ttl_values:
                ttl_data["average_ttl"] = sum(ttl_values) / len(ttl_values)
            
            return ttl_data
            
        except Exception as e:
            logger.error(f"Error analyzing TTL patterns: {e}")
            return {}
    
    def optimize_ttl_settings(self, hospital_id: str) -> Dict[str, Any]:
        """
        Optimize TTL settings based on usage patterns.
        
        Args:
            hospital_id: Hospital identifier
            
        Returns:
            Dict with optimization results
        """
        try:
            optimization_result = {
                "optimizations_applied": 0,
                "keys_updated": 0,
                "recommendations": [],
                "before": {},
                "after": {}
            }
            
            # Analyze current patterns
            patterns = self.analyze_key_patterns(hospital_id)
            optimization_result["before"] = patterns
            
            # Get performance metrics
            app_metrics = self.monitor.get_application_metrics()
            
            # Optimize based on hit rates and latency
            for app_name, metrics in app_metrics.items():
                if app_name == "system":
                    continue
                
                if hasattr(metrics, 'hit_rate') and metrics.hit_rate < self.min_hit_rate_threshold:
                    # Increase TTL for better hit rates
                    self._adjust_ttl_for_category(hospital_id, "semantic", increase=True)
                    optimization_result["optimizations_applied"] += 1
                    optimization_result["recommendations"].append(f"Increased TTL for {app_name} semantic cache")
                
                if hasattr(metrics, 'average_latency_ms') and metrics.average_latency_ms > self.max_latency_threshold_ms:
                    # Optimize for latency
                    self._optimize_for_latency(hospital_id)
                    optimization_result["optimizations_applied"] += 1
                    optimization_result["recommendations"].append(f"Applied latency optimizations for {app_name}")
            
            # Re-analyze after optimization
            optimization_result["after"] = self.analyze_key_patterns(hospital_id)
            
            # Record optimization
            with self._lock:
                self._optimization_history.append({
                    "timestamp": time.time(),
                    "hospital_id": hospital_id,
                    "type": "ttl_optimization",
                    "result": optimization_result
                })
            
            logger.info(f"Applied {optimization_result['optimizations_applied']} TTL optimizations for hospital {hospital_id}")
            return optimization_result
            
        except Exception as e:
            logger.error(f"Error optimizing TTL settings: {e}")
            return {"error": str(e)}
    
    def _adjust_ttl_for_category(self, hospital_id: str, category: str, increase: bool = True):
        """Adjust TTL for a specific category of keys."""
        try:
            pattern = f"semantic:*:{hospital_id}:{category}:*"
            keys = self.redis_ops.keys(pattern)
            
            for key in keys[:50]:  # Limit to avoid performance impact
                current_ttl = self.redis_ops.ttl(key)
                if current_ttl > 0:
                    if increase:
                        new_ttl = min(current_ttl * 2, self.config.semantic_cache_ttl * 2)
                    else:
                        new_ttl = max(current_ttl // 2, 300)  # Minimum 5 minutes
                    
                    self.redis_ops.expire(key, int(new_ttl))
            
        except Exception as e:
            logger.error(f"Error adjusting TTL for category {category}: {e}")
    
    def _optimize_for_latency(self, hospital_id: str):
        """Apply latency optimizations."""
        try:
            # Remove old semantic cache entries to reduce memory pressure
            self._cleanup_old_semantic_cache(hospital_id)
            
            # Optimize call context TTL
            pattern = f"call:context:*"
            keys = self.redis_ops.keys(pattern)
            
            for key in keys[:20]:  # Sample keys
                current_ttl = self.redis_ops.ttl(key)
                if current_ttl > self.config.call_context_ttl:
                    # Reduce TTL for call contexts to free memory faster
                    self.redis_ops.expire(key, self.config.call_context_ttl)
            
        except Exception as e:
            logger.error(f"Error optimizing for latency: {e}")
    
    def _cleanup_old_semantic_cache(self, hospital_id: str):
        """Clean up old semantic cache entries."""
        try:
            patterns = [
                f"semantic:*:{hospital_id}:*",
                f"embedding:*:{hospital_id}:*"
            ]
            
            for pattern in patterns:
                keys = self.redis_ops.keys(pattern)
                
                # Remove keys with very low TTL (likely stale)
                for key in keys:
                    ttl = self.redis_ops.ttl(key)
                    if 0 < ttl < 60:  # Less than 1 minute remaining
                        self.redis_ops.delete(key)
            
        except Exception as e:
            logger.error(f"Error cleaning up semantic cache: {e}")
    
    def preload_hospital_cache(self, hospital_id: str, priority_data: Optional[Dict[str, Any]] = None) -> bool:
        """
        Preload cache with high-priority hospital data.
        
        Args:
            hospital_id: Hospital identifier
            priority_data: Optional priority data to cache
            
        Returns:
            bool: True if preloading successful
        """
        try:
            if not priority_data:
                # Load from voice agent data loaders if available
                try:
                    from voice_agent.data_loaders.cache_preloader import CachePreloader
                    preloader = CachePreloader()
                    return preloader.preload_hospital_data_sync(hospital_id)
                except ImportError:
                    logger.warning("Voice agent data loaders not available for preloading")
                    return False
            
            # Cache priority data
            cached_count = 0
            for key, value in priority_data.items():
                cache_key = f"preload:{hospital_id}:{key}"
                if self.redis_ops.set(cache_key, value, ttl=self.config.default_ttl):
                    cached_count += 1
            
            logger.info(f"Preloaded {cached_count} items for hospital {hospital_id}")
            return cached_count > 0
            
        except Exception as e:
            logger.error(f"Error preloading hospital cache: {e}")
            return False
    
    def get_optimization_recommendations(self, hospital_id: str) -> List[str]:
        """
        Get optimization recommendations for a hospital.
        
        Args:
            hospital_id: Hospital identifier
            
        Returns:
            List of optimization recommendations
        """
        try:
            recommendations = []
            
            # Analyze patterns
            patterns = self.analyze_key_patterns(hospital_id)
            
            # Memory recommendations
            if patterns.get("total_keys", 0) > 5000:
                recommendations.append("Consider implementing key expiration policies")
            
            # Performance recommendations
            app_metrics = self.monitor.get_application_metrics()
            for app_name, metrics in app_metrics.items():
                if app_name == "system":
                    continue
                
                if hasattr(metrics, 'hit_rate') and metrics.hit_rate < 0.5:
                    recommendations.append(f"Improve cache strategy for {app_name} - low hit rate")
                
                if hasattr(metrics, 'average_latency_ms') and metrics.average_latency_ms > 200:
                    recommendations.append(f"Optimize {app_name} cache operations - high latency")
            
            # Semantic cache recommendations
            if patterns.get("semantic_keys", 0) > 2000:
                recommendations.append("Consider semantic cache cleanup or size limits")
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error getting optimization recommendations: {e}")
            return [f"Error analyzing optimization opportunities: {e}"]
    
    def start_auto_optimization(self, interval: int = 300):
        """
        Start automatic optimization.
        
        Args:
            interval: Optimization interval in seconds
        """
        if self._auto_optimization_active:
            logger.warning("Auto-optimization already active")
            return
        
        self.optimization_interval = interval
        self._auto_optimization_active = True
        
        def optimization_loop():
            while self._auto_optimization_active:
                try:
                    # Get all hospitals from Redis keys
                    hospital_patterns = [
                        "semantic:*:*:*",
                        "hospital:*:*"
                    ]
                    
                    hospital_ids = set()
                    for pattern in hospital_patterns:
                        keys = self.redis_ops.keys(pattern)
                        for key in keys:
                            parts = key.split(":")
                            if len(parts) >= 3:
                                hospital_ids.add(parts[2])
                    
                    # Optimize each hospital
                    for hospital_id in list(hospital_ids)[:5]:  # Limit to 5 hospitals per cycle
                        self.optimize_ttl_settings(hospital_id)
                    
                    time.sleep(self.optimization_interval)
                    
                except Exception as e:
                    logger.error(f"Error in auto-optimization loop: {e}")
                    time.sleep(self.optimization_interval)
        
        self._optimization_thread = threading.Thread(target=optimization_loop, daemon=True)
        self._optimization_thread.start()
        
        logger.info(f"Started auto-optimization with {interval}s interval")
    
    def stop_auto_optimization(self):
        """Stop automatic optimization."""
        self._auto_optimization_active = False
        if self._optimization_thread:
            self._optimization_thread.join(timeout=5)
        logger.info("Stopped auto-optimization")
    
    def get_optimization_history(self, hours: int = 24) -> List[Dict[str, Any]]:
        """
        Get optimization history.
        
        Args:
            hours: Number of hours of history to return
            
        Returns:
            List of optimization records
        """
        cutoff_time = time.time() - (hours * 3600)
        
        with self._lock:
            return [record for record in self._optimization_history if record["timestamp"] >= cutoff_time]


# Global cache optimizer instance
_cache_optimizer: Optional[CacheOptimizer] = None
_optimizer_lock = threading.Lock()


def get_cache_optimizer() -> CacheOptimizer:
    """
    Get global cache optimizer instance.
    
    Returns:
        CacheOptimizer instance
    """
    global _cache_optimizer
    
    with _optimizer_lock:
        if _cache_optimizer is None:
            _cache_optimizer = CacheOptimizer()
            logger.info("Initialized global cache optimizer")
    
    return _cache_optimizer
