import { logoutUser } from '../../../lib/firebase';
import { removeTokenCookie } from '../../../lib/auth';

export default async function handler(req, res) {
  // Only allow POST method
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  try {
    // Logout from Firebase
    await logoutUser();
    
    // Remove JWT cookie
    removeTokenCookie(res);
    
    return res.status(200).json({
      success: true,
      message: 'Logged out successfully'
    });
  } catch (error) {
    console.error('Logout error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
}