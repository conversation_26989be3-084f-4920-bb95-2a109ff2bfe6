/**
 * Node.js Adapter for Shared Redis Implementation
 * 
 * Provides a high-level Node.js interface for whatsapp_agent to use the shared Redis
 * implementation with enhanced features including multilingual semantic caching.
 */

import { createClient } from 'redis';
import { logger } from '../../../whatsapp_agent/lib/logger.js';

class NodeJSRedisAdapter {
  constructor() {
    this.client = null;
    this.isConnected = false;
    this.config = this._loadConfig();
    this.stats = {
      operations: 0,
      cache_hits: 0,
      cache_misses: 0,
      errors: 0,
      total_time: 0
    };
  }

  /**
   * Load Redis configuration from environment variables
   */
  _loadConfig() {
    return {
      url: process.env.REDIS_URL || 'redis://localhost:6379/0',
      max_connections: parseInt(process.env.REDIS_MAX_CONNECTIONS || '50'),
      socket_timeout: parseInt(process.env.REDIS_SOCKET_TIMEOUT || '5000'),
      default_ttl: parseInt(process.env.REDIS_DEFAULT_TTL || '3600'),
      semantic_cache_ttl: parseInt(process.env.REDIS_SEMANTIC_CACHE_TTL || '86400'),
      similarity_threshold: parseFloat(process.env.REDIS_SEMANTIC_SIMILARITY_THRESHOLD || '0.8'),
      whatsapp_agent_enabled: process.env.REDIS_WHATSAPP_AGENT_ENABLED !== 'false'
    };
  }

  /**
   * Initialize Redis connection
   */
  async initialize() {
    try {
      if (this.client && this.isConnected) {
        logger.info('[REDIS_ADAPTER] Already connected');
        return true;
      }

      this.client = createClient({ 
        url: this.config.url,
        socket: {
          connectTimeout: this.config.socket_timeout,
          commandTimeout: this.config.socket_timeout
        }
      });

      // Set up event handlers
      this.client.on('error', (err) => {
        logger.error('[REDIS_ADAPTER] Redis error:', err);
        this.isConnected = false;
        this.stats.errors++;
      });

      this.client.on('connect', () => {
        logger.info('[REDIS_ADAPTER] Redis connected');
        this.isConnected = true;
      });

      this.client.on('reconnecting', () => {
        logger.info('[REDIS_ADAPTER] Redis reconnecting');
      });

      this.client.on('ready', () => {
        logger.info('[REDIS_ADAPTER] Redis ready');
        this.isConnected = true;
      });

      await this.client.connect();
      
      // Test connection
      await this.client.ping();
      
      logger.info('[REDIS_ADAPTER] Initialized successfully');
      return true;

    } catch (error) {
      logger.error('[REDIS_ADAPTER] Initialization error:', error);
      this.isConnected = false;
      return false;
    }
  }

  /**
   * Record operation statistics
   */
  _recordOperation(operationTime, hit = null, error = false) {
    this.stats.operations++;
    this.stats.total_time += operationTime;
    
    if (error) {
      this.stats.errors++;
    } else if (hit === true) {
      this.stats.cache_hits++;
    } else if (hit === false) {
      this.stats.cache_misses++;
    }
  }

  /**
   * Get optimal TTL for a key based on its type
   */
  _getOptimalTTL(key) {
    if (key.includes('semantic:multi:')) {
      return this.config.semantic_cache_ttl;
    } else if (key.includes('availability:')) {
      return 1800; // 30 minutes for availability
    } else if (key.includes('doctor:arrival:')) {
      return 3600; // 1 hour for doctor arrival status
    }
    return this.config.default_ttl;
  }

  /**
   * Set a key-value pair
   */
  async set(key, value, ttl = null) {
    const startTime = Date.now();
    
    try {
      if (!this.isConnected) {
        await this.initialize();
      }

      const serializedValue = typeof value === 'string' ? value : JSON.stringify(value);
      const expiry = ttl || this._getOptimalTTL(key);
      
      await this.client.setEx(key, expiry, serializedValue);
      
      this._recordOperation(Date.now() - startTime);
      return true;

    } catch (error) {
      logger.error(`[REDIS_ADAPTER] Set error for key ${key}:`, error);
      this._recordOperation(Date.now() - startTime, null, true);
      return false;
    }
  }

  /**
   * Get value from Redis
   */
  async get(key, asJson = true) {
    const startTime = Date.now();
    
    try {
      if (!this.isConnected) {
        await this.initialize();
      }

      const value = await this.client.get(key);
      const hit = value !== null;
      
      this._recordOperation(Date.now() - startTime, hit);
      
      if (!value) {
        return null;
      }

      if (!asJson) {
        return value;
      }

      try {
        return JSON.parse(value);
      } catch {
        return value;
      }

    } catch (error) {
      logger.error(`[REDIS_ADAPTER] Get error for key ${key}:`, error);
      this._recordOperation(Date.now() - startTime, false, true);
      return null;
    }
  }

  /**
   * Delete keys from Redis
   */
  async delete(...keys) {
    const startTime = Date.now();
    
    try {
      if (!this.isConnected) {
        await this.initialize();
      }

      const result = await this.client.del(keys);
      this._recordOperation(Date.now() - startTime);
      return result;

    } catch (error) {
      logger.error(`[REDIS_ADAPTER] Delete error for keys ${keys}:`, error);
      this._recordOperation(Date.now() - startTime, null, true);
      return 0;
    }
  }

  /**
   * Check if keys exist
   */
  async exists(...keys) {
    const startTime = Date.now();
    
    try {
      if (!this.isConnected) {
        await this.initialize();
      }

      const result = await this.client.exists(keys);
      this._recordOperation(Date.now() - startTime);
      return result;

    } catch (error) {
      logger.error(`[REDIS_ADAPTER] Exists error for keys ${keys}:`, error);
      this._recordOperation(Date.now() - startTime, null, true);
      return 0;
    }
  }

  /**
   * Get keys matching pattern
   */
  async keys(pattern = '*') {
    const startTime = Date.now();
    
    try {
      if (!this.isConnected) {
        await this.initialize();
      }

      const result = await this.client.keys(pattern);
      this._recordOperation(Date.now() - startTime);
      return result;

    } catch (error) {
      logger.error(`[REDIS_ADAPTER] Keys error for pattern ${pattern}:`, error);
      this._recordOperation(Date.now() - startTime, null, true);
      return [];
    }
  }

  /**
   * Cache doctor availability status for WhatsApp queries
   */
  async cacheDoctorAvailability(hospitalId, doctorId, status, estimatedTime = null) {
    try {
      // Create availability queries in multiple languages/scripts
      const queries = [
        `when will doctor ${doctorId} arrive`,
        `doctor ${doctorId} availability`,
        `is doctor ${doctorId} available`,
        `doctor ${doctorId} ka time kya hai`,  // Hindi in English script
        `doctor ${doctorId} kab aayenge`,     // Hindi in English script
        `${doctorId} doctor kab milenge`,     // Hindi in English script
      ];

      // Create response based on status
      let response;
      if (status === 'arrived') {
        response = `Dr. ${doctorId} has arrived at the hospital.`;
      } else if (status === 'delayed' && estimatedTime) {
        response = `Dr. ${doctorId} is delayed and will arrive in ${estimatedTime}.`;
      } else if (estimatedTime) {
        response = `Dr. ${doctorId} will arrive in ${estimatedTime}.`;
      } else {
        response = `Dr. ${doctorId} is currently unavailable.`;
      }

      // Cache all query variations
      let successCount = 0;
      for (const query of queries) {
        const cacheKey = `semantic:multi:${hospitalId}:doctor_availability:${this._hashString(query)}`;
        const cacheData = {
          query,
          response,
          hospital_id: hospitalId,
          category: 'doctor_availability',
          doctor_id: doctorId,
          status,
          estimated_time: estimatedTime,
          timestamp: Date.now()
        };

        if (await this.set(cacheKey, cacheData, this.config.semantic_cache_ttl)) {
          successCount++;
        }
      }

      // Also cache direct availability status
      const statusKey = `availability:${hospitalId}:doctor:${doctorId}:status`;
      await this.set(statusKey, { status, estimated_time: estimatedTime }, 1800); // 30 minutes

      logger.info(`[REDIS_ADAPTER] Cached doctor availability: ${doctorId} - ${status} (${successCount} variations)`);
      return successCount > 0;

    } catch (error) {
      logger.error('[REDIS_ADAPTER] Error caching doctor availability:', error);
      return false;
    }
  }

  /**
   * Get doctor availability status
   */
  async getDoctorAvailability(hospitalId, doctorId) {
    try {
      const statusKey = `availability:${hospitalId}:doctor:${doctorId}:status`;
      const availability = await this.get(statusKey);
      
      if (availability) {
        return {
          status: availability.status,
          estimated_time: availability.estimated_time,
          cached: true
        };
      }

      return null;

    } catch (error) {
      logger.error('[REDIS_ADAPTER] Error getting doctor availability:', error);
      return null;
    }
  }

  /**
   * Search for semantically similar responses (simplified for Node.js)
   */
  async searchSimilarResponses(query, hospitalId, category = 'general', limit = 5) {
    try {
      // For Node.js, we'll use exact and partial matching since we don't have
      // the embedding models available. In production, this could call a
      // Python service for semantic matching.
      
      const pattern = `semantic:multi:${hospitalId}:${category}:*`;
      const keys = await this.keys(pattern);
      
      const results = [];
      const queryLower = query.toLowerCase();
      
      for (const key of keys.slice(0, 50)) { // Limit search for performance
        const data = await this.get(key);
        if (data && data.query) {
          const cachedQueryLower = data.query.toLowerCase();
          
          // Simple similarity scoring based on word overlap
          const similarity = this._calculateSimpleSimilarity(queryLower, cachedQueryLower);
          
          if (similarity >= this.config.similarity_threshold) {
            results.push({
              ...data,
              similarity
            });
          }
        }
      }

      // Sort by similarity and return top results
      results.sort((a, b) => b.similarity - a.similarity);
      return results.slice(0, limit);

    } catch (error) {
      logger.error('[REDIS_ADAPTER] Error searching similar responses:', error);
      return [];
    }
  }

  /**
   * Simple similarity calculation based on word overlap
   */
  _calculateSimpleSimilarity(query1, query2) {
    const words1 = new Set(query1.split(/\s+/));
    const words2 = new Set(query2.split(/\s+/));
    
    const intersection = new Set([...words1].filter(x => words2.has(x)));
    const union = new Set([...words1, ...words2]);
    
    return intersection.size / union.size;
  }

  /**
   * Simple string hashing function
   */
  _hashString(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString();
  }

  /**
   * Clear cache entries matching pattern
   */
  async clearCache(pattern) {
    try {
      const keys = await this.keys(pattern);
      if (keys.length > 0) {
        return await this.delete(...keys);
      }
      return 0;
    } catch (error) {
      logger.error('[REDIS_ADAPTER] Error clearing cache:', error);
      return 0;
    }
  }

  /**
   * Get adapter statistics
   */
  getStats() {
    const stats = { ...this.stats };
    
    if (stats.operations > 0) {
      stats.average_time_ms = stats.total_time / stats.operations;
      stats.hit_rate = stats.cache_hits / (stats.cache_hits + stats.cache_misses) || 0;
      stats.error_rate = stats.errors / stats.operations;
    } else {
      stats.average_time_ms = 0;
      stats.hit_rate = 0;
      stats.error_rate = 0;
    }

    stats.is_connected = this.isConnected;
    stats.config = this.config;
    
    return stats;
  }

  /**
   * Test connection health
   */
  async testConnection() {
    try {
      if (!this.isConnected) {
        await this.initialize();
      }
      
      await this.client.ping();
      return true;
    } catch (error) {
      logger.error('[REDIS_ADAPTER] Connection test failed:', error);
      return false;
    }
  }

  /**
   * Close connection
   */
  async close() {
    try {
      if (this.client) {
        await this.client.quit();
        this.isConnected = false;
        logger.info('[REDIS_ADAPTER] Connection closed');
      }
    } catch (error) {
      logger.error('[REDIS_ADAPTER] Error closing connection:', error);
    }
  }
}

// Global adapter instance
let nodeJSAdapter = null;

/**
 * Get global Node.js Redis adapter instance
 */
export function getNodeJSAdapter() {
  if (!nodeJSAdapter) {
    nodeJSAdapter = new NodeJSRedisAdapter();
    logger.info('[REDIS_ADAPTER] Initialized global Node.js Redis adapter');
  }
  return nodeJSAdapter;
}

export { NodeJSRedisAdapter };
export default { NodeJSRedisAdapter, getNodeJSAdapter };
