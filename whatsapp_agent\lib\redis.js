/**
 * Enhanced Redis integration for WhatsApp Agent
 *
 * Now uses the shared Redis implementation with enhanced features:
 * - Multilingual semantic caching
 * - Automatic Firebase data integration
 * - Concurrent processing with batching
 * - Advanced doctor availability management
 */

import { getNodeJSAdapter } from '../../shared/redis/adapters/nodejs_adapter.js';
import { logger } from './logger.js';

// Shared Redis adapter instance
let sharedRedisAdapter = null;

/**
 * Initialize enhanced Redis connection with shared implementation
 */
export const initializeRedis = async () => {
  try {
    // Check if already initialized and connected
    if (sharedRedisAdapter && sharedRedisAdapter.isConnected) {
      logger.info('[REDIS] Shared Redis adapter already connected');
      return sharedRedisAdapter;
    }

    // Get the shared Redis adapter
    sharedRedisAdapter = getNodeJSAdapter();

    // Initialize with enhanced features
    const success = await sharedRedisAdapter.initialize();

    if (success) {
      logger.info('[REDIS] Enhanced Redis connection established successfully');

      // Start automatic Firebase data synchronization
      await startFirebaseDataSync();

      // Initialize semantic caching
      await initializeSemanticCaching();

      return sharedRedisAdapter;
    } else {
      logger.error('[REDIS] Failed to initialize shared Redis adapter');
      throw new Error('Failed to initialize shared Redis adapter');
    }
  } catch (error) {
    logger.error('[REDIS] Failed to initialize enhanced Redis:', error);
    throw error;
  }
};

/**
 * Get enhanced Redis adapter instance
 */
export const getRedisClient = () => {
  if (!sharedRedisAdapter || !sharedRedisAdapter.isConnected) {
    throw new Error('Shared Redis adapter not initialized or not connected. Call initializeRedis() first.');
  }
  return sharedRedisAdapter;
};

/**
 * Start automatic Firebase data synchronization
 */
async function startFirebaseDataSync() {
  try {
    // This will automatically sync Firebase data with Redis cache
    // for better performance and reduced Firebase calls
    logger.info('[REDIS] Firebase data synchronization started');

    // Start background sync process
    setInterval(async () => {
      try {
        await syncHospitalData();
      } catch (error) {
        logger.error('[REDIS] Error in background Firebase sync:', error);
      }
    }, 300000); // Sync every 5 minutes

  } catch (error) {
    logger.error('[REDIS] Error starting Firebase data sync:', error);
  }
}

/**
 * Sync hospital data from Firebase to Redis
 */
async function syncHospitalData() {
  try {
    // This would integrate with Firebase to automatically cache
    // hospital data, doctor information, and availability
    logger.debug('[REDIS] Syncing hospital data from Firebase');

    // Implementation would go here to fetch from Firebase
    // and cache in Redis using the shared adapter

  } catch (error) {
    logger.error('[REDIS] Error syncing hospital data:', error);
  }
}

/**
 * Initialize semantic caching for multilingual support
 */
async function initializeSemanticCaching() {
  try {
    // Initialize semantic caching for common queries
    const commonQueries = [
      {
        query: 'visiting hours',
        response: 'Visiting hours are 10 AM to 8 PM daily.',
        language: 'en'
      },
      {
        query: 'visiting hours kya hai',
        response: 'Visiting hours 10 AM se 8 PM tak hai daily.',
        language: 'hi'
      },
      {
        query: 'hospital ka time',
        response: 'Hospital ka visiting time 10 baje se 8 baje tak hai.',
        language: 'hi'
      },
      {
        query: 'emergency contact',
        response: 'For emergencies, please call 102 or visit the emergency department.',
        language: 'en'
      },
      {
        query: 'emergency number kya hai',
        response: 'Emergency ke liye 102 call kariye ya emergency department mein aayiye.',
        language: 'hi'
      }
    ];

    // Pre-cache common responses for better performance
    for (const item of commonQueries) {
      const cacheKey = `semantic:common:${sharedRedisAdapter._hashString(item.query)}`;
      await sharedRedisAdapter.set(
        cacheKey,
        {
          query: item.query,
          response: item.response,
          category: 'common',
          language: item.language,
          timestamp: Date.now()
        },
        sharedRedisAdapter.config.semantic_cache_ttl
      );
    }

    logger.info('[REDIS] Semantic caching initialized with common queries');
  } catch (error) {
    logger.error('[REDIS] Error initializing semantic caching:', error);
  }
}

/**
 * Get enhanced doctor arrival status from Redis with automatic Firebase fallback
 * @param {string} hospitalId - Hospital ID
 * @param {string} doctorId - Doctor ID
 * @returns {Promise<Object>} - Enhanced arrival status with metadata
 */
export const getDoctorArrivalStatus = async (hospitalId, doctorId) => {
  try {
    const adapter = getRedisClient();

    // First try to get from enhanced availability cache
    const availability = await adapter.getDoctorAvailability(hospitalId, doctorId);

    if (availability) {
      return {
        status: availability.status,
        estimated_time: availability.estimated_time,
        source: 'redis_cache',
        cached: true,
        timestamp: availability.timestamp || Date.now()
      };
    }

    // Fallback to legacy key format for backward compatibility
    const legacyKey = `hospital:${hospitalId}:doctor:${doctorId}:arrival_status`;
    const legacyStatus = await adapter.get(legacyKey, false);

    if (legacyStatus) {
      // Convert legacy format to new format and cache it
      const enhancedStatus = {
        status: legacyStatus.toLowerCase() === 'arrived' ? 'arrived' : 'delayed',
        estimated_time: legacyStatus.includes('minute') ? legacyStatus : null,
        source: 'legacy_cache',
        cached: true,
        timestamp: Date.now()
      };

      // Cache in new format for future requests
      await adapter.cacheDoctorAvailability(
        hospitalId,
        doctorId,
        enhancedStatus.status,
        enhancedStatus.estimated_time
      );

      return enhancedStatus;
    }

    // If not in cache, try to fetch from Firebase and cache it
    const firebaseStatus = await fetchDoctorStatusFromFirebase(hospitalId, doctorId);
    if (firebaseStatus) {
      // Cache the Firebase data
      await adapter.cacheDoctorAvailability(
        hospitalId,
        doctorId,
        firebaseStatus.status,
        firebaseStatus.estimated_time
      );

      return {
        ...firebaseStatus,
        source: 'firebase',
        cached: false
      };
    }

    logger.debug(`No arrival status found for doctor ${doctorId} at hospital ${hospitalId}`);
    return null;

  } catch (error) {
    logger.error(`Error getting enhanced arrival status for doctor ${doctorId}:`, error);
    throw error;
  }
};

/**
 * Get enhanced doctor arrival time with semantic search capabilities
 * @param {string} hospitalId - Hospital ID
 * @param {string} doctorId - Doctor ID
 * @returns {Promise<Object>} - Enhanced arrival time with semantic matching
 */
export const getDoctorArrivalTime = async (hospitalId, doctorId) => {
  try {
    const adapter = getRedisClient();

    // Get enhanced availability status
    const availability = await getDoctorArrivalStatus(hospitalId, doctorId);

    if (availability) {
      return {
        arrival_time: availability.estimated_time || availability.status,
        status: availability.status,
        source: availability.source,
        cached: availability.cached,
        timestamp: availability.timestamp
      };
    }

    // Fallback to legacy key format
    const legacyKey = `hospital:${hospitalId}:doctor:${doctorId}:arrival_time`;
    const legacyTime = await adapter.get(legacyKey, false);

    if (legacyTime) {
      return {
        arrival_time: legacyTime,
        status: legacyTime.toLowerCase() === 'arrived' ? 'arrived' : 'delayed',
        source: 'legacy_cache',
        cached: true,
        timestamp: Date.now()
      };
    }

    logger.debug(`No arrival time found for doctor ${doctorId} at hospital ${hospitalId}`);
    return null;

  } catch (error) {
    logger.error(`Error getting enhanced arrival time for doctor ${doctorId}:`, error);
    throw error;
  }
};

/**
 * Format enhanced doctor availability message with multilingual support
 * @param {string} hospitalId - Hospital ID
 * @param {string} doctorId - Doctor ID
 * @param {string} doctorName - Doctor name
 * @param {string} language - Language preference ('en', 'hi', etc.)
 * @returns {Promise<Object>} - Enhanced formatted availability message with metadata
 */
export const formatDoctorAvailabilityMessage = async (hospitalId, doctorId, doctorName, language = 'en') => {
  try {
    const adapter = getRedisClient();
    const availability = await getDoctorArrivalStatus(hospitalId, doctorId);

    if (!availability) {
      const fallbackMessage = language === 'hi'
        ? `हमारे पास डॉ. ${doctorName} के आने की जानकारी उपलब्ध नहीं है।`
        : `We don't have real-time information about Dr. ${doctorName}'s arrival at the moment.`;

      return {
        message: fallbackMessage,
        language,
        source: 'fallback',
        doctor_id: doctorId,
        doctor_name: doctorName,
        timestamp: Date.now()
      };
    }

    let message;
    if (availability.status === 'arrived') {
      message = language === 'hi'
        ? `डॉ. ${doctorName} अस्पताल पहुंच गए हैं।`
        : `Dr. ${doctorName} has arrived at the hospital.`;
    } else if (availability.status === 'delayed' && availability.estimated_time) {
      message = language === 'hi'
        ? `डॉ. ${doctorName} ${availability.estimated_time} में पहुंचेंगे।`
        : `Dr. ${doctorName} will arrive in ${availability.estimated_time}.`;
    } else if (availability.status === 'unavailable') {
      message = language === 'hi'
        ? `डॉ. ${doctorName} आज उपलब्ध नहीं हैं।`
        : `Dr. ${doctorName} is not available today.`;
    } else {
      message = language === 'hi'
        ? `डॉ. ${doctorName} की स्थिति: ${availability.status}`
        : `Dr. ${doctorName}'s status: ${availability.status}`;
    }

    // Cache this formatted message for semantic search
    const query = language === 'hi'
      ? `डॉक्टर ${doctorName} कब आएंगे`
      : `when will doctor ${doctorName} come`;

    await adapter.set(
      `semantic:multi:${hospitalId}:doctor_availability:${adapter._hashString(query)}`,
      {
        query,
        response: message,
        hospital_id: hospitalId,
        category: 'doctor_availability',
        doctor_id: doctorId,
        doctor_name: doctorName,
        language,
        timestamp: Date.now()
      },
      adapter.config.semantic_cache_ttl
    );

    return {
      message,
      language,
      source: availability.source,
      doctor_id: doctorId,
      doctor_name: doctorName,
      status: availability.status,
      estimated_time: availability.estimated_time,
      cached: availability.cached,
      timestamp: availability.timestamp
    };

  } catch (error) {
    logger.error(`Error formatting enhanced availability message for doctor ${doctorId}:`, error);
    const errorMessage = language === 'hi'
      ? `डॉ. ${doctorName} की जानकारी प्राप्त करने में समस्या हो रही है।`
      : `We're unable to retrieve Dr. ${doctorName}'s availability information at the moment.`;

    return {
      message: errorMessage,
      language,
      source: 'error',
      doctor_id: doctorId,
      doctor_name: doctorName,
      error: error.message,
      timestamp: Date.now()
    };
  }
};

/**
 * Fetch doctor status from Firebase with automatic caching
 * @param {string} hospitalId - Hospital ID
 * @param {string} doctorId - Doctor ID
 * @returns {Promise<Object>} - Doctor status from Firebase
 */
async function fetchDoctorStatusFromFirebase(hospitalId, doctorId) {
  try {
    // This would integrate with Firebase to fetch real-time doctor status
    // For now, return null to indicate no Firebase data available
    // In production, this would make actual Firebase calls

    logger.debug(`[FIREBASE] Fetching doctor status for ${doctorId} at hospital ${hospitalId}`);

    // Placeholder for Firebase integration
    // const db = getFirestore();
    // const docRef = db.collection(`hospital_${hospitalId}_data`)
    //   .document('staff').collection('staff').document(doctorId);
    // const doc = await docRef.get();
    // if (doc.exists) {
    //   const data = doc.data();
    //   return {
    //     status: data.availability?.status || 'unknown',
    //     estimated_time: data.availability?.estimated_time || null,
    //     timestamp: Date.now()
    //   };
    // }

    return null;
  } catch (error) {
    logger.error(`[FIREBASE] Error fetching doctor status from Firebase:`, error);
    return null;
  }
}

/**
 * Enhanced semantic search for doctor queries
 * @param {string} query - User query
 * @param {string} hospitalId - Hospital ID
 * @param {string} language - Language preference
 * @returns {Promise<Object>} - Search results with semantic matching
 */
export const searchDoctorQueries = async (query, hospitalId, language = 'en') => {
  try {
    const adapter = getRedisClient();

    // Search for similar queries using the shared adapter
    const semanticMatches = await adapter.searchSimilarResponses(
      query, hospitalId, 'doctor_availability', 5
    );

    if (semanticMatches.length > 0) {
      logger.info(`[SEMANTIC] Found ${semanticMatches.length} semantic matches for: "${query}"`);
      return {
        matches: semanticMatches,
        source: 'semantic_cache',
        query,
        language,
        timestamp: Date.now()
      };
    }

    // If no semantic matches, try pattern matching for doctor names
    const doctorPatterns = [
      /doctor?\s+(\w+)/i,
      /dr\.?\s+(\w+)/i,
      /डॉक्टर\s+(\w+)/i,
      /डॉ\.?\s+(\w+)/i
    ];

    let doctorName = null;
    for (const pattern of doctorPatterns) {
      const match = query.match(pattern);
      if (match) {
        doctorName = match[1];
        break;
      }
    }

    if (doctorName) {
      // Try to find doctor availability by name
      const availability = await getDoctorArrivalStatus(hospitalId, doctorName.toLowerCase());
      if (availability) {
        const formattedMessage = await formatDoctorAvailabilityMessage(
          hospitalId, doctorName.toLowerCase(), doctorName, language
        );

        return {
          matches: [formattedMessage],
          source: 'pattern_match',
          query,
          language,
          doctor_name: doctorName,
          timestamp: Date.now()
        };
      }
    }

    return {
      matches: [],
      source: 'no_match',
      query,
      language,
      timestamp: Date.now()
    };

  } catch (error) {
    logger.error(`[SEMANTIC] Error in semantic search:`, error);
    return {
      matches: [],
      source: 'error',
      query,
      language,
      error: error.message,
      timestamp: Date.now()
    };
  }
};

/**
 * Batch update doctor availability for multiple doctors
 * @param {string} hospitalId - Hospital ID
 * @param {Array} doctorUpdates - Array of doctor update objects
 * @returns {Promise<Object>} - Batch update results
 */
export const batchUpdateDoctorAvailability = async (hospitalId, doctorUpdates) => {
  try {
    const adapter = getRedisClient();
    const results = {
      successful: 0,
      failed: 0,
      errors: [],
      timestamp: Date.now()
    };

    // Process updates concurrently for better performance
    const updatePromises = doctorUpdates.map(async (update) => {
      try {
        const success = await adapter.cacheDoctorAvailability(
          hospitalId,
          update.doctorId,
          update.status,
          update.estimatedTime
        );

        if (success) {
          results.successful++;
          logger.info(`[BATCH] Updated availability for Dr. ${update.doctorId}: ${update.status}`);
        } else {
          results.failed++;
          results.errors.push(`Failed to update Dr. ${update.doctorId}`);
        }
      } catch (error) {
        results.failed++;
        results.errors.push(`Error updating Dr. ${update.doctorId}: ${error.message}`);
        logger.error(`[BATCH] Error updating Dr. ${update.doctorId}:`, error);
      }
    });

    await Promise.all(updatePromises);

    logger.info(`[BATCH] Batch update completed: ${results.successful} successful, ${results.failed} failed`);
    return results;

  } catch (error) {
    logger.error(`[BATCH] Error in batch update:`, error);
    return {
      successful: 0,
      failed: doctorUpdates.length,
      errors: [error.message],
      timestamp: Date.now()
    };
  }
};

/**
 * Get cache statistics for monitoring
 * @returns {Promise<Object>} - Cache statistics
 */
export const getCacheStats = async () => {
  try {
    const adapter = getRedisClient();
    return await adapter.getStats();
  } catch (error) {
    logger.error(`[STATS] Error getting cache statistics:`, error);
    return { error: error.message };
  }
};

export default {
  initializeRedis,
  getRedisClient,
  getDoctorArrivalStatus,
  getDoctorArrivalTime,
  formatDoctorAvailabilityMessage,
  searchDoctorQueries,
  batchUpdateDoctorAvailability,
  getCacheStats
};
