/**
 * DateTime Utilities for Staff Portal
 * 
 * Provides robust datetime parsing and formatting utilities
 * to handle various datetime formats consistently across the system.
 */

/**
 * Common datetime formats used in the system
 */
const DATETIME_FORMATS = [
  // ISO formats
  /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/,           // 2023-12-25T15:30:45.123Z
  /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z$/,                  // 2023-12-25T15:30:45Z
  /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}[+-]\d{2}:\d{2}$/, // 2023-12-25T15:30:45.123+05:30
  /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}[+-]\d{2}:\d{2}$/,    // 2023-12-25T15:30:45+05:30
  /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}$/,            // 2023-12-25T15:30:45.123
  /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}$/,                   // 2023-12-25T15:30:45
  
  // Space separated formats
  /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3}$/,            // 2023-12-25 15:30:45.123
  /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/,                   // 2023-12-25 15:30:45
  /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$/,                         // 2023-12-25 15:30
];

/**
 * Time formats for parsing time strings
 */
const TIME_FORMATS = [
  /^\d{2}:\d{2}:\d{2}\.\d{3}$/,    // 15:30:45.123
  /^\d{2}:\d{2}:\d{2}$/,           // 15:30:45
  /^\d{1,2}:\d{2}$/,               // 15:30 or 3:30
  /^\d{1,2}:\d{2} (AM|PM)$/i,      // 3:30 PM
  /^\d{1,2}:\d{2}:\d{2} (AM|PM)$/i, // 3:30:45 PM
];

/**
 * Date formats
 * Note: MM/DD/YYYY (US) and DD/MM/YYYY (European) share the same regex pattern and cannot be reliably distinguished by regex alone.
 * Disambiguation requires additional context or explicit format specification.
 */
const DATE_FORMATS = [
  /^\d{4}-\d{2}-\d{2}$/,           // 2023-12-25 (ISO)
  /^\d{2}\/\d{2}\/\d{4}$/,      // 12/25/2023 (US) or 25/12/2023 (European) - ambiguous
  /^\d{4}\/\d{2}\/\d{2}$/,      // 2023/12/25
];

/**
 * Parse datetime string with multiple format attempts
 * @param {string|Date|null} datetimeInput - Input to parse
 * @returns {Date|null} Parsed Date object or null if parsing fails
 */
export function parseDateTimeRobust(datetimeInput) {
  if (!datetimeInput) {
    return null;
  }

  // If already a Date object, return as-is
  if (datetimeInput instanceof Date) {
    return isNaN(datetimeInput.getTime()) ? null : datetimeInput;
  }

  // Must be a string at this point
  if (typeof datetimeInput !== 'string') {
    console.error(`Invalid datetime input type: ${typeof datetimeInput}`);
    return null;
  }

  const datetimeStr = datetimeInput.trim();
  if (!datetimeStr) {
    return null;
  }

  // Try parsing with Date constructor (handles most ISO formats)
  try {
    const date = new Date(datetimeStr);
    if (!isNaN(date.getTime())) {
      return date;
    }
  } catch (error) {
    // Continue to manual parsing
  }

  // Try manual parsing for specific formats
  for (const format of DATETIME_FORMATS) {
    if (format.test(datetimeStr)) {
      try {
        // Normalize timezone indicators
        let normalizedStr = datetimeStr.replace('Z', '+00:00');
        const date = new Date(normalizedStr);
        if (!isNaN(date.getTime())) {
          return date;
        }
      } catch (error) {
        continue;
      }
    }
  }

  console.error(`Unable to parse datetime: ${datetimeStr}`);
  return null;
}

/**
 * Parse date string with multiple format attempts
 * @param {string|Date|null} dateInput - Input to parse
 * @returns {Date|null} Parsed Date object (time set to midnight) or null if parsing fails
 */
export function parseDateRobust(dateInput) {
  if (!dateInput) {
    return null;
  }

  // If already a Date object, return date part only
  if (dateInput instanceof Date) {
    if (isNaN(dateInput.getTime())) {
      return null;
    }
    const dateOnly = new Date(dateInput);
    dateOnly.setHours(0, 0, 0, 0);
    return dateOnly;
  }

  // Must be a string at this point
  if (typeof dateInput !== 'string') {
    console.error(`Invalid date input type: ${typeof dateInput}`);
    return null;
  }

  const dateStr = dateInput.trim();
  if (!dateStr) {
    return null;
  }

  // Try parsing with Date constructor
  try {
    const date = new Date(dateStr);
    if (!isNaN(date.getTime())) {
      date.setHours(0, 0, 0, 0);
      return date;
    }
  } catch (error) {
    // Continue to manual parsing
  }

  console.error(`Unable to parse date: ${dateStr}`);
  return null;
}

/**
 * Safely combine date and time
 * @param {string|Date} dateInput - Date input
 * @param {string} timeInput - Time input (HH:MM format)
 * @returns {Date|null} Combined datetime or null if combination fails
 */
export function combineDateTimeSafe(dateInput, timeInput) {
  try {
    // Parse date
    const parsedDate = parseDateRobust(dateInput);
    if (!parsedDate) {
      console.error(`Failed to parse date: ${dateInput}`);
      return null;
    }

    // Parse time
    if (!timeInput || typeof timeInput !== 'string') {
      console.error(`Invalid time input: ${timeInput}`);
      return null;
    }

    const timeStr = timeInput.trim();
    const timeMatch = timeStr.match(/^(\d{1,2}):(\d{2})(?::(\d{2}))?$/);
    
    if (!timeMatch) {
      console.error(`Invalid time format: ${timeInput}`);
      return null;
    }

    const hours = parseInt(timeMatch[1], 10);
    const minutes = parseInt(timeMatch[2], 10);
    const seconds = timeMatch[3] ? parseInt(timeMatch[3], 10) : 0;

    // Validate time values
    if (hours < 0 || hours > 23 || minutes < 0 || minutes > 59 || seconds < 0 || seconds > 59) {
      console.error(`Invalid time values: ${timeInput}`);
      return null;
    }

    // Combine date and time
    const combined = new Date(parsedDate);
    combined.setHours(hours, minutes, seconds, 0);

    return combined;

  } catch (error) {
    console.error(`Error combining date ${dateInput} and time ${timeInput}:`, error);
    return null;
  }
}

/**
 * Validate time format
 * @param {string} timeStr - Time string to validate
 * @returns {boolean} True if format is valid, false otherwise
 */
export function validateTimeFormat(timeStr) {
  if (typeof timeStr !== 'string') {
    return false;
  }

  const trimmed = timeStr.trim();
  return TIME_FORMATS.some(format => format.test(trimmed));
}

/**
 * Validate date format
 * @param {string} dateStr - Date string to validate
 * @returns {boolean} True if format is valid, false otherwise
 */
export function validateDateFormat(dateStr) {
  if (typeof dateStr !== 'string') {
    return false;
  }

  return parseDateRobust(dateStr) !== null;
}

/**
 * Format date for display
 * @param {string|Date} dateInput - Date to format
 * @param {string} locale - Locale for formatting (default: 'en-US')
 * @param {Object} options - Intl.DateTimeFormat options
 * @returns {string|null} Formatted date string or null if formatting fails
 */
export function formatDateDisplay(dateInput, locale = 'en-US', options = {}) {
  try {
    const date = parseDateRobust(dateInput);
    if (!date) {
      return null;
    }

    const defaultOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      ...options
    };

    return date.toLocaleDateString(locale, defaultOptions);

  } catch (error) {
    console.error(`Error formatting date ${dateInput}:`, error);
    return null;
  }
}

/**
 * Format time for display in 12-hour format
 * @param {string|Date} timeInput - Time to format
 * @returns {string|null} Formatted time string (e.g., "9:00 AM") or null if formatting fails
 */
export function formatTimeDisplay(timeInput) {
  try {
    let date;

    if (typeof timeInput === 'string') {
      // If it's a time string, combine with today's date
      const today = new Date();
      date = combineDateTimeSafe(today, timeInput);
    } else if (timeInput instanceof Date) {
      date = timeInput;
    } else {
      return null;
    }

    if (!date) {
      return null;
    }

    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });

  } catch (error) {
    console.error(`Error formatting time ${timeInput}:`, error);
    return null;
  }
}

/**
 * Get ISO date string (YYYY-MM-DD)
 * @param {string|Date} dateInput - Date to format
 * @returns {string|null} ISO date string or null if formatting fails
 */
export function getISODateString(dateInput) {
  try {
    const date = parseDateRobust(dateInput);
    if (!date) {
      return null;
    }

    return date.toISOString().split('T')[0];

  } catch (error) {
    console.error(`Error getting ISO date string for ${dateInput}:`, error);
    return null;
  }
}

/**
 * Get tomorrow's date in ISO format
 * @returns {string} Tomorrow's date in YYYY-MM-DD format
 */
export function getTomorrowDate() {
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  return getISODateString(tomorrow);
}

/**
 * Get date string for N days ago
 * @param {number} daysAgo - Number of days ago
 * @returns {string} Date string in YYYY-MM-DD format
 */
export function getDateString(daysAgo = 0) {
  const date = new Date();
  date.setDate(date.getDate() - daysAgo);
  return getISODateString(date);
}

/**
 * Check if a date is in the past
 * @param {string|Date} dateInput - Date to check
 * @returns {boolean} True if date is in the past
 */
export function isDateInPast(dateInput) {
  const date = parseDateRobust(dateInput);
  if (!date) {
    return false;
  }

  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  return date < today;
}

/**
 * Check if a date is too far in the future
 * @param {string|Date} dateInput - Date to check
 * @param {number} maxYears - Maximum years in the future (default: 1)
 * @returns {boolean} True if date is too far in the future
 */
export function isDateTooFarFuture(dateInput, maxYears = 1) {
  const date = parseDateRobust(dateInput);
  if (!date) {
    return false;
  }

  const maxDate = new Date();
  maxDate.setFullYear(maxDate.getFullYear() + maxYears);

  return date > maxDate;
}

/**
 * Convert time string to minutes with robust validation
 * @param {string} timeStr - Time string in HH:MM format
 * @returns {number} Total minutes (0 if invalid)
 */
export function timeToMinutes(timeStr) {
  if (!timeStr || typeof timeStr !== 'string') {
    return 0;
  }

  const trimmed = timeStr.trim();

  // Validate time format (HH:MM) - supports both 24-hour format
  if (!/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(trimmed)) {
    console.warn(`Invalid time format: ${timeStr}. Expected format: HH:MM (24-hour)`);
    return 0;
  }

  const [hours, minutes] = trimmed.split(':').map(Number);
  if (isNaN(hours) || isNaN(minutes)) {
    console.warn(`Invalid time values: ${timeStr}. Hours and minutes must be numeric.`);
    return 0;
  }

  // Additional validation for time ranges
  if (hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
    console.warn(`Time values out of range: ${timeStr}. Hours: 0-23, Minutes: 0-59.`);
    return 0;
  }

  return hours * 60 + minutes;
}

/**
 * Convert minutes to time string
 * @param {number} totalMinutes - Total minutes
 * @returns {string} Time string in HH:MM format
 */
export function minutesToTime(totalMinutes) {
  if (typeof totalMinutes !== 'number' || isNaN(totalMinutes) || totalMinutes < 0) {
    console.warn(`Invalid minutes value: ${totalMinutes}. Must be a non-negative number.`);
    return '00:00';
  }

  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;

  // Handle overflow (more than 24 hours)
  if (hours >= 24) {
    console.warn(`Time overflow: ${totalMinutes} minutes (${hours}:${minutes}). Capping at 23:59.`);
    return '23:59';
  }

  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
}

export default {
  parseDateTimeRobust,
  parseDateRobust,
  combineDateTimeSafe,
  validateTimeFormat,
  validateDateFormat,
  formatDateDisplay,
  formatTimeDisplay,
  getISODateString,
  getTomorrowDate,
  getDateString,
  isDateInPast,
  isDateTooFarFuture,
  timeToMinutes,
  minutesToTime
};
