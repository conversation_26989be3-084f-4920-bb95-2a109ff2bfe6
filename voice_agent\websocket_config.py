import os
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from enum import Enum

# Module-local logger
logger = logging.getLogger(__name__)

class WebSocketMode(Enum):
    """WebSocket operation modes"""
    WEBHOOK_ONLY = "webhook_only"
    WEBSOCKET_ONLY = "websocket_only"
    HYBRID = "hybrid"  # Support both webhook and WebSocket

@dataclass
class WebSocketConfig:
    """
    Configuration for WebSocket server and connections.
    All settings can be overridden via environment variables.
    """
    
    # Server configuration
    host: str = field(default_factory=lambda: os.environ.get("WEBSOCKET_HOST", "0.0.0.0"))
    port: int = field(default_factory=lambda: int(os.environ.get("WEBSOCKET_PORT", "8765")))
    
    # Connection limits
    max_connections: int = field(default_factory=lambda: int(os.environ.get("MAX_WEBSOCKET_CONNECTIONS", "1000")))
    max_connections_per_hospital: int = field(default_factory=lambda: int(os.environ.get("MAX_CONNECTIONS_PER_HOSPITAL", "100")))
    
    # Timing configuration
    heartbeat_interval: float = field(default_factory=lambda: float(os.environ.get("WEBSOCKET_HEARTBEAT_INTERVAL", "30.0")))
    cleanup_interval: float = field(default_factory=lambda: float(os.environ.get("WEBSOCKET_CLEANUP_INTERVAL", "300.0")))
    connection_timeout: float = field(default_factory=lambda: float(os.environ.get("WEBSOCKET_CONNECTION_TIMEOUT", "60.0")))
    message_timeout: float = field(default_factory=lambda: float(os.environ.get("WEBSOCKET_MESSAGE_TIMEOUT", "30.0")))
    
    # Reconnection settings
    max_reconnection_attempts: int = field(default_factory=lambda: int(os.environ.get("WEBSOCKET_MAX_RECONNECTION_ATTEMPTS", "5")))
    reconnection_base_delay: float = field(default_factory=lambda: float(os.environ.get("WEBSOCKET_RECONNECTION_BASE_DELAY", "1.0")))
    reconnection_max_delay: float = field(default_factory=lambda: float(os.environ.get("WEBSOCKET_RECONNECTION_MAX_DELAY", "60.0")))
    
    # Message handling
    max_message_size: int = field(default_factory=lambda: int(os.environ.get("WEBSOCKET_MAX_MESSAGE_SIZE", "1048576")))  # 1MB
    max_queue_size: int = field(default_factory=lambda: int(os.environ.get("WEBSOCKET_MAX_QUEUE_SIZE", "100")))
    
    # Error handling
    max_error_rate: float = field(default_factory=lambda: float(os.environ.get("WEBSOCKET_MAX_ERROR_RATE", "0.1")))  # 10%
    error_window_seconds: int = field(default_factory=lambda: int(os.environ.get("WEBSOCKET_ERROR_WINDOW", "60")))
    
    # Security settings
    enable_compression: bool = field(default_factory=lambda: os.environ.get("WEBSOCKET_ENABLE_COMPRESSION", "false").lower() == "true")
    require_subprotocol: bool = field(default_factory=lambda: os.environ.get("WEBSOCKET_REQUIRE_SUBPROTOCOL", "true").lower() == "true")
    allowed_origins: List[str] = field(default_factory=lambda: os.environ.get("WEBSOCKET_ALLOWED_ORIGINS", "*").split(","))
    
    # Operation mode
    mode: WebSocketMode = field(default_factory=lambda: WebSocketMode(os.environ.get("WEBSOCKET_MODE", "websocket_only")))
    
    # Jambonz-specific settings
    jambonz_subprotocol: str = field(default_factory=lambda: os.environ.get("JAMBONZ_SUBPROTOCOL", "ws.jambonz.org"))
    default_hospital_id: str = field(default_factory=lambda: os.environ.get("DEFAULT_HOSPITAL_ID", "1"))
    
    # Monitoring and logging
    enable_metrics: bool = field(default_factory=lambda: os.environ.get("WEBSOCKET_ENABLE_METRICS", "true").lower() == "true")
    metrics_interval: float = field(default_factory=lambda: float(os.environ.get("WEBSOCKET_METRICS_INTERVAL", "60.0")))
    log_level: str = field(default_factory=lambda: os.environ.get("WEBSOCKET_LOG_LEVEL", "INFO"))
    
    # Performance tuning
    enable_ping_pong: bool = field(default_factory=lambda: os.environ.get("WEBSOCKET_ENABLE_PING_PONG", "true").lower() == "true")
    ping_interval: float = field(default_factory=lambda: float(os.environ.get("WEBSOCKET_PING_INTERVAL", "30.0")))
    ping_timeout: float = field(default_factory=lambda: float(os.environ.get("WEBSOCKET_PING_TIMEOUT", "60.0")))

    # Language configuration for voice calls
    default_language: str = field(default_factory=lambda: os.environ.get("DEFAULT_LANGUAGE", "hi"))
    supported_languages: List[str] = field(default_factory=lambda: os.environ.get("SUPPORTED_LANGUAGES", "hi,en,bn,ta,te,gu,kn,ml,mr,or,pa,as").split(","))
    enable_auto_language_detection: bool = field(default_factory=lambda: os.environ.get("ENABLE_AUTO_LANGUAGE_DETECTION", "true").lower() == "true")
    language_detection_confidence_threshold: float = field(default_factory=lambda: float(os.environ.get("LANGUAGE_DETECTION_THRESHOLD", "0.7")))
    fallback_to_primary_language: bool = field(default_factory=lambda: os.environ.get("FALLBACK_TO_PRIMARY_LANGUAGE", "true").lower() == "true")
    enable_hospital_language_preferences: bool = field(default_factory=lambda: os.environ.get("ENABLE_HOSPITAL_LANGUAGE_PREFERENCES", "true").lower() == "true")

    def __post_init__(self):
        """Validate configuration after initialization"""
        self._validate_config()
    
    def _validate_config(self):
        """Validate configuration values"""
        errors = []
        
        # Validate port range
        if not (1 <= self.port <= 65535):
            errors.append(f"Invalid port: {self.port}. Must be between 1 and 65535.")
        
        # Validate connection limits
        if self.max_connections <= 0:
            errors.append(f"max_connections must be positive: {self.max_connections}")
        
        if self.max_connections_per_hospital <= 0:
            errors.append(f"max_connections_per_hospital must be positive: {self.max_connections_per_hospital}")
        
        if self.max_connections_per_hospital > self.max_connections:
            errors.append(f"max_connections_per_hospital ({self.max_connections_per_hospital}) cannot exceed max_connections ({self.max_connections})")
        
        # Validate timing values
        if self.heartbeat_interval <= 0:
            errors.append(f"heartbeat_interval must be positive: {self.heartbeat_interval}")
        
        if self.cleanup_interval <= 0:
            errors.append(f"cleanup_interval must be positive: {self.cleanup_interval}")
        
        if self.connection_timeout <= 0:
            errors.append(f"connection_timeout must be positive: {self.connection_timeout}")

        if self.message_timeout <= 0:
            errors.append(f"message_timeout must be positive: {self.message_timeout}")

        # Validate reconnection settings
        if self.max_reconnection_attempts < 0:
            errors.append(f"max_reconnection_attempts cannot be negative: {self.max_reconnection_attempts}")
        
        if self.reconnection_base_delay <= 0:
            errors.append(f"reconnection_base_delay must be positive: {self.reconnection_base_delay}")
        
        if self.reconnection_max_delay <= self.reconnection_base_delay:
            errors.append(f"reconnection_max_delay ({self.reconnection_max_delay}) must be greater than reconnection_base_delay ({self.reconnection_base_delay})")
        
        # Validate message settings
        if self.max_message_size <= 0:
            errors.append(f"max_message_size must be positive: {self.max_message_size}")
        
        if self.max_queue_size <= 0:
            errors.append(f"max_queue_size must be positive: {self.max_queue_size}")
        
        # Validate error handling
        if not (0 <= self.max_error_rate <= 1):
            errors.append(f"max_error_rate must be between 0 and 1: {self.max_error_rate}")
        
        if self.error_window_seconds <= 0:
            errors.append(f"error_window_seconds must be positive: {self.error_window_seconds}")
        
        # Validate ping settings
        if self.enable_ping_pong:
            if self.ping_interval <= 0:
                errors.append(f"ping_interval must be positive when ping_pong enabled: {self.ping_interval}")
            
            if self.ping_timeout <= self.ping_interval:
                errors.append(f"ping_timeout ({self.ping_timeout}) must be greater than ping_interval ({self.ping_interval})")

        # Validate language configuration
        if not self.default_language:
            errors.append("default_language cannot be empty")

        if not self.supported_languages:
            errors.append("supported_languages cannot be empty")

        if self.default_language not in self.supported_languages:
            errors.append(f"default_language '{self.default_language}' must be in supported_languages")

        if not (0.0 <= self.language_detection_confidence_threshold <= 1.0):
            errors.append(f"language_detection_confidence_threshold must be between 0.0 and 1.0: {self.language_detection_confidence_threshold}")

        if errors:
            error_msg = "WebSocket configuration validation failed:\n" + "\n".join(f"  - {error}" for error in errors)
            logger.error(error_msg)
            raise ValueError(error_msg)
        
        logger.info("WebSocket configuration validated successfully")
    
    def get_websockets_server_kwargs(self) -> Dict[str, Any]:
        """
        Get keyword arguments for websockets.serve()
        
        Returns:
            Dictionary of server configuration
        """
        kwargs = {
            "host": self.host,
            "port": self.port,
            "max_size": self.max_message_size,
            "max_queue": self.max_queue_size,
            "compression": "deflate" if self.enable_compression else None,
        }
        
        # Add subprotocol requirement
        if self.require_subprotocol:
            kwargs["subprotocols"] = [self.jambonz_subprotocol]
        
        # Add ping/pong settings
        if self.enable_ping_pong:
            kwargs["ping_interval"] = self.ping_interval
            kwargs["ping_timeout"] = self.ping_timeout
        else:
            kwargs["ping_interval"] = None
            kwargs["ping_timeout"] = None
        
        return kwargs
    
    def get_connection_manager_kwargs(self) -> Dict[str, Any]:
        """
        Get keyword arguments for connection manager
        
        Returns:
            Dictionary of connection manager configuration
        """
        return {
            "max_connections": self.max_connections,
            "heartbeat_interval": self.heartbeat_interval,
            "cleanup_interval": self.cleanup_interval,
            "connection_timeout": self.connection_timeout,
            "message_timeout": self.message_timeout
        }
    
    def get_reconnection_manager_kwargs(self) -> Dict[str, Any]:
        """
        Get keyword arguments for reconnection manager
        
        Returns:
            Dictionary of reconnection manager configuration
        """
        return {
            "max_retries": self.max_reconnection_attempts,
            "base_delay": self.reconnection_base_delay,
            "max_delay": self.reconnection_max_delay
        }
    
    def is_webhook_enabled(self) -> bool:
        """Check if webhook mode is enabled"""
        return self.mode in [WebSocketMode.WEBHOOK_ONLY, WebSocketMode.HYBRID]
    
    def is_websocket_enabled(self) -> bool:
        """Check if WebSocket mode is enabled"""
        return self.mode in [WebSocketMode.WEBSOCKET_ONLY, WebSocketMode.HYBRID]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            "server": {
                "host": self.host,
                "port": self.port,
                "mode": self.mode.value
            },
            "connections": {
                "max_connections": self.max_connections,
                "max_connections_per_hospital": self.max_connections_per_hospital,
                "connection_timeout": self.connection_timeout
            },
            "timing": {
                "heartbeat_interval": self.heartbeat_interval,
                "cleanup_interval": self.cleanup_interval,
                "message_timeout": self.message_timeout
            },
            "reconnection": {
                "max_attempts": self.max_reconnection_attempts,
                "base_delay": self.reconnection_base_delay,
                "max_delay": self.reconnection_max_delay
            },
            "messages": {
                "max_size": self.max_message_size,
                "max_queue_size": self.max_queue_size
            },
            "error_handling": {
                "max_error_rate": self.max_error_rate,
                "error_window_seconds": self.error_window_seconds
            },
            "security": {
                "enable_compression": self.enable_compression,
                "require_subprotocol": self.require_subprotocol,
                "allowed_origins": self.allowed_origins
            },
            "jambonz": {
                "subprotocol": self.jambonz_subprotocol,
                "default_hospital_id": self.default_hospital_id
            },
            "monitoring": {
                "enable_metrics": self.enable_metrics,
                "metrics_interval": self.metrics_interval,
                "log_level": self.log_level
            },
            "performance": {
                "enable_ping_pong": self.enable_ping_pong,
                "ping_interval": self.ping_interval,
                "ping_timeout": self.ping_timeout
            }
        }
    
    @classmethod
    def from_env(cls) -> 'WebSocketConfig':
        """
        Create configuration from environment variables
        
        Returns:
            WebSocketConfig instance
        """
        return cls()
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'WebSocketConfig':
        """
        Create configuration from dictionary (inverse of to_dict)

        Args:
            config_dict: Configuration dictionary (can be nested or flat)

        Returns:
            WebSocketConfig instance

        Raises:
            ValueError: If configuration values are invalid
        """
        # Handle both nested (from to_dict) and flat dictionary formats
        flat_config = {}

        # Define the mapping from to_dict nested keys to dataclass field names
        nested_key_mapping = {
            # server section
            ("server", "host"): "host",
            ("server", "port"): "port",
            ("server", "mode"): "mode",

            # connections section
            ("connections", "max_connections"): "max_connections",
            ("connections", "max_connections_per_hospital"): "max_connections_per_hospital",
            ("connections", "connection_timeout"): "connection_timeout",

            # timing section
            ("timing", "heartbeat_interval"): "heartbeat_interval",
            ("timing", "cleanup_interval"): "cleanup_interval",
            ("timing", "message_timeout"): "message_timeout",

            # reconnection section
            ("reconnection", "max_attempts"): "max_reconnection_attempts",
            ("reconnection", "base_delay"): "reconnection_base_delay",
            ("reconnection", "max_delay"): "reconnection_max_delay",

            # messages section
            ("messages", "max_size"): "max_message_size",
            ("messages", "max_queue_size"): "max_queue_size",

            # error_handling section
            ("error_handling", "max_error_rate"): "max_error_rate",
            ("error_handling", "error_window_seconds"): "error_window_seconds",

            # security section
            ("security", "enable_compression"): "enable_compression",
            ("security", "require_subprotocol"): "require_subprotocol",
            ("security", "allowed_origins"): "allowed_origins",

            # jambonz section
            ("jambonz", "subprotocol"): "jambonz_subprotocol",
            ("jambonz", "default_hospital_id"): "default_hospital_id",

            # monitoring section
            ("monitoring", "enable_metrics"): "enable_metrics",
            ("monitoring", "metrics_interval"): "metrics_interval",
            ("monitoring", "log_level"): "log_level",

            # performance section
            ("performance", "enable_ping_pong"): "enable_ping_pong",
            ("performance", "ping_interval"): "ping_interval",
            ("performance", "ping_timeout"): "ping_timeout",
        }

        # Check if this is a nested dictionary (from to_dict) or flat dictionary
        is_nested = any(isinstance(v, dict) for v in config_dict.values())

        if is_nested:
            # Handle nested dictionary format (from to_dict)
            for section, values in config_dict.items():
                if isinstance(values, dict):
                    for key, value in values.items():
                        # Map nested keys to dataclass field names
                        field_name = nested_key_mapping.get((section, key))
                        if field_name:
                            flat_config[field_name] = value
                        else:
                            logger.warning(f"Unknown nested config key: {section}.{key}")
                else:
                    # Handle top-level values
                    flat_config[section] = values
        else:
            # Handle flat dictionary format (direct field mapping)
            flat_config = config_dict.copy()

        # Perform type conversions for specific fields
        flat_config = cls._convert_types(flat_config)

        # Filter out None values and unknown fields
        import dataclasses
        valid_fields = {f.name for f in dataclasses.fields(cls)}
        filtered_config = {k: v for k, v in flat_config.items()
                          if k in valid_fields and v is not None}

        return cls(**filtered_config)

    @classmethod
    def _convert_types(cls, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Convert string values back to their proper types

        Args:
            config: Configuration dictionary with potentially string values

        Returns:
            Configuration dictionary with proper types
        """
        converted = config.copy()

        # Convert mode string back to enum if present
        if 'mode' in converted and isinstance(converted['mode'], str):
            try:
                converted['mode'] = WebSocketMode(converted['mode'])
            except ValueError as e:
                logger.warning(f"Invalid WebSocket mode '{converted['mode']}', using default")
                converted['mode'] = WebSocketMode.WEBSOCKET_ONLY

        # Convert numeric strings to proper types
        numeric_fields = {
            'port': int,
            'max_connections': int,
            'max_connections_per_hospital': int,
            'max_reconnection_attempts': int,
            'max_message_size': int,
            'max_queue_size': int,
            'error_window_seconds': int,
            'heartbeat_interval': float,
            'cleanup_interval': float,
            'connection_timeout': float,
            'message_timeout': float,
            'reconnection_base_delay': float,
            'reconnection_max_delay': float,
            'max_error_rate': float,
            'metrics_interval': float,
            'ping_interval': float,
            'ping_timeout': float,
        }

        for field, field_type in numeric_fields.items():
            if field in converted and isinstance(converted[field], str):
                try:
                    converted[field] = field_type(converted[field])
                except (ValueError, TypeError) as e:
                    logger.warning(f"Invalid {field} value '{converted[field]}', removing from config")
                    # Remove invalid values to prevent validation errors
                    del converted[field]

        # Convert boolean strings to proper types
        boolean_fields = [
            'enable_compression', 'require_subprotocol', 'enable_metrics',
            'enable_ping_pong'
        ]

        for field in boolean_fields:
            if field in converted and isinstance(converted[field], str):
                converted[field] = converted[field].lower() in ('true', '1', 'yes', 'on')

        # Convert allowed_origins string to list if needed
        if 'allowed_origins' in converted and isinstance(converted['allowed_origins'], str):
            converted['allowed_origins'] = [origin.strip() for origin in converted['allowed_origins'].split(',')]

        return converted

# Global configuration instance
websocket_config = WebSocketConfig.from_env()

def get_websocket_config() -> WebSocketConfig:
    """Get global WebSocket configuration"""
    return websocket_config

def reload_websocket_config() -> WebSocketConfig:
    """Reload configuration from environment variables"""
    global websocket_config
    websocket_config = WebSocketConfig.from_env()
    logger.info("WebSocket configuration reloaded from environment")
    return websocket_config
