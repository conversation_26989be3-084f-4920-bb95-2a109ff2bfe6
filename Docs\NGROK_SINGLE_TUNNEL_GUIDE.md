# ngrok Single Tunnel Guide for Voice Agent

## Overview

This guide explains how to use the Voice Agent system with <PERSON><PERSON>'s free plan (single tunnel limitation) by utilizing the WebSocket proxy feature that routes everything through port 8000.

## 🎯 **Solution: WebSocket Proxy Through FastAPI**

The Voice Agent now includes a WebSocket proxy endpoint that forwards WebSocket connections from FastAPI (port 8000) to the internal WebSocket server (port 8765). This allows you to expose everything through a single ngrok tunnel.

## 🚀 **Setup Instructions**

### Step 1: Start the Voice Agent

```bash
# Navigate to your project directory
cd /path/to/VoiceHealthPortalDNT

# Start the voice agent (both FastAPI and WebSocket servers)
python -m uvicorn voice_agent.main:app --host 0.0.0.0 --port 8000
```

**Verify both servers are running:**
```bash
# Check FastAPI server
curl http://localhost:8000/health

# Check WebSocket server status
curl http://localhost:8000/health/websocket
```

### Step 2: Create Single ngrok Tunnel

```bash
# Create tunnel for FastAPI port only (this handles both HTTP and WebSocket traffic)
ngrok http 8000
```

**Example ngrok output:**
```
Session Status                online
Account                       your-account (Plan: Free)
Version                       3.x.x
Region                        United States (us)
Latency                       45ms
Web Interface                 http://127.0.0.1:4040
Forwarding                    https://abc123.ngrok.io -> http://localhost:8000
```

### Step 3: Configure Jambonz

Use the ngrok HTTPS URL with the WebSocket proxy endpoint:

**Jambonz Application Configuration:**
```
WebSocket URL: wss://abc123.ngrok.io/ws/jambonz/{hospital_id}
```

**Examples for different hospitals:**
- Hospital 1: `wss://abc123.ngrok.io/ws/jambonz/1`
- Hospital 2: `wss://abc123.ngrok.io/ws/jambonz/hospital_2`
- Hospital 3: `wss://abc123.ngrok.io/ws/jambonz/mumbai_general`

## 🔧 **How the WebSocket Proxy Works**

### Architecture Overview

```
Jambonz → ngrok tunnel → FastAPI (port 8000) → WebSocket Proxy → Internal WebSocket Server (port 8765)
```

### Proxy Flow

1. **Jambonz connects** to `wss://your-ngrok-url.ngrok.io/ws/jambonz/{hospital_id}`
2. **FastAPI receives** the WebSocket connection on the proxy endpoint
3. **Proxy establishes** internal connection to `ws://localhost:8765/ws/jambonz/{hospital_id}`
4. **Bidirectional forwarding** between external and internal WebSocket connections
5. **Hospital ID preserved** throughout the proxy chain

### Code Implementation

<augment_code_snippet path="voice_agent/main.py" mode="EXCERPT">
````python
@app.websocket("/ws/jambonz/{hospital_id}")
async def websocket_proxy(websocket: WebSocket, hospital_id: str):
    """WebSocket proxy for single ngrok tunnel support"""
    await websocket.accept(subprotocol="ws.jambonz.org")
    
    # Connect to internal WebSocket server
    internal_ws_uri = f"ws://localhost:{websocket_config.port}/ws/jambonz/{hospital_id}"
    
    async with websockets.connect(internal_ws_uri, subprotocols=["ws.jambonz.org"]) as internal_ws:
        # Bidirectional message forwarding
        await asyncio.gather(
            proxy_to_internal(),
            proxy_from_internal(),
            return_exceptions=True
        )
````
</augment_code_snippet>

## ✅ **Testing the Setup**

### 1. Test WebSocket Proxy Connection

```bash
# Install wscat if not available
npm install -g wscat

# Test connection through the proxy
wscat -c wss://your-ngrok-url.ngrok.io/ws/jambonz/1 -s ws.jambonz.org
```

### 2. Test Jambonz Message Flow

Send a test message through the proxy:

```json
{
  "type": "session:new",
  "msgid": "test-001",
  "call_sid": "call-001",
  "from": "+**********",
  "to": "+**********"
}
```

**Expected Response:**
```json
{
  "type": "ack",
  "msgid": "test-001",
  "verbs": [
    {
      "verb": "gather",
      "actionHook": "websocket",
      "say": {
        "text": "Welcome message..."
      }
    }
  ]
}
```

### 3. Verify Hospital Routing

Test different hospital IDs:

```bash
# Terminal 1: Hospital 1
wscat -c wss://your-ngrok-url.ngrok.io/ws/jambonz/1 -s ws.jambonz.org

# Terminal 2: Hospital 2
wscat -c wss://your-ngrok-url.ngrok.io/ws/jambonz/2 -s ws.jambonz.org

# Terminal 3: Check metrics
curl https://your-ngrok-url.ngrok.io/websocket/metrics | jq '.hospital_stats'
```

## 📊 **Monitoring and Debugging**

### 1. Check Proxy Status

```bash
# View proxy connections and metrics
curl https://your-ngrok-url.ngrok.io/websocket/status

# Check detailed metrics
curl https://your-ngrok-url.ngrok.io/websocket/metrics
```

### 2. Monitor Logs

Watch for proxy-related log messages:

```bash
# Monitor voice agent logs
tail -f voice_agent.log | grep -i "proxy\|websocket"
```

**Key log messages to look for:**
- `WebSocket proxy connection established for hospital: {hospital_id}`
- `Connected to internal WebSocket server for hospital: {hospital_id}`
- `Proxied message to/from internal WS`

### 3. ngrok Web Interface

Access ngrok's web interface for traffic inspection:
```
http://127.0.0.1:4040
```

This shows all HTTP and WebSocket traffic going through the tunnel.

## 🚨 **Troubleshooting**

### Issue 1: WebSocket Connection Fails

**Symptoms**: Connection refused or timeout when connecting to ngrok URL

**Solutions**:
1. Verify both servers are running:
   ```bash
   curl http://localhost:8000/health/websocket
   ```

2. Check ngrok tunnel status:
   ```bash
   curl http://127.0.0.1:4040/api/tunnels
   ```

3. Test local proxy connection:
   ```bash
   wscat -c ws://localhost:8000/ws/jambonz/1 -s ws.jambonz.org
   ```

### Issue 2: Messages Not Being Proxied

**Symptoms**: Connection established but no message flow

**Solutions**:
1. Check proxy logs for errors
2. Verify internal WebSocket server is accepting connections:
   ```bash
   wscat -c ws://localhost:8765/ws/jambonz/1 -s ws.jambonz.org
   ```

3. Test message flow directly to internal server first

### Issue 3: Hospital ID Not Preserved

**Symptoms**: All connections show same hospital ID

**Solutions**:
1. Verify URL format: `/ws/jambonz/{hospital_id}`
2. Check proxy logs for hospital ID extraction
3. Test with different hospital IDs in URL

## 🔄 **Alternative Solutions**

### Option 1: Use Only WebSocket Port (Simpler)

If you don't need FastAPI endpoints accessible externally:

```bash
# Tunnel only WebSocket port
ngrok http 8765

# Configure Jambonz with direct WebSocket URL
# wss://your-ngrok-url.ngrok.io/ws/jambonz/{hospital_id}
```

### Option 2: ngrok Paid Plan

Consider upgrading to ngrok's paid plan for multiple tunnels:
- **Personal Plan**: $8/month - 3 tunnels
- **Pro Plan**: $20/month - 10 tunnels

### Option 3: Alternative Tunneling Services

Free alternatives with multiple tunnel support:
- **LocalTunnel**: `npm install -g localtunnel`
- **Serveo**: SSH-based tunneling
- **Cloudflare Tunnel**: Free with Cloudflare account

## 📋 **Production Considerations**

### Security

- ngrok free plan exposes your service publicly
- Consider authentication for production use
- Monitor ngrok web interface for unexpected traffic

### Performance

- Proxy adds ~1-2ms latency per message
- Monitor response times: `curl https://your-ngrok-url.ngrok.io/websocket/metrics`
- Consider paid ngrok plan for better performance

### Reliability

- ngrok free URLs change on restart
- Update Jambonz configuration when ngrok restarts
- Consider paid ngrok plan for stable URLs

## 🎯 **Quick Reference**

### Commands Summary

```bash
# 1. Start voice agent
python -m uvicorn voice_agent.main:app --host 0.0.0.0 --port 8000

# 2. Start ngrok tunnel
ngrok http 8000

# 3. Configure Jambonz
# WebSocket URL: wss://your-ngrok-url.ngrok.io/ws/jambonz/{hospital_id}

# 4. Test connection
wscat -c wss://your-ngrok-url.ngrok.io/ws/jambonz/1 -s ws.jambonz.org
```

### URLs Summary

- **FastAPI Health**: `https://your-ngrok-url.ngrok.io/health`
- **WebSocket Status**: `https://your-ngrok-url.ngrok.io/health/websocket`
- **WebSocket Proxy**: `wss://your-ngrok-url.ngrok.io/ws/jambonz/{hospital_id}`
- **Metrics**: `https://your-ngrok-url.ngrok.io/websocket/metrics`

This setup allows you to use the full Voice Agent functionality with ngrok's free single-tunnel limitation! 🎉
