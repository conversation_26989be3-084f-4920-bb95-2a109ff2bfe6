"""
Migration Helper for Voice Agent to Shared Redis Implementation

Provides utilities to help migrate voice_agent from its current Redis implementation
to the new shared Redis implementation while maintaining compatibility.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional
from .adapters.python_adapter import get_python_adapter

logger = logging.getLogger(__name__)


class VoiceAgentMigrationHelper:
    """
    Helper class to migrate voice_agent to shared Redis implementation.
    Provides compatibility layer and migration utilities.
    """
    
    def __init__(self):
        """Initialize migration helper."""
        self.shared_adapter = get_python_adapter()
        self._migration_stats = {
            "keys_migrated": 0,
            "errors": 0,
            "start_time": None,
            "end_time": None
        }
    
    def create_compatibility_layer(self) -> 'RedisManagerCompatibility':
        """
        Create a compatibility layer that mimics the existing voice_agent RedisManager.
        
        Returns:
            Compatibility layer instance
        """
        return RedisManagerCompatibility(self.shared_adapter)
    
    async def migrate_existing_data(self, old_redis_client, hospital_ids: List[str]) -> Dict[str, Any]:
        """
        Migrate existing data from old Redis instance to shared implementation.
        
        Args:
            old_redis_client: Existing Redis client
            hospital_ids: List of hospital IDs to migrate
            
        Returns:
            Migration results
        """
        import time
        self._migration_stats["start_time"] = time.time()
        
        try:
            for hospital_id in hospital_ids:
                await self._migrate_hospital_data(old_redis_client, hospital_id)
            
            self._migration_stats["end_time"] = time.time()
            logger.info(f"Migration completed: {self._migration_stats}")
            return self._migration_stats
            
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            self._migration_stats["errors"] += 1
            return self._migration_stats
    
    async def _migrate_hospital_data(self, old_redis_client, hospital_id: str):
        """Migrate data for a specific hospital."""
        try:
            # Migrate semantic cache entries
            await self._migrate_semantic_cache(old_redis_client, hospital_id)
            
            # Migrate call contexts
            await self._migrate_call_contexts(old_redis_client)
            
            # Migrate hospital data
            await self._migrate_hospital_cache(old_redis_client, hospital_id)
            
            logger.info(f"Migrated data for hospital {hospital_id}")
            
        except Exception as e:
            logger.error(f"Error migrating hospital {hospital_id}: {e}")
            self._migration_stats["errors"] += 1
    
    async def _migrate_semantic_cache(self, old_redis_client, hospital_id: str):
        """Migrate semantic cache entries."""
        try:
            pattern = f"semantic:{hospital_id}:*"
            keys = old_redis_client.keys(pattern)
            
            for key in keys:
                try:
                    data = old_redis_client.get(key)
                    if data:
                        # Parse old format and convert to new format
                        if isinstance(data, str):
                            import json
                            data = json.loads(data)
                        
                        query = data.get("query", "")
                        response = data.get("response", "")
                        category = data.get("category", "general")
                        
                        if query and response:
                            await self.shared_adapter.cache_semantic_response_async(
                                query, response, hospital_id, category
                            )
                            self._migration_stats["keys_migrated"] += 1
                
                except Exception as e:
                    logger.warning(f"Failed to migrate key {key}: {e}")
                    self._migration_stats["errors"] += 1
            
        except Exception as e:
            logger.error(f"Error migrating semantic cache for {hospital_id}: {e}")
    
    async def _migrate_call_contexts(self, old_redis_client):
        """Migrate call context entries."""
        try:
            pattern = "call:context:*"
            keys = old_redis_client.keys(pattern)
            
            for key in keys:
                try:
                    data = old_redis_client.get(key)
                    ttl = old_redis_client.ttl(key)
                    
                    if data:
                        call_id = key.split(":")[-1]
                        await self.shared_adapter.save_call_context(call_id, data, ttl if ttl > 0 else None)
                        self._migration_stats["keys_migrated"] += 1
                
                except Exception as e:
                    logger.warning(f"Failed to migrate call context {key}: {e}")
                    self._migration_stats["errors"] += 1
            
        except Exception as e:
            logger.error(f"Error migrating call contexts: {e}")
    
    async def _migrate_hospital_cache(self, old_redis_client, hospital_id: str):
        """Migrate hospital data cache."""
        try:
            patterns = [
                f"hospital:{hospital_id}:doctors",
                f"hospital:{hospital_id}:tests",
                f"hospital:{hospital_id}:*"
            ]
            
            for pattern in patterns:
                keys = old_redis_client.keys(pattern)
                
                for key in keys:
                    try:
                        data = old_redis_client.get(key)
                        ttl = old_redis_client.ttl(key)
                        
                        if data:
                            # Extract data type from key
                            parts = key.split(":")
                            if len(parts) >= 3:
                                data_type = parts[2]
                                await self.shared_adapter.cache_hospital_data_async(
                                    hospital_id, data_type, data, ttl if ttl > 0 else None
                                )
                                self._migration_stats["keys_migrated"] += 1
                    
                    except Exception as e:
                        logger.warning(f"Failed to migrate hospital data {key}: {e}")
                        self._migration_stats["errors"] += 1
            
        except Exception as e:
            logger.error(f"Error migrating hospital cache for {hospital_id}: {e}")


class RedisManagerCompatibility:
    """
    Compatibility layer that mimics the existing voice_agent RedisManager interface.
    Allows existing code to work with minimal changes.
    """
    
    def __init__(self, shared_adapter):
        """Initialize compatibility layer."""
        self.shared_adapter = shared_adapter
        self.redis_client = None  # For compatibility
        self.semantic_cache = None  # For compatibility
    
    # Basic Redis operations (compatible with existing code)
    
    def set(self, key: str, value: Any, expiry: int = None) -> bool:
        """Set key-value pair (sync version for compatibility)."""
        try:
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(self.shared_adapter.set_async(key, value, expiry))
        except RuntimeError:
            # If no event loop, create one
            return asyncio.run(self.shared_adapter.set_async(key, value, expiry))
    
    def get(self, key: str) -> Any:
        """Get value (sync version for compatibility)."""
        try:
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(self.shared_adapter.get_async(key))
        except RuntimeError:
            return asyncio.run(self.shared_adapter.get_async(key))
    
    def delete(self, *keys: str) -> int:
        """Delete keys (sync version for compatibility)."""
        try:
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(self.shared_adapter.delete_async(*keys))
        except RuntimeError:
            return asyncio.run(self.shared_adapter.delete_async(*keys))
    
    def keys(self, pattern: str = "*") -> List[str]:
        """Get keys matching pattern (sync version for compatibility)."""
        return self.shared_adapter.keys(pattern)
    
    def is_connected(self) -> bool:
        """Check connection status."""
        return self.shared_adapter.is_connected()
    
    def reconnect(self) -> bool:
        """Reconnect to Redis."""
        return self.shared_adapter.reconnect()
    
    # Async versions (preferred for new code)
    
    async def set_async(self, key: str, value: Any, expiry: int = None) -> bool:
        """Set key-value pair (async version)."""
        return await self.shared_adapter.set_async(key, value, expiry)
    
    async def get_async(self, key: str) -> Any:
        """Get value (async version)."""
        return await self.shared_adapter.get_async(key)
    
    async def delete_async(self, *keys: str) -> int:
        """Delete keys (async version)."""
        return await self.shared_adapter.delete_async(*keys)
    
    # Semantic cache operations
    
    async def cache_semantic_response_async(self, query: str, response: str, 
                                          hospital_id: str, category: str = "general") -> bool:
        """Cache semantic response."""
        return await self.shared_adapter.cache_semantic_response_async(query, response, hospital_id, category)
    
    async def semantic_search_async(self, query: str, hospital_id: str, 
                                  category: str = "general", limit: int = 5) -> List[Dict[str, Any]]:
        """Search for semantic matches."""
        return await self.shared_adapter.semantic_search_async(query, hospital_id, category, limit)
    
    # Hospital data operations
    
    async def cache_hospital_data_async(self, hospital_id: str, data_type: str, data: Any) -> bool:
        """Cache hospital data."""
        return await self.shared_adapter.cache_hospital_data_async(hospital_id, data_type, data)
    
    async def get_cached_hospital_data_async(self, hospital_id: str, data_type: str) -> Any:
        """Get cached hospital data."""
        return await self.shared_adapter.get_cached_hospital_data_async(hospital_id, data_type)
    
    # Doctor and test caching
    
    async def cache_doctor_info_async(self, query: str, doctor_data: Dict[str, Any], hospital_id: str) -> bool:
        """Cache doctor information."""
        return await self.shared_adapter.cache_doctor_info_async(query, doctor_data, hospital_id)
    
    async def cache_test_info_async(self, query: str, test_data: Dict[str, Any], hospital_id: str) -> bool:
        """Cache test information."""
        return await self.shared_adapter.cache_test_info_async(query, test_data, hospital_id)
    
    # Availability caching
    
    async def cache_availability_async(self, hospital_id: str, item_id: str, 
                                      item_type: str, date: str, is_available: bool) -> bool:
        """Cache availability information."""
        return await self.shared_adapter.cache_availability_async(hospital_id, item_id, item_type, date, is_available)
    
    async def get_availability_async(self, hospital_id: str, item_id: str,
                                    item_type: str, date: str) -> Optional[bool]:
        """Get availability information."""
        return await self.shared_adapter.get_availability_async(hospital_id, item_id, item_type, date)

    async def get_available_items_async(self, hospital_id: str, item_type: str, date: str) -> List[str]:
        """Get list of available doctor or test IDs for a specific date."""
        return await self.shared_adapter.get_available_items_async(hospital_id, item_type, date)

    async def cache_multiple_availability_async(self, hospital_id: str, date: str,
                                              availability_data: List[Dict[str, Any]],
                                              ttl: Optional[int] = None) -> bool:
        """Cache availability for multiple doctors and tests efficiently."""
        return await self.shared_adapter.cache_multiple_availability_async(hospital_id, date, availability_data, ttl)

    # Booking limit management

    async def cache_doctor_daily_limits_async(self, hospital_id: str, doctor_id: str,
                                            daily_limits: Dict[str, int], ttl: int = 86400) -> bool:
        """Cache doctor's daily booking limits."""
        return await self.shared_adapter.cache_doctor_daily_limits_async(hospital_id, doctor_id, daily_limits, ttl)

    async def get_doctor_daily_limits_async(self, hospital_id: str, doctor_id: str) -> Optional[Dict[str, int]]:
        """Get doctor's daily booking limits from cache."""
        return await self.shared_adapter.get_doctor_daily_limits_async(hospital_id, doctor_id)

    async def cache_next_available_date_async(self, hospital_id: str, doctor_id: str,
                                            next_date: str, ttl: int = 86400) -> bool:
        """Cache next available date for a doctor."""
        return await self.shared_adapter.cache_next_available_date_async(hospital_id, doctor_id, next_date, ttl)

    async def get_next_available_date_async(self, hospital_id: str, doctor_id: str) -> Optional[str]:
        """Get next available date for a doctor from cache."""
        return await self.shared_adapter.get_next_available_date_async(hospital_id, doctor_id)

    # Booking counter management

    async def increment_booking_counter_async(self, hospital_id: str, doctor_id: str, date: str) -> int:
        """Atomically increment booking counter for a doctor on a specific date."""
        return await self.shared_adapter.increment_booking_counter_async(hospital_id, doctor_id, date)

    async def get_booking_counter_async(self, hospital_id: str, doctor_id: str, date: str) -> int:
        """Get current booking counter for a doctor on a specific date."""
        return await self.shared_adapter.get_booking_counter_async(hospital_id, doctor_id, date)

    async def check_booking_availability_redis_async(self, hospital_id: str, doctor_id: str, date: str) -> Dict[str, Any]:
        """Check booking availability for a doctor on a specific date using Redis cache."""
        return await self.shared_adapter.check_booking_availability_redis_async(hospital_id, doctor_id, date)
    
    # Cache management
    
    async def clear_cache_async(self, pattern: str) -> int:
        """Clear cache entries."""
        return await self.shared_adapter.clear_cache_async(pattern)
    
    async def get_cache_stats_async(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return await self.shared_adapter.get_cache_stats_async()
    
    # Preloading
    
    async def preload_hospital_data_from_config_async(self, hospital_id: str, hospital_data: Dict[str, Any] = None) -> bool:
        """Preload hospital data."""
        return await self.shared_adapter.preload_hospital_data_async(hospital_id, hospital_data)

    # ==================== DATA LOADER INTEGRATION ====================

    async def cache_semantic_response_async(self, query: str, response_data: Dict[str, Any],
                                          hospital_id: str, category: str, ttl: Optional[int] = None) -> bool:
        """Cache semantic response for voice agent queries."""
        return await self.shared_adapter.cache_semantic_response_async(query, response_data, hospital_id, category, ttl)

    async def get_semantic_response_async(self, query: str, hospital_id: str,
                                        category: str = None) -> Optional[Dict[str, Any]]:
        """Get semantic response for voice agent queries."""
        return await self.shared_adapter.get_semantic_response_async(query, hospital_id, category)

    async def clear_semantic_cache_async(self, hospital_id: str, category: str = None) -> bool:
        """Clear semantic cache for a hospital."""
        return await self.shared_adapter.clear_semantic_cache_async(hospital_id, category)

    def get_data_loaders(self):
        """
        Get data loaders for hospital data management.

        Returns:
            Dict with data loader instances
        """
        try:
            from .data_loaders import FirebaseDataLoader, ConfigDataLoader, QueryGenerator, CachePreloader

            # Create data loader instances
            firebase_loader = FirebaseDataLoader()
            config_loader = ConfigDataLoader()
            query_generator = QueryGenerator(config_loader)
            cache_preloader = CachePreloader(self.shared_adapter)

            return {
                "firebase_loader": firebase_loader,
                "config_loader": config_loader,
                "query_generator": query_generator,
                "cache_preloader": cache_preloader
            }
        except ImportError as e:
            logger.error(f"Error importing data loaders: {e}")
            return {}

    async def preload_hospital_data_async(self, hospital_id: str,
                                        hospital_data: Dict[str, Any] = None) -> bool:
        """
        Preload hospital data into semantic cache.

        Args:
            hospital_id: Hospital identifier
            hospital_data: Optional pre-loaded hospital data

        Returns:
            bool: Success or failure
        """
        try:
            loaders = self.get_data_loaders()
            if not loaders:
                logger.error("Data loaders not available")
                return False

            cache_preloader = loaders["cache_preloader"]
            return await cache_preloader.preload_hospital_data_async(hospital_id, hospital_data)

        except Exception as e:
            logger.error(f"Error preloading hospital data: {e}")
            return False

    def preload_hospital_data_sync(self, hospital_id: str,
                                 hospital_data: Dict[str, Any] = None) -> bool:
        """
        Synchronous preload hospital data into semantic cache.

        Args:
            hospital_id: Hospital identifier
            hospital_data: Optional pre-loaded hospital data

        Returns:
            bool: Success or failure
        """
        try:
            loaders = self.get_data_loaders()
            if not loaders:
                logger.error("Data loaders not available")
                return False

            cache_preloader = loaders["cache_preloader"]
            return cache_preloader.preload_hospital_data_sync(hospital_id, hospital_data)

        except Exception as e:
            logger.error(f"Error preloading hospital data: {e}")
            return False


def create_redis_manager_replacement() -> RedisManagerCompatibility:
    """
    Create a drop-in replacement for the existing RedisManager.
    
    Returns:
        Compatibility layer that can replace existing RedisManager
    """
    migration_helper = VoiceAgentMigrationHelper()
    return migration_helper.create_compatibility_layer()


# For easy importing in voice_agent
def get_shared_redis_manager():
    """Get shared Redis manager for voice_agent."""
    return create_redis_manager_replacement()
