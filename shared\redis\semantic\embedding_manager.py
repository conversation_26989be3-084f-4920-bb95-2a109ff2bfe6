"""
Embedding Manager for Semantic Processing

Manages embedding generation and similarity calculations for both:
- IndicBERT embeddings for voice agent (Indian languages)
- Multilingual embeddings for WhatsApp agent (English + regional languages)
"""

import logging
import threading
import time
from typing import Optional, List, Dict, Any, Tuple, Union
import numpy as np
from functools import lru_cache

logger = logging.getLogger(__name__)


class EmbeddingManager:
    """
    Manages embedding generation and similarity calculations.
    Supports both IndicBERT and multilingual models.
    """
    
    def __init__(self):
        """Initialize embedding manager."""
        self._lock = threading.Lock()
        self._indic_bert_model = None
        self._multilingual_model = None
        self._model_cache = {}
        self._embedding_cache = {}
        
        # Statistics
        self._stats = {
            "embeddings_generated": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "similarity_calculations": 0,
            "total_time": 0.0
        }
    
    def _load_indic_bert_model(self):
        """Load IndicBERT model for voice agent."""
        if self._indic_bert_model is not None:
            return self._indic_bert_model
        
        try:
            # Import here to avoid dependency issues if not needed
            from transformers import AlbertModel, AlbertTokenizer
            import torch
            
            model_name = "ai4bharat/indic-bert"
            
            logger.info(f"Loading IndicBERT model: {model_name}")
            
            # Load tokenizer and model
            tokenizer = AlbertTokenizer.from_pretrained(model_name)
            model = AlbertModel.from_pretrained(model_name)
            
            # Set to evaluation mode
            model.eval()
            
            self._indic_bert_model = {
                "tokenizer": tokenizer,
                "model": model,
                "device": "cuda" if torch.cuda.is_available() else "cpu"
            }
            
            # Move model to appropriate device
            model.to(self._indic_bert_model["device"])
            
            logger.info(f"IndicBERT model loaded successfully on {self._indic_bert_model['device']}")
            return self._indic_bert_model
            
        except ImportError as e:
            logger.warning(f"IndicBERT dependencies not available: {e}")
            return None
        except Exception as e:
            logger.error(f"Failed to load IndicBERT model: {e}")
            return None
    
    def _load_multilingual_model(self):
        """Load multilingual model for WhatsApp agent."""
        if self._multilingual_model is not None:
            return self._multilingual_model
        
        try:
            # Import here to avoid dependency issues if not needed
            from sentence_transformers import SentenceTransformer
            
            model_name = "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
            
            logger.info(f"Loading multilingual model: {model_name}")
            
            model = SentenceTransformer(model_name)
            
            self._multilingual_model = {
                "model": model,
                "name": model_name
            }
            
            logger.info("Multilingual model loaded successfully")
            return self._multilingual_model
            
        except ImportError as e:
            logger.warning(f"Multilingual model dependencies not available: {e}")
            return None
        except Exception as e:
            logger.error(f"Failed to load multilingual model: {e}")
            return None
    
    @lru_cache(maxsize=1000)
    def _get_cached_embedding(self, text: str, model_type: str) -> Optional[np.ndarray]:
        """Get cached embedding if available."""
        cache_key = f"{model_type}:{hash(text)}"
        return self._embedding_cache.get(cache_key)
    
    def _cache_embedding(self, text: str, model_type: str, embedding: np.ndarray):
        """Cache embedding for future use."""
        cache_key = f"{model_type}:{hash(text)}"
        self._embedding_cache[cache_key] = embedding
        
        # Limit cache size
        if len(self._embedding_cache) > 10000:
            # Remove oldest 20% of entries
            keys_to_remove = list(self._embedding_cache.keys())[:2000]
            for key in keys_to_remove:
                del self._embedding_cache[key]
    
    def generate_indic_bert_embedding(self, text: str) -> Optional[np.ndarray]:
        """
        Generate IndicBERT embedding for voice agent.
        
        Args:
            text: Input text in any supported Indian language
            
        Returns:
            Embedding vector or None if generation failed
        """
        start_time = time.time()
        
        try:
            # Check cache first
            cached = self._get_cached_embedding(text, "indic_bert")
            if cached is not None:
                self._stats["cache_hits"] += 1
                return cached
            
            self._stats["cache_misses"] += 1
            
            # Load model if needed
            model_info = self._load_indic_bert_model()
            if not model_info:
                return None
            
            import torch
            
            tokenizer = model_info["tokenizer"]
            model = model_info["model"]
            device = model_info["device"]
            
            # Tokenize input
            inputs = tokenizer(
                text,
                return_tensors="pt",
                padding=True,
                truncation=True,
                max_length=512
            )
            
            # Move inputs to device
            inputs = {k: v.to(device) for k, v in inputs.items()}
            
            # Generate embedding
            with torch.no_grad():
                outputs = model(**inputs)
                # Use pooled output (CLS token representation)
                embedding = outputs.pooler_output.cpu().numpy().flatten()
            
            # Cache the embedding
            self._cache_embedding(text, "indic_bert", embedding)
            
            self._stats["embeddings_generated"] += 1
            self._stats["total_time"] += time.time() - start_time
            
            return embedding
            
        except Exception as e:
            logger.error(f"Failed to generate IndicBERT embedding: {e}")
            return None
    
    def generate_multilingual_embedding(self, text: str) -> Optional[np.ndarray]:
        """
        Generate multilingual embedding for WhatsApp agent.
        
        Args:
            text: Input text in English or regional language (in English script)
            
        Returns:
            Embedding vector or None if generation failed
        """
        start_time = time.time()
        
        try:
            # Check cache first
            cached = self._get_cached_embedding(text, "multilingual")
            if cached is not None:
                self._stats["cache_hits"] += 1
                return cached
            
            self._stats["cache_misses"] += 1
            
            # Load model if needed
            model_info = self._load_multilingual_model()
            if not model_info:
                return None
            
            model = model_info["model"]
            
            # Generate embedding
            embedding = model.encode(text, convert_to_numpy=True)
            
            # Cache the embedding
            self._cache_embedding(text, "multilingual", embedding)
            
            self._stats["embeddings_generated"] += 1
            self._stats["total_time"] += time.time() - start_time
            
            return embedding
            
        except Exception as e:
            logger.error(f"Failed to generate multilingual embedding: {e}")
            return None
    
    def calculate_similarity(self, embedding1: np.ndarray, embedding2: np.ndarray) -> float:
        """
        Calculate cosine similarity between two embeddings.
        
        Args:
            embedding1: First embedding vector
            embedding2: Second embedding vector
            
        Returns:
            Similarity score between 0 and 1
        """
        try:
            self._stats["similarity_calculations"] += 1
            
            # Normalize embeddings
            norm1 = np.linalg.norm(embedding1)
            norm2 = np.linalg.norm(embedding2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            # Calculate cosine similarity
            similarity = np.dot(embedding1, embedding2) / (norm1 * norm2)
            
            # Ensure result is between 0 and 1
            return max(0.0, min(1.0, (similarity + 1) / 2))
            
        except Exception as e:
            logger.error(f"Failed to calculate similarity: {e}")
            return 0.0
    
    def find_most_similar(self, query_embedding: np.ndarray, 
                         candidate_embeddings: List[Tuple[str, np.ndarray]], 
                         threshold: float = 0.8) -> List[Tuple[str, float]]:
        """
        Find most similar embeddings from candidates.
        
        Args:
            query_embedding: Query embedding vector
            candidate_embeddings: List of (id, embedding) tuples
            threshold: Minimum similarity threshold
            
        Returns:
            List of (id, similarity) tuples sorted by similarity (descending)
        """
        try:
            results = []
            
            for candidate_id, candidate_embedding in candidate_embeddings:
                similarity = self.calculate_similarity(query_embedding, candidate_embedding)
                
                if similarity >= threshold:
                    results.append((candidate_id, similarity))
            
            # Sort by similarity (descending)
            results.sort(key=lambda x: x[1], reverse=True)
            
            return results
            
        except Exception as e:
            logger.error(f"Failed to find similar embeddings: {e}")
            return []
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get embedding manager statistics.
        
        Returns:
            Dict with statistics
        """
        stats = self._stats.copy()
        
        if stats["embeddings_generated"] > 0:
            stats["average_generation_time_ms"] = (stats["total_time"] / stats["embeddings_generated"]) * 1000
        else:
            stats["average_generation_time_ms"] = 0
        
        if stats["cache_hits"] + stats["cache_misses"] > 0:
            stats["cache_hit_rate"] = stats["cache_hits"] / (stats["cache_hits"] + stats["cache_misses"])
        else:
            stats["cache_hit_rate"] = 0
        
        stats["models_loaded"] = {
            "indic_bert": self._indic_bert_model is not None,
            "multilingual": self._multilingual_model is not None
        }
        
        stats["cache_size"] = len(self._embedding_cache)
        
        return stats
    
    def clear_cache(self):
        """Clear embedding cache."""
        self._embedding_cache.clear()
        logger.info("Embedding cache cleared")
    
    def reset_stats(self):
        """Reset statistics."""
        self._stats = {
            "embeddings_generated": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "similarity_calculations": 0,
            "total_time": 0.0
        }


# Global embedding manager instance
_embedding_manager: Optional[EmbeddingManager] = None
_manager_lock = threading.Lock()


def get_embedding_manager() -> EmbeddingManager:
    """
    Get global embedding manager instance.
    
    Returns:
        EmbeddingManager instance
    """
    global _embedding_manager
    
    with _manager_lock:
        if _embedding_manager is None:
            _embedding_manager = EmbeddingManager()
            logger.info("Initialized global embedding manager")
    
    return _embedding_manager
