import { Save, <PERSON>, <PERSON><PERSON><PERSON>riangle, CheckCircle } from 'react-feather';
import Link from 'next/link';
import { useState, useCallback } from 'react';

export default function HospitalSettings({
  hospitalSettings,
  setHospitalSettings,
  onSave,
  loading
}) {
  const [validationErrors, setValidationErrors] = useState({});
  const [validationWarnings, setValidationWarnings] = useState({});

  // Production-ready phone number validation
  const validatePhoneNumber = useCallback((phone) => {
    const errors = [];
    const warnings = [];

    if (!phone || phone.trim() === '') {
      errors.push('Phone number is required');
      return { isValid: false, errors, warnings, sanitized: '' };
    }

    // Remove all non-phone characters except allowed ones
    const sanitized = phone.replace(/[^+0-9\s\-().]/g, '');

    // Check minimum length (considering international format)
    if (sanitized.replace(/[^0-9]/g, '').length < 7) {
      errors.push('Phone number must contain at least 7 digits');
    }

    // Check maximum length
    if (sanitized.replace(/[^0-9]/g, '').length > 15) {
      errors.push('Phone number cannot exceed 15 digits');
    }

    // Validate format patterns
    const phonePatterns = [
      /^\+?[1-9]\d{6,14}$/, // International format
      /^\+?[1-9][\d\s\-().]{6,18}$/, // With formatting characters
      /^[0-9]{10}$/, // Simple 10-digit format
      /^\([0-9]{3}\)\s?[0-9]{3}-[0-9]{4}$/, // US format (xxx) xxx-xxxx
      /^[0-9]{3}-[0-9]{3}-[0-9]{4}$/, // US format xxx-xxx-xxxx
    ];

    const cleanPhone = sanitized.replace(/[\s\-().]/g, '');
    const hasValidFormat = phonePatterns.some(pattern =>
      pattern.test(sanitized) || pattern.test(cleanPhone)
    );

    if (!hasValidFormat) {
      errors.push('Please enter a valid phone number format');
    }

    // Check for suspicious patterns
    if (/^(\d)\1{6,}$/.test(cleanPhone)) {
      warnings.push('Phone number appears to have repeated digits');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      sanitized
    };
  }, []);

  // Production-ready reminder template validation
  const validateReminderTemplate = useCallback((template) => {
    const errors = [];
    const warnings = [];

    if (!template || template.trim() === '') {
      errors.push('Reminder template is required');
      return { isValid: false, errors, warnings, sanitized: '' };
    }

    // Define allowed placeholders
    const allowedPlaceholders = [
      '{patient_name}',
      '{doctor_name}',
      '{appointment_date}',
      '{appointment_time}',
      '{hospital_name}',
      '{hospital_phone}',
      '{hospital_address}',
      '{hospital_email}'
    ];

    // Sanitize potentially harmful content
    let sanitized = template
      .replace(/[<>]/g, '') // Remove HTML tags
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .trim();

    // Check length constraints
    if (sanitized.length > 500) {
      errors.push('Template cannot exceed 500 characters');
      sanitized = sanitized.substring(0, 500);
    }

    if (sanitized.length < 20) {
      warnings.push('Template seems very short, consider adding more details');
    }

    // Validate placeholders
    const placeholderRegex = /\{[^}]+\}/g;
    const foundPlaceholders = sanitized.match(placeholderRegex) || [];
    const unknownPlaceholders = foundPlaceholders.filter(p => !allowedPlaceholders.includes(p));

    if (unknownPlaceholders.length > 0) {
      warnings.push(`Unknown placeholders found: ${unknownPlaceholders.join(', ')}`);
    }

    // Check for required placeholders
    const requiredPlaceholders = ['{patient_name}', '{appointment_date}', '{appointment_time}'];
    const missingRequired = requiredPlaceholders.filter(p => !sanitized.includes(p));

    if (missingRequired.length > 0) {
      warnings.push(`Consider including: ${missingRequired.join(', ')}`);
    }

    // Check for balanced braces
    const openBraces = (sanitized.match(/\{/g) || []).length;
    const closeBraces = (sanitized.match(/\}/g) || []).length;

    if (openBraces !== closeBraces) {
      errors.push('Unbalanced braces in template');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      sanitized
    };
  }, []);

  // Handle phone number change with validation
  const handlePhoneChange = useCallback((e) => {
    const value = e.target.value;
    const validation = validatePhoneNumber(value);

    // Update validation state
    setValidationErrors(prev => ({
      ...prev,
      phone: validation.errors
    }));

    setValidationWarnings(prev => ({
      ...prev,
      phone: validation.warnings
    }));

    // Update hospital settings with sanitized value
    setHospitalSettings(prev => ({
      ...prev,
      phone: validation.sanitized
    }));
  }, [validatePhoneNumber, setHospitalSettings]);

  // Handle reminder template change with validation
  const handleReminderTemplateChange = useCallback((e) => {
    const value = e.target.value;
    const validation = validateReminderTemplate(value);

    // Update validation state
    setValidationErrors(prev => ({
      ...prev,
      reminderTemplate: validation.errors
    }));

    setValidationWarnings(prev => ({
      ...prev,
      reminderTemplate: validation.warnings
    }));

    // Update hospital settings with sanitized value
    setHospitalSettings(prev => ({
      ...prev,
      reminderTemplate: validation.sanitized
    }));
  }, [validateReminderTemplate, setHospitalSettings]);

  // Enhanced save handler with validation
  const handleSave = useCallback(() => {
    // Validate all fields before saving
    const phoneValidation = validatePhoneNumber(hospitalSettings.phone || '');
    const templateValidation = validateReminderTemplate(hospitalSettings.reminderTemplate || '');

    // Update validation states
    setValidationErrors({
      phone: phoneValidation.errors,
      reminderTemplate: templateValidation.errors
    });

    setValidationWarnings({
      phone: phoneValidation.warnings,
      reminderTemplate: templateValidation.warnings
    });

    // Check if there are any validation errors
    const hasErrors = phoneValidation.errors.length > 0 || templateValidation.errors.length > 0;

    if (hasErrors) {
      // Scroll to first error field
      const firstErrorField = phoneValidation.errors.length > 0 ? 'hospital-phone' : 'reminder-template';
      document.getElementById(firstErrorField)?.scrollIntoView({ behavior: 'smooth', block: 'center' });
      return;
    }

    // Proceed with save if no errors
    onSave();
  }, [hospitalSettings.phone, hospitalSettings.reminderTemplate, validatePhoneNumber, validateReminderTemplate, onSave]);

  // Validation message component
  const ValidationMessage = ({ errors = [], warnings = [], fieldName }) => {
    if (errors.length === 0 && warnings.length === 0) return null;

    return (
      <div className="mt-2 space-y-1">
        {errors.map((error, index) => (
          <div key={`error-${index}`} className="flex items-center text-sm text-red-600">
            <AlertTriangle className="h-4 w-4 mr-1 flex-shrink-0" />
            <span>{error}</span>
          </div>
        ))}
        {warnings.map((warning, index) => (
          <div key={`warning-${index}`} className="flex items-center text-sm text-yellow-600">
            <AlertTriangle className="h-4 w-4 mr-1 flex-shrink-0" />
            <span>{warning}</span>
          </div>
        ))}
        {errors.length === 0 && warnings.length === 0 && (
          <div className="flex items-center text-sm text-green-600">
            <CheckCircle className="h-4 w-4 mr-1 flex-shrink-0" />
            <span>Valid {fieldName}</span>
          </div>
        )}
      </div>
    );
  };
  return (
    <div className="space-y-6">
      {/* Hospital Settings */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Hospital Settings</h2>
        </div>
        
        <div className="px-6 py-4">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <div>
              <label htmlFor="hospital-name" className="block text-sm font-medium text-gray-700">Hospital Name</label>
              <input
                type="text"
                id="hospital-name"
                value={hospitalSettings.name}
                onChange={(e) => setHospitalSettings({...hospitalSettings, name: e.target.value})}
                className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
            
            <div>
              <label htmlFor="hospital-address" className="block text-sm font-medium text-gray-700">Address</label>
              <input
                type="text"
                id="hospital-address"
                value={hospitalSettings.address}
                onChange={(e) => setHospitalSettings({...hospitalSettings, address: e.target.value})}
                className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
            
            <div>
              <label htmlFor="hospital-phone" className="block text-sm font-medium text-gray-700">Phone</label>
              <input
                type="tel"
                id="hospital-phone"
                value={hospitalSettings.phone || ''}
                onChange={handlePhoneChange}
                placeholder="+****************"
                pattern="[+]?[0-9\s\-().]+"
                title="Please enter a valid phone number"
                className={`mt-1 p-2 block w-full border rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                  validationErrors.phone?.length > 0
                    ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                    : validationWarnings.phone?.length > 0
                    ? 'border-yellow-300 focus:ring-yellow-500 focus:border-yellow-500'
                    : 'border-gray-300'
                }`}
              />
              <ValidationMessage
                errors={validationErrors.phone}
                warnings={validationWarnings.phone}
                fieldName="phone number"
              />
            </div>
            
            <div>
              <label htmlFor="hospital-email" className="block text-sm font-medium text-gray-700">Email</label>
              <input
                type="email"
                id="hospital-email"
                value={hospitalSettings.email}
                onChange={(e) => setHospitalSettings({...hospitalSettings, email: e.target.value})}
                className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
          </div>
          
          <div className="mt-6">
            <h3 className="text-md font-medium text-gray-700 mb-3">Notification Settings</h3>
            
            <div className="space-y-4">
              <div className="flex items-start">
                <div className="flex items-center h-5">
                  <input
                    id="enable-sms-reminders"
                    type="checkbox"
                    checked={hospitalSettings.enableSmsReminders}
                    onChange={(e) => setHospitalSettings({...hospitalSettings, enableSmsReminders: e.target.checked})}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </div>
                <div className="ml-3 text-sm">
                  <label htmlFor="enable-sms-reminders" className="font-medium text-gray-700">Enable SMS Reminders</label>
                  <p className="text-gray-500">Send SMS reminders to patients about their appointments</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="flex items-center h-5">
                  <input
                    id="enable-email-reminders"
                    type="checkbox"
                    checked={hospitalSettings.enableEmailReminders}
                    onChange={(e) => setHospitalSettings({...hospitalSettings, enableEmailReminders: e.target.checked})}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </div>
                <div className="ml-3 text-sm">
                  <label htmlFor="enable-email-reminders" className="font-medium text-gray-700">Enable Email Reminders</label>
                  <p className="text-gray-500">Send email reminders to patients about their appointments</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="flex items-center h-5">
                  <input
                    id="enable-test-result-notifications"
                    type="checkbox"
                    checked={hospitalSettings.enableTestResultNotifications}
                    onChange={(e) => setHospitalSettings({...hospitalSettings, enableTestResultNotifications: e.target.checked})}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </div>
                <div className="ml-3 text-sm">
                  <label htmlFor="enable-test-result-notifications" className="font-medium text-gray-700">Enable Test Result Notifications</label>
                  <p className="text-gray-500">Notify patients when their test results are available</p>
                </div>
              </div>
              
              <div>
                <label htmlFor="reminder-hours" className="block text-sm font-medium text-gray-700">Reminder Hours Before Appointment</label>
                <select
                  id="reminder-hours"
                  value={hospitalSettings.reminderHoursBeforeAppointment}
                  onChange={(e) => setHospitalSettings({...hospitalSettings, reminderHoursBeforeAppointment: parseInt(e.target.value)})}
                  className="mt-1 p-2 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
                  <option value="2">2 hours</option>
                  <option value="4">4 hours</option>
                  <option value="12">12 hours</option>
                  <option value="24">24 hours</option>
                  <option value="48">48 hours</option>
                </select>
              </div>
            </div>
          </div>
          
          <div className="mt-6 flex justify-end">
            <button
              onClick={handleSave}
              disabled={loading}
              className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 disabled:opacity-50"
            >
              <Save className="h-4 w-4 mr-2" />
              {loading ? 'Saving...' : 'Save Settings'}
            </button>
          </div>
        </div>
      </div>

      {/* Reminders Section */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Reminder System</h2>
        </div>
        
        <div className="px-6 py-4">
          <p className="text-gray-600 mb-4">
            Configure how reminders are sent to patients for appointments and medical tests.
          </p>
          
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800">Important Information</h3>
                <div className="mt-2 text-sm text-yellow-700">
                  <p>
                    Make sure you have configured your Twilio account details in the hospital settings to enable SMS reminders.
                    Email reminders require a valid SMTP server configuration.
                  </p>
                </div>
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Doctor Arrival Notifications</h3>
              
              <p className="text-sm text-gray-600 mb-4">
                When a doctor is running late, use this system to notify all scheduled patients for that doctor.
              </p>
              
              <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-blue-800">How it works</h3>
                    <div className="mt-2 text-sm text-blue-700">
                      <p>
                        When a doctor arrival status is updated as "late," the system will automatically
                        send notifications to all patients with appointments for that doctor on the current day.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="mt-4">
                <Link href="/dashboard" className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500">
                  <Bell className="mr-2 h-4 w-4" />
                  Go to Doctor Status Dashboard
                </Link>
              </div>
            </div>
            
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Appointment Reminders</h3>
              
              <p className="text-sm text-gray-600 mb-4">
                Automate sending reminders to patients about their upcoming appointments.
              </p>
              
              <div className="mt-4 space-y-3">
                <div className="flex items-start">
                  <div className="flex items-center h-5">
                    <input
                      id="auto-reminder"
                      type="checkbox"
                      checked={hospitalSettings.autoSendReminders}
                      onChange={(e) => setHospitalSettings({...hospitalSettings, autoSendReminders: e.target.checked})}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </div>
                  <div className="ml-3 text-sm">
                    <label htmlFor="auto-reminder" className="font-medium text-gray-700">Enable Automatic Reminders</label>
                    <p className="text-gray-500">System will automatically send reminders based on your settings</p>
                  </div>
                </div>
                
                <div className="flex flex-col space-y-2">
                  <label htmlFor="reminder-template" className="block text-sm font-medium text-gray-700">Reminder Message Template</label>
                  <textarea
                    id="reminder-template"
                    rows="4"
                    maxLength="500"
                    value={hospitalSettings.reminderTemplate || "Dear {patient_name}, this is a reminder about your appointment with Dr. {doctor_name} on {appointment_date} at {appointment_time}. Please arrive 15 minutes early. Contact us at {hospital_phone} if you need to reschedule."}
                    onChange={handleReminderTemplateChange}
                    placeholder="Enter your reminder message template..."
                    className={`p-2 block w-full border rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm resize-vertical ${
                      validationErrors.reminderTemplate?.length > 0
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                        : validationWarnings.reminderTemplate?.length > 0
                        ? 'border-yellow-300 focus:ring-yellow-500 focus:border-yellow-500'
                        : 'border-gray-300'
                    }`}
                  ></textarea>
                  <div className="flex justify-between items-start">
                    <p className="text-xs text-gray-500">
                      Available placeholders: {'{patient_name}'}, {'{doctor_name}'}, {'{appointment_date}'}, {'{appointment_time}'}, {'{hospital_name}'}, {'{hospital_phone}'}, {'{hospital_address}'}, {'{hospital_email}'}
                    </p>
                    <span className="text-xs text-gray-400">
                      {(hospitalSettings.reminderTemplate || '').length}/500
                    </span>
                  </div>
                  <ValidationMessage
                    errors={validationErrors.reminderTemplate}
                    warnings={validationWarnings.reminderTemplate}
                    fieldName="reminder template"
                  />
                </div>
              </div>
              
              <div className="mt-4">
                <button
                  onClick={handleSave}
                  disabled={loading}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 disabled:opacity-50"
                >
                  <Save className="mr-2 h-4 w-4" />
                  {loading ? 'Saving...' : 'Save Reminder Settings'}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
