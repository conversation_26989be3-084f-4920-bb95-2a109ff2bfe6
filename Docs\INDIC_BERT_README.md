# IndicBERT Integration for Voice Health Portal

This document provides information about the integration of AI4Bharat's IndicBERT model into the Voice Health Portal system to enhance multilingual support for Indian languages.

## Overview

IndicBERT is a multilingual ALBERT model pre-trained exclusively on 12 major Indian languages: Assamese, Bengali, English, Gujarati, Hindi, Kannada, Malayalam, Marathi, Oriya, Punjabi, Tamil, and Telugu. It is specifically designed to handle the linguistic diversity of India and provides better semantic understanding across these languages.

## Features

- **Enhanced Multilingual Support**: Better semantic understanding across 12 Indian languages
- **Improved Semantic Matching**: More accurate matching of user queries with cached responses
- **Cross-lingual Capabilities**: Ability to match queries across different Indian languages
- **Optimized Performance**: Balanced between accuracy and response time for real-time voice interactions

## Implementation Details

The IndicBERT integration is now part of the **shared Redis implementation** and consists of the following components:

1. **IndicBERT Cache** (`shared/redis/semantic/indic_bert_cache.py`): Semantic caching functionality using IndicBERT embeddings for enhanced Indian language support.

2. **Embedding Manager** (`shared/redis/semantic/embedding_manager.py`): Manages IndicBERT model loading and embedding generation with optimized performance.

3. **Semantic Processor** (`semantic_processor.py`): Enhanced with additional Indian language patterns and script detection capabilities.

4. **Semantic Integration** (`semantic_integration.py`): Updated to leverage shared IndicBERT implementation for better multilingual support.

## Usage

The system automatically uses IndicBERT for semantic processing through the shared Redis implementation. The voice agent now uses the enhanced shared Redis adapter which includes IndicBERT support.

Configuration is handled through the shared Redis configuration:

```bash
export REDIS_INDIC_BERT_ENABLED=true
export REDIS_SEMANTIC_SIMILARITY_THRESHOLD=0.8
```

## Testing

IndicBERT integration can be tested through the shared Redis implementation:

```bash
python shared/redis/comprehensive_integration_example.py
```

This script tests:
- IndicBERT semantic cache initialization
- Embedding generation for multiple Indian languages
- Semantic similarity between queries in different languages
- Cross-lingual semantic matching capabilities
- Integration with voice agent and WhatsApp agent

## Performance Considerations

IndicBERT is optimized for Indian languages but may have slightly higher computational requirements compared to the previous model. The implementation includes:

- Lazy loading of the model to minimize startup time
- Caching of embeddings to reduce computation for repeated queries
- Optimized similarity calculation for fast response times

## Dependencies

The following dependencies are required for IndicBERT integration:

- transformers>=4.52.4
- torch>=2.7.1
- indic-nlp-library>=0.92
- sentencepiece>=0.1.99  # Required for IndicBERT tokenization

These dependencies are included in the `requirements.txt` file.

## References

- [IndicBERT on HuggingFace](https://huggingface.co/ai4bharat/indic-bert)
- [AI4Bharat](https://ai4bharat.org/)
- [IndicBERT Paper](https://aclanthology.org/2020.findings-emnlp.445/)

## Known Issues and Limitations

- IndicBERT may require more memory compared to the previous model
- Initial loading time may be slightly longer
- GPU acceleration is recommended for production deployments with high traffic

## Support

For issues or questions related to the IndicBERT integration, please contact the Voice Health Portal development team.