import { withAuth } from '../../lib/auth';
import { searchFirestore } from '../../lib/firebase';
import { searchPostgres } from '../../lib/pg_utils';

export default withAuth(async (req, res) => {
  // Only allow GET method
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  try {
    // Get search term and hospital_id from query parameters
    const { term } = req.query;
    const hospitalId = req.user.hospital_id;

    if (!term || term.trim().length < 2) {
      return res.status(400).json({
        success: false,
        message: 'Search term must be at least 2 characters'
      });
    }

    // Search in Firestore (for doctors and staff)
    const firestoreResults = await searchFirestore(hospitalId, term);

    // Search in PostgreSQL (for appointments, test bookings, patients)
    const postgresResults = await searchPostgres(hospitalId, term);

    // Combine results
    const results = {
      doctors: firestoreResults.success ? firestoreResults.data.doctors : [],
      staff: firestoreResults.success ? firestoreResults.data.staff : [],
      patients: postgresResults.success ? postgresResults.data.patients : [],
      appointments: postgresResults.success ? postgresResults.data.appointments : [],
      testBookings: postgresResults.success ? postgresResults.data.testBookings : []
    };

    return res.status(200).json({
      success: true,
      data: results
    });
  } catch (error) {
    console.error('Search error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});