# Shared Components

This directory contains shared components for the Voice Health Portal project, including the unified LLM service and Redis implementation.

## Components

### 1. Shared LLM Service

The unified LLM service provides:

- **Unified LLM Interface**: Single service used by both voice_agent and whatsapp_agent
- **Function Calling**: Instead of fine-tuning, uses OpenAI's function calling for hospital operations
- **Async Support**: Built with AsyncOpenAI for better performance under high concurrency
- **Multilingual Support**: Supports Hindi (primary), Bengali, and English
- **Redis Caching**: Reduces costs and latency through intelligent caching
- **Production Ready**: Proper error handling, logging, and monitoring

### 2. Shared Redis Implementation

The unified Redis implementation provides:

- **Production-ready connection pooling** with automatic reconnection
- **IndicBERT semantic caching** for enhanced Indian language support in voice agent
- **Multilingual processing** for English and regional languages in WhatsApp agent
- **Comprehensive monitoring** and performance optimization
- **Cross-platform compatibility** (Python async/sync, Node.js)
- **Centralized configuration** management
- **Advanced TTL management** and cache invalidation

## Architecture

### LLM Service Architecture

```
shared/
├── __init__.py                 # Package initialization
├── llm_service.py             # Core LLM service with function calling
├── function_handlers.py       # Function implementations for LLM calls
├── llm_api_server.py         # HTTP API server for external access
├── config.py                 # Configuration management
├── start_llm_server.py       # Server startup script
├── requirements.txt          # Python dependencies
└── README.md                 # This file
```

### Redis Implementation Architecture

```
shared/
├── redis/
│   ├── __init__.py                 # Package initialization
│   ├── connection_manager.py       # Redis connection management with pooling
│   ├── config.py                  # Centralized Redis configuration
│   ├── base_operations.py         # Core Redis operations
│   ├── semantic/
│   │   ├── __init__.py
│   │   ├── indic_bert_cache.py    # IndicBERT integration for voice agent
│   │   ├── multilingual_cache.py  # Multilingual support for WhatsApp
│   │   └── embedding_manager.py   # Embedding generation and management
│   ├── cache/
│   │   ├── __init__.py
│   │   ├── monitor.py             # Cache monitoring and metrics
│   │   ├── optimizer.py           # Cache optimization strategies
│   │   └── ttl_manager.py         # TTL and expiration management
│   └── adapters/
│       ├── __init__.py
│       ├── python_adapter.py      # Python/asyncio adapter for voice_agent
│       └── nodejs_adapter.js      # Node.js adapter for whatsapp_agent
```

## Key Components

### LLM Service (`llm_service.py`)
- Core service using AsyncOpenAI client
- Function registration and calling system
- Usage tracking and cost optimization
- Multilingual prompt generation

### Function Handlers (`function_handlers.py`)
- Implementations of functions that LLM can call
- Hospital data retrieval (doctors, tests, schedules)
- Booking operations (appointments, tests)
- Integration with Firebase and PostgreSQL

### API Server (`llm_api_server.py`)
- FastAPI-based HTTP server
- RESTful endpoints for external access
- Used by WhatsApp agent (Node.js) to access Python LLM service
- Health checks and monitoring endpoints

## Setup

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Set Environment Variables**:
   ```bash
   export OPENAI_API_KEY="your-openai-api-key"
   export REDIS_URL="redis://localhost:6379"  # Optional
   ```

3. **Start the API Server**:
   ```bash
   python start_llm_server.py
   ```

## Usage

### From Voice Agent (Python)
```python
from shared.llm_integration import process_voice_input

context = {
    "hospital_id": "hospital_123",
    "language": "hi",
    "hospital_name": "City Hospital",
    "user_info": {"phone": "+91XXXXXXXXXX"}
}

result = await process_voice_input("I want to book an appointment", context)
```

### From WhatsApp Agent (Node.js)
```javascript
import { whatsappLLMIntegration } from './lib/llm_integration.js';

const result = await whatsappLLMIntegration.processMessage(message, hospitalContext);
```

### Direct HTTP API
```bash
curl -X POST http://localhost:8001/process \
  -H "Content-Type: application/json" \
  -d '{
    "message": "I want to book an appointment",
    "context": {
      "hospital_id": "hospital_123",
      "language": "hi"
    }
  }'
```

## Available Functions

The LLM can call these functions to perform hospital operations:

- `get_hospital_info(hospital_id)` - Get hospital information
- `get_available_doctors(hospital_id, date)` - Get available doctors
- `get_doctor_schedule(hospital_id, doctor_id, date)` - Get doctor schedule
- `get_available_tests(hospital_id, date)` - Get available tests
- `book_appointment(hospital_id, patient_name, phone, doctor_id, date, time)` - Book appointment
- `book_test(hospital_id, patient_name, phone, test_id, date, time)` - Book test

## Configuration

Configuration is managed through environment variables and the `config.py` file:

- **LLM Settings**: Model, temperature, max tokens
- **API Settings**: OpenAI API key, timeout, retries
- **Cache Settings**: Redis configuration, TTL
- **Language Settings**: Supported languages, default language

## Monitoring

### Health Check
```bash
curl http://localhost:8001/health
```

### Usage Statistics
```bash
curl http://localhost:8001/usage
```

### Available Functions
```bash
curl http://localhost:8001/functions
```

## Migration from Old System

The new system replaces:

- `voice_agent/llm_cost_optimizer.py` → `shared/llm_service.py`
- `voice_agent/llm_fine_tuning.py` → Function calling approach
- Fine-tuned models → GPT-4.1 nano with function calling

### Benefits of New Approach

1. **No Fine-tuning Required**: Faster deployment, easier updates
2. **Real-time Data**: Functions access live hospital data
3. **Better Accuracy**: GPT-4.1 nano is more capable than fine-tuned models
4. **Unified Service**: Single service for both applications
5. **Cost Effective**: Function calling is more cost-effective than fine-tuning

## Development

### Adding New Functions

1. Define function in `llm_service.py`:
```python
self.register_function(
    name="new_function",
    description="Description for LLM",
    parameters={...}  # JSON schema
)
```

2. Implement handler in `function_handlers.py`:
```python
async def new_function(self, param1: str) -> Dict[str, Any]:
    # Implementation
    return {"success": True, "result": "..."}
```

3. Register handler:
```python
def get_function_handlers():
    return {
        "new_function": hospital_handlers.new_function,
        # ... other handlers
    }
```

### Testing

The service includes comprehensive error handling and fallback mechanisms:

- LLM service failures fall back to cached responses
- Function call failures return error messages
- Network issues are handled gracefully

## Production Deployment

1. **Environment Variables**: Set all required environment variables
2. **Redis**: Configure Redis for caching (optional but recommended)
3. **Monitoring**: Use health check endpoint for monitoring
4. **Scaling**: Service is stateless and can be horizontally scaled
5. **Security**: Configure CORS and authentication as needed

## Troubleshooting

### Common Issues

1. **OpenAI API Key**: Ensure OPENAI_API_KEY is set correctly
2. **Redis Connection**: Check Redis URL and connectivity
3. **Function Handlers**: Ensure voice_agent functions are properly imported
4. **Port Conflicts**: Default port is 8001, change if needed

### Logs

Logs are written to both console and `llm_service.log` file. Check logs for detailed error information.

---

## Redis Implementation

### Overview

The shared Redis implementation provides a unified, production-ready caching solution for all applications in the Voice Health Portal system. It consolidates Redis functionality from voice_agent, whatsapp_agent, and staff_portal into a single, optimized implementation.

### Key Features

- **IndicBERT Integration**: Enhanced semantic caching for voice agent with support for 12 Indian languages
- **Multilingual Support**: Semantic processing for WhatsApp agent supporting English and regional languages in English script
- **Production-Ready**: Connection pooling, automatic reconnection, comprehensive error handling
- **Cross-Platform**: Python async/sync support for voice_agent, Node.js support for whatsapp_agent
- **Intelligent Optimization**: Automatic TTL management, cache optimization, and performance monitoring
- **Backward Compatibility**: Drop-in replacement for existing Redis implementations

### Quick Start

#### For Voice Agent (Python)

```python
# Simple migration - replace existing redis_manager
from shared.redis.migration_helper import get_shared_redis_manager
redis_manager = get_shared_redis_manager()

# All existing code continues to work
result = redis_manager.set("key", "value", expiry=3600)
data = redis_manager.get("key")

# Enhanced semantic caching
await redis_manager.cache_semantic_response_async(
    "डॉक्टर शर्मा कब आएंगे?",
    "डॉ. शर्मा शाम 5 बजे आएंगे।",
    "hospital_123",
    "doctor_schedule"
)

# Semantic search with IndicBERT
matches = await redis_manager.semantic_search_async(
    "शर्मा डॉक्टर का समय क्या है?",
    "hospital_123",
    "doctor_schedule"
)
```

#### For WhatsApp Agent (Node.js)

```javascript
import { getNodeJSAdapter } from 'shared/redis/adapters/nodejs_adapter.js';

const redis = getNodeJSAdapter();
await redis.initialize();

// Enhanced doctor availability caching
await redis.cacheDoctorAvailability(
  'hospital_123',
  'dr_sharma',
  'delayed',
  '15 minutes'
);

// Multilingual semantic search
const matches = await redis.searchSimilarResponses(
  'doctor sharma kab aayenge',
  'hospital_123',
  'doctor_availability'
);
```

### Installation

1. **Install Python Dependencies**:
   ```bash
   cd shared/redis
   pip install -r requirements.txt
   ```

2. **Set Environment Variables**:
   ```bash
   export REDIS_URL="redis://localhost:6379/0"
   export REDIS_INDIC_BERT_ENABLED="true"
   export REDIS_SEMANTIC_SIMILARITY_THRESHOLD="0.8"
   ```

3. **For IndicBERT Support** (Voice Agent):
   ```bash
   # Install additional dependencies for IndicBERT
   pip install transformers torch sentence-transformers
   ```

### Configuration

The shared Redis implementation uses environment variables for configuration:

#### Connection Settings
- `REDIS_URL`: Redis connection URL (default: redis://localhost:6379/0)
- `REDIS_MAX_CONNECTIONS`: Maximum connections in pool (default: 50)
- `REDIS_SOCKET_TIMEOUT`: Socket timeout in seconds (default: 5.0)

#### Cache Settings
- `REDIS_DEFAULT_TTL`: Default TTL in seconds (default: 3600)
- `REDIS_SEMANTIC_CACHE_TTL`: Semantic cache TTL (default: 86400)
- `REDIS_CALL_CONTEXT_TTL`: Call context TTL (default: 600)

#### Semantic Processing
- `REDIS_INDIC_BERT_ENABLED`: Enable IndicBERT for voice agent (default: true)
- `REDIS_SEMANTIC_SIMILARITY_THRESHOLD`: Similarity threshold (default: 0.8)
- `REDIS_EMBEDDING_DIMENSION`: Embedding dimension (default: 768)

#### Feature Flags
- `REDIS_VOICE_AGENT_ENABLED`: Enable voice agent features (default: true)
- `REDIS_WHATSAPP_AGENT_ENABLED`: Enable WhatsApp agent features (default: true)
- `REDIS_STAFF_PORTAL_ENABLED`: Enable staff portal features (default: true)

### Migration Guide

#### Voice Agent Migration

1. **Simple Replacement**:
   ```python
   # OLD
   from voice_agent.cache_manager import redis_manager

   # NEW
   from shared.redis.migration_helper import get_shared_redis_manager
   redis_manager = get_shared_redis_manager()
   ```

2. **Enhanced Features**:
   ```python
   # Use new semantic caching
   from shared.redis.adapters.python_adapter import get_python_adapter
   redis_adapter = get_python_adapter()

   # IndicBERT semantic matching
   await redis_adapter.cache_semantic_response_async(query, response, hospital_id)
   ```

3. **Data Migration**:
   ```python
   from shared.redis.migration_helper import VoiceAgentMigrationHelper

   migration_helper = VoiceAgentMigrationHelper()
   await migration_helper.migrate_existing_data(old_redis_client, hospital_ids)
   ```

#### WhatsApp Agent Migration

1. **Replace Redis Client**:
   ```javascript
   // OLD
   import { getRedisClient } from './lib/redis.js';

   // NEW
   import { getNodeJSAdapter } from 'shared/redis/adapters/nodejs_adapter.js';
   const redis = getNodeJSAdapter();
   ```

2. **Enhanced Features**:
   ```javascript
   // Multilingual doctor availability
   await redis.cacheDoctorAvailability(hospitalId, doctorId, status, estimatedTime);

   // Semantic search
   const matches = await redis.searchSimilarResponses(query, hospitalId, category);
   ```

### Monitoring and Optimization

#### Performance Monitoring

```python
from shared.redis.cache.monitor import get_cache_monitor

monitor = get_cache_monitor()
monitor.start_monitoring(interval=60)  # Monitor every minute

# Get performance report
report = monitor.get_performance_report()
print(report)
```

#### Cache Optimization

```python
from shared.redis.cache.optimizer import get_cache_optimizer

optimizer = get_cache_optimizer()
optimizer.start_auto_optimization(interval=300)  # Optimize every 5 minutes

# Manual optimization
result = optimizer.optimize_ttl_settings("hospital_123")
```

#### TTL Management

```python
from shared.redis.cache.ttl_manager import get_ttl_manager

ttl_manager = get_ttl_manager()
ttl_manager.start_auto_cleanup(interval=3600)  # Cleanup every hour

# Get TTL statistics
stats = ttl_manager.get_ttl_statistics("semantic:*")
```

### API Reference

#### Python Adapter

**Basic Operations**:
- `set(key, value, expiry=None)`: Set key-value pair
- `get(key)`: Get value
- `delete(*keys)`: Delete keys
- `exists(*keys)`: Check key existence

**Async Operations**:
- `set_async(key, value, expiry=None)`: Async set
- `get_async(key)`: Async get
- `delete_async(*keys)`: Async delete

**Semantic Caching**:
- `cache_semantic_response_async(query, response, hospital_id, category)`: Cache with IndicBERT
- `semantic_search_async(query, hospital_id, category, limit)`: Search similar responses
- `get_best_semantic_match_async(query, hospital_id, category)`: Get best match

**Call Context**:
- `save_call_context(call_id, context_data, ttl)`: Save call context
- `load_call_context(call_id)`: Load call context
- `delete_call_context(call_id)`: Delete call context

#### Node.js Adapter

**Basic Operations**:
- `set(key, value, ttl)`: Set key-value pair
- `get(key, asJson)`: Get value
- `delete(...keys)`: Delete keys
- `exists(...keys)`: Check key existence

**Doctor Availability**:
- `cacheDoctorAvailability(hospitalId, doctorId, status, estimatedTime)`: Cache availability
- `getDoctorAvailability(hospitalId, doctorId)`: Get availability status

**Semantic Search**:
- `searchSimilarResponses(query, hospitalId, category, limit)`: Search similar responses

### Production Deployment

#### Environment Setup

1. **Redis Server**: Ensure Redis server is running and accessible
2. **Memory**: Allocate sufficient memory for semantic embeddings
3. **Monitoring**: Set up monitoring for cache performance
4. **Backup**: Configure Redis persistence and backup

#### Performance Tuning

1. **Connection Pooling**: Adjust `REDIS_MAX_CONNECTIONS` based on load
2. **TTL Settings**: Optimize TTL values for your use case
3. **Similarity Threshold**: Tune `REDIS_SEMANTIC_SIMILARITY_THRESHOLD`
4. **Monitoring**: Enable monitoring and auto-optimization

#### Security

1. **Authentication**: Use Redis AUTH if available
2. **Network**: Secure Redis network access
3. **Encryption**: Use TLS for Redis connections in production
4. **Access Control**: Implement proper access controls

### Troubleshooting

#### Common Issues

1. **Connection Errors**: Check Redis URL and network connectivity
2. **Memory Issues**: Monitor Redis memory usage and adjust limits
3. **Performance**: Check cache hit rates and optimize TTL settings
4. **IndicBERT**: Ensure transformers and torch are installed correctly

#### Debug Mode

```python
import logging
logging.getLogger('shared.redis').setLevel(logging.DEBUG)
```

#### Health Checks

```python
# Python
redis_adapter = get_python_adapter()
is_healthy = redis_adapter.is_connected()

# Node.js
const redis = getNodeJSAdapter();
const isHealthy = await redis.testConnection();
```

### Examples

See the following files for comprehensive examples:
- `shared/redis/voice_agent_integration_example.py`: Voice agent integration examples
- `shared/redis/whatsapp_agent_integration_example.js`: WhatsApp agent integration examples

### Support

For issues and questions:
1. Check the troubleshooting section above
2. Review the example integration files
3. Check Redis server logs and application logs
4. Verify environment variable configuration
