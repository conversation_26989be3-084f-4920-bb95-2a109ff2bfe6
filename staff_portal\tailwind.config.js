/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx}",
    "./components/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // Primary color: Navy Blue
        'primary': {
          50: '#E6F0F9',
          100: '#CCE0F3',
          200: '#99C2E6',
          300: '#66A3D9',
          400: '#3385CC',
          500: '#003253', // Main primary color
          600: '#002B48',
          700: '#00243E',
          800: '#001C33',
          900: '#001529',
        },
        // Secondary color: Slate
        'secondary': {
          50: '#F8FAFC',
          100: '#F1F5F9',
          200: '#E2E8F0',
          300: '#CBD5E1',
          400: '#94A3B8',
          500: '#64748B',
          600: '#475569', // Main secondary color
          700: '#334155',
          800: '#1E293B', // Main text color
          900: '#0F172A',
        },
        // Accent color: Amber
        'accent': {
          50: '#FFFBEB',
          100: '#FEF3C7',
          200: '#FDE68A',
          300: '#FCD34D',
          400: '#FBBF24',
          500: '#F59E0B', // Main accent color
          600: '#D97706',
          700: '#B45309',
          800: '#92400E',
          900: '#78350F',
        },
      },
      fontFamily: {
        'sans': ['Inter', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif'],
      },
    },
  },
  plugins: [],
}