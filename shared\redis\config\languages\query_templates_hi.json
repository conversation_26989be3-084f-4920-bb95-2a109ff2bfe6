{"language": "hi", "language_name": "हिंदी", "doctor_name_queries": ["डॉक्टर {doctor_name} कब आते हैं", "डॉ. {doctor_name} का समय क्या है", "क्या डॉक्टर {doctor_name} उपलब्ध हैं", "मैं डॉक्टर {doctor_name} से कब मिल सकता हूं", "डॉ. {doctor_name} की टाइमिंग", "{doctor_name} डॉक्टर कब मरीज़ देखते हैं", "डॉक्टर {doctor_name} का शेड्यूल", "डॉ. {doctor_name} के परामर्श का समय", "डॉक्टर {doctor_name} अस्पताल में कब होते हैं", "डॉ. {doctor_name} से अपॉइंटमेंट कब मिल सकती है"], "doctor_specialty_queries": ["{specialty} डॉक्टर कब उपलब्ध हैं", "{specialty} का डॉक्टर कब आता है", "क्या {specialty} डॉक्टर उपलब्ध है", "मैं {specialty} डॉक्टर से कब मिल सकता हूं", "{specialty} डॉक्टर की टाइमिंग", "{specialty} डॉक्टर का शेड्यूल", "{specialty} की उपलब्धता", "{specialty} विशेषज्ञ की टाइमिंग", "{specialty} स्पेशलिस्ट कब आते हैं", "{specialty} के लिए परामर्श का समय"], "test_name_queries": ["{test_name} की कीमत क्या है", "{test_name} का खर्च कितना है", "{test_name} की फीस क्या है", "{test_name} का दाम", "{test_name} की लागत", "{test_name} के लिए कितना पैसा लगेगा", "{test_name} के चार्जेस", "{test_name} टेस्ट की कीमत", "{test_name} जांच का खर्च", "{test_name} परीक्षण की फीस"], "test_general_queries": ["खून की जांच की कीमत", "टेस्ट की लागत", "जांच की फीस", "लैब टेस्ट के चार्जेस", "डायग्नोस्टिक की लागत", "मेडिकल टेस्ट की कीमतें", "पैथोलॉजी के चार्जेस", "लेबोरेटरी टेस्ट की फीस", "रेडियोलॉजी टेस्ट की लागत", "इमेजिंग टेस्ट की कीमतें"], "common_queries": {"greeting": ["नमस्ते", "नमस्कार", "सुप्रभात", "शु<PERSON> दोपहर", "शुभ संध्या", "हैलो", "हाय", "आदा<PERSON>"], "help": ["मेरी मदद करें", "मुझे सहायता चाहिए", "क्या आप मदद कर सकते हैं", "आप क्या कर सकते हैं", "आप कैसे मदद कर सकते हैं", "मेरी सहायता करें", "मुझे सहारा चाहिए", "कृपया मदद करें"], "appointment": ["अपॉइंटमेंट बुक करें", "अपॉइंटमेंट का समय दें", "मुलाकात का समय", "अपॉइंटमेंट बुकिंग", "मुझे कब अपॉइंटमेंट मिल सकती है", "समय निर्धारित करें", "मुलाकात तय करें", "अपॉइंटमेंट सेट करें"], "hospital_info": ["अस्पताल का समय", "मिलने का समय", "अस्पताल का पता", "संपर्क नंबर", "अस्पताल की लोकेशन", "आपातकालीन संपर्क"]}, "specialties_hindi": {"Cardiology": "हृदय रोग विशेषज्ञ", "Neurology": "न्यूरोलॉजिस्ट", "Orthopedics": "हड्डी रोग विशेषज्ञ", "Pediatrics": "बाल रोग विशेषज्ञ", "Gynecology": "स्त्री रोग विशेषज्ञ", "Dermatology": "त्वचा रोग विशेषज्ञ", "ENT": "कान नाक गला विशेषज्ञ", "Ophthalmology": "नेत्र रोग विशेषज्ञ", "General Medicine": "सामान्य चिकित्सक", "Surgery": "सर्जन", "Psychiatry": "मानसिक रोग विशेषज्ञ", "Radiology": "रेडियोलॉजिस्ट", "Pathology": "पैथोलॉजिस्ट", "Anesthesiology": "एनेस्थीसियोलॉजिस्ट", "Emergency Medicine": "आपातकालीन चिकित्सक"}, "tests_hindi": {"Blood Test": "खून की जांच", "X-Ray": "एक्स-रे", "CT Scan": "सीटी स्कैन", "MRI": "एमआरआई स्कैन", "Ultrasound": "अल्ट्रासाउंड", "ECG": "ईसीजी", "Urine Test": "पेशाब की जांच", "Endoscopy": "एंडोस्कोपी", "Biopsy": "बायोप्सी", "Mammography": "मैमोग्राफी"}, "response_templates": {"doctor_schedule": "डॉ. {doctor_name} {schedule} उपलब्ध हैं। परामर्श शुल्क {fee} है।", "test_price": "{test_name} की कीमत {price} है। समय: {duration}।", "appointment_booking": "अपॉइंटमेंट बुक करने के लिए कृपया रिसेप्शन पर कॉल करें या अस्पताल आएं।", "general_help": "मैं डॉक्टर के शेड्यूल, टेस्ट की कीमतों और अपॉइंटमेंट की जानकारी में आपकी मदद कर सकता हूं।"}, "metadata": {"version": "2.0", "description": "Enhanced Hindi query templates for shared Redis voice agent", "total_templates": 78, "last_updated": "2024-01-15", "compatibility": "shared_redis_v1.0"}}