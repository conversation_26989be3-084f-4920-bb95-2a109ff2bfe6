import asyncio
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from collections import defaultdict, deque
import json
from .datetime_utils import (
    get_current_iso_timestamp_fresh, parse_datetime_robust
)

# Module-local logger
logger = logging.getLogger(__name__)

@dataclass
class ConnectionMetrics:
    """Metrics for individual WebSocket connections"""
    connection_id: str
    call_sid: Optional[str] = None
    hospital_id: Optional[str] = None
    connected_at: datetime = field(default_factory=datetime.now)
    last_activity: datetime = field(default_factory=datetime.now)
    messages_sent: int = 0
    messages_received: int = 0
    bytes_sent: int = 0
    bytes_received: int = 0
    errors: int = 0
    reconnections: int = 0
    avg_response_time: float = 0.0
    response_times: deque = field(default_factory=lambda: deque(maxlen=100))
    
    def update_sent(self, message_size: int, response_time: float = None):
        """Update sent message metrics"""
        self.messages_sent += 1
        self.bytes_sent += message_size
        self.last_activity = datetime.now()
        
        if response_time is not None:
            self.response_times.append(response_time)
            self.avg_response_time = sum(self.response_times) / len(self.response_times)
    
    def update_received(self, message_size: int):
        """Update received message metrics"""
        self.messages_received += 1
        self.bytes_received += message_size
        self.last_activity = datetime.now()
    
    def update_error(self):
        """Update error count"""
        self.errors += 1
        self.last_activity = datetime.now()
    
    def update_reconnection(self):
        """Update reconnection count"""
        self.reconnections += 1
        self.last_activity = datetime.now()
    
    def get_uptime_seconds(self) -> float:
        """Get connection uptime in seconds"""
        return (datetime.now() - self.connected_at).total_seconds()
    
    def get_idle_seconds(self) -> float:
        """Get seconds since last activity"""
        return (datetime.now() - self.last_activity).total_seconds()

@dataclass
class SystemMetrics:
    """System-wide WebSocket metrics"""
    start_time: datetime = field(default_factory=datetime.now)
    total_connections: int = 0
    active_connections: int = 0
    total_calls: int = 0
    active_calls: int = 0
    total_messages_sent: int = 0
    total_messages_received: int = 0
    total_bytes_sent: int = 0
    total_bytes_received: int = 0
    total_errors: int = 0
    total_reconnections: int = 0
    peak_connections: int = 0
    peak_calls: int = 0
    
    def get_uptime_seconds(self) -> float:
        """Get system uptime in seconds"""
        return (datetime.now() - self.start_time).total_seconds()

class WebSocketMetricsCollector:
    """
    Collects and manages WebSocket performance metrics.
    Provides real-time monitoring and historical data.
    """
    
    def __init__(self, history_retention_hours: int = 24):
        """
        Initialize metrics collector
        
        Args:
            history_retention_hours: How long to retain historical metrics
        """
        self.history_retention = timedelta(hours=history_retention_hours)
        
        # Current metrics
        self.system_metrics = SystemMetrics()
        self.connection_metrics: Dict[str, ConnectionMetrics] = {}
        
        # Historical data (time-series)
        self.historical_data: deque = deque(maxlen=1440)  # 24 hours at 1-minute intervals
        
        # Hospital-specific metrics
        self.hospital_metrics: Dict[str, Dict[str, Any]] = defaultdict(lambda: {
            "connections": 0,
            "calls": 0,
            "messages_sent": 0,
            "messages_received": 0,
            "errors": 0,
            "avg_response_time": 0.0
        })
        
        # Performance tracking
        self.response_time_buckets = {
            "0-100ms": 0,
            "100-500ms": 0,
            "500ms-1s": 0,
            "1s-5s": 0,
            "5s+": 0
        }
        
        # Error tracking
        self.error_types: Dict[str, int] = defaultdict(int)
        
        # Background task for historical data collection
        self._collection_task = None
        self._running = False
    
    async def start_collection(self, interval_seconds: int = 60):
        """
        Start background metrics collection
        
        Args:
            interval_seconds: Collection interval in seconds
        """
        if self._running:
            return
        
        self._running = True
        self._collection_task = asyncio.create_task(
            self._collection_loop(interval_seconds)
        )
        logger.info(f"Started metrics collection with {interval_seconds}s interval")
    
    async def stop_collection(self):
        """Stop background metrics collection"""
        self._running = False
        if self._collection_task:
            self._collection_task.cancel()
            try:
                await self._collection_task
            except asyncio.CancelledError:
                pass
        logger.info("Stopped metrics collection")
    
    async def _collection_loop(self, interval_seconds: int):
        """Background loop for collecting historical metrics"""
        while self._running:
            try:
                # Collect current snapshot
                snapshot = self._create_snapshot()
                self.historical_data.append(snapshot)
                
                # Clean up old connection metrics
                self._cleanup_old_connections()
                
                await asyncio.sleep(interval_seconds)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in metrics collection loop: {e}")
                await asyncio.sleep(interval_seconds)
    
    def _create_snapshot(self) -> Dict[str, Any]:
        """Create a snapshot of current metrics"""
        return {
            "timestamp": get_current_iso_timestamp_fresh(),
            "system": {
                "active_connections": self.system_metrics.active_connections,
                "active_calls": self.system_metrics.active_calls,
                "total_messages_sent": self.system_metrics.total_messages_sent,
                "total_messages_received": self.system_metrics.total_messages_received,
                "total_errors": self.system_metrics.total_errors,
                "uptime_seconds": self.system_metrics.get_uptime_seconds()
            },
            "hospitals": dict(self.hospital_metrics),
            "response_time_buckets": dict(self.response_time_buckets),
            "error_types": dict(self.error_types)
        }
    
    def _cleanup_old_connections(self):
        """Remove metrics for old disconnected connections"""
        cutoff_time = datetime.now() - timedelta(hours=1)
        
        to_remove = []
        for conn_id, metrics in self.connection_metrics.items():
            if metrics.last_activity < cutoff_time:
                to_remove.append(conn_id)
        
        for conn_id in to_remove:
            del self.connection_metrics[conn_id]
        
        if to_remove:
            logger.debug(f"Cleaned up {len(to_remove)} old connection metrics")
    
    def register_connection(self, connection_id: str, hospital_id: str = None, call_sid: str = None):
        """Register a new connection"""
        self.connection_metrics[connection_id] = ConnectionMetrics(
            connection_id=connection_id,
            hospital_id=hospital_id,
            call_sid=call_sid
        )
        
        self.system_metrics.total_connections += 1
        self.system_metrics.active_connections += 1
        self.system_metrics.peak_connections = max(
            self.system_metrics.peak_connections,
            self.system_metrics.active_connections
        )
        
        if hospital_id:
            self.hospital_metrics[hospital_id]["connections"] += 1
        
        if call_sid:
            self.system_metrics.total_calls += 1
            self.system_metrics.active_calls += 1
            self.system_metrics.peak_calls = max(
                self.system_metrics.peak_calls,
                self.system_metrics.active_calls
            )
            
            if hospital_id:
                self.hospital_metrics[hospital_id]["calls"] += 1
    
    def unregister_connection(self, connection_id: str):
        """Unregister a connection"""
        if connection_id in self.connection_metrics:
            metrics = self.connection_metrics[connection_id]
            
            self.system_metrics.active_connections = max(0, self.system_metrics.active_connections - 1)
            
            if metrics.call_sid:
                self.system_metrics.active_calls = max(0, self.system_metrics.active_calls - 1)
            
            # Don't delete immediately - keep for historical purposes
            # Will be cleaned up by _cleanup_old_connections
    
    def record_message_sent(self, connection_id: str, message_size: int, response_time: float = None):
        """Record a sent message"""
        if connection_id in self.connection_metrics:
            metrics = self.connection_metrics[connection_id]
            metrics.update_sent(message_size, response_time)
            
            self.system_metrics.total_messages_sent += 1
            self.system_metrics.total_bytes_sent += message_size
            
            if metrics.hospital_id:
                self.hospital_metrics[metrics.hospital_id]["messages_sent"] += 1
            
            # Update response time buckets
            if response_time is not None:
                self._update_response_time_bucket(response_time)
                
                # Update hospital average response time
                if metrics.hospital_id:
                    hospital_stats = self.hospital_metrics[metrics.hospital_id]
                    current_avg = hospital_stats["avg_response_time"]
                    message_count = hospital_stats["messages_sent"]
                    
                    # Calculate running average
                    new_avg = ((current_avg * (message_count - 1)) + response_time) / message_count
                    hospital_stats["avg_response_time"] = new_avg
    
    def record_message_received(self, connection_id: str, message_size: int):
        """Record a received message"""
        if connection_id in self.connection_metrics:
            metrics = self.connection_metrics[connection_id]
            metrics.update_received(message_size)
            
            self.system_metrics.total_messages_received += 1
            self.system_metrics.total_bytes_received += message_size
            
            if metrics.hospital_id:
                self.hospital_metrics[metrics.hospital_id]["messages_received"] += 1
    
    def record_error(self, connection_id: str, error_type: str):
        """Record an error"""
        if connection_id in self.connection_metrics:
            metrics = self.connection_metrics[connection_id]
            metrics.update_error()
            
            if metrics.hospital_id:
                self.hospital_metrics[metrics.hospital_id]["errors"] += 1
        
        self.system_metrics.total_errors += 1
        self.error_types[error_type] += 1
    
    def record_reconnection(self, connection_id: str):
        """Record a reconnection"""
        if connection_id in self.connection_metrics:
            metrics = self.connection_metrics[connection_id]
            metrics.update_reconnection()
        
        self.system_metrics.total_reconnections += 1
    
    def _update_response_time_bucket(self, response_time: float):
        """Update response time bucket counters"""
        if response_time < 0.1:
            self.response_time_buckets["0-100ms"] += 1
        elif response_time < 0.5:
            self.response_time_buckets["100-500ms"] += 1
        elif response_time < 1.0:
            self.response_time_buckets["500ms-1s"] += 1
        elif response_time < 5.0:
            self.response_time_buckets["1s-5s"] += 1
        else:
            self.response_time_buckets["5s+"] += 1
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """Get current system metrics"""
        return {
            "system": {
                "uptime_seconds": self.system_metrics.get_uptime_seconds(),
                "total_connections": self.system_metrics.total_connections,
                "active_connections": self.system_metrics.active_connections,
                "peak_connections": self.system_metrics.peak_connections,
                "total_calls": self.system_metrics.total_calls,
                "active_calls": self.system_metrics.active_calls,
                "peak_calls": self.system_metrics.peak_calls,
                "total_messages_sent": self.system_metrics.total_messages_sent,
                "total_messages_received": self.system_metrics.total_messages_received,
                "total_bytes_sent": self.system_metrics.total_bytes_sent,
                "total_bytes_received": self.system_metrics.total_bytes_received,
                "total_errors": self.system_metrics.total_errors,
                "total_reconnections": self.system_metrics.total_reconnections
            },
            "hospitals": dict(self.hospital_metrics),
            "response_time_distribution": dict(self.response_time_buckets),
            "error_types": dict(self.error_types),
            "connection_details": {
                conn_id: {
                    "call_sid": metrics.call_sid,
                    "hospital_id": metrics.hospital_id,
                    "uptime_seconds": metrics.get_uptime_seconds(),
                    "idle_seconds": metrics.get_idle_seconds(),
                    "messages_sent": metrics.messages_sent,
                    "messages_received": metrics.messages_received,
                    "errors": metrics.errors,
                    "reconnections": metrics.reconnections,
                    "avg_response_time": metrics.avg_response_time
                }
                for conn_id, metrics in self.connection_metrics.items()
            }
        }
    
    def get_historical_metrics(self, hours: int = 1) -> List[Dict[str, Any]]:
        """
        Get historical metrics for specified time period
        
        Args:
            hours: Number of hours of history to return
            
        Returns:
            List of historical metric snapshots
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        return [
            snapshot for snapshot in self.historical_data
            if parse_datetime_robust(snapshot["timestamp"]) and parse_datetime_robust(snapshot["timestamp"]) > cutoff_time
        ]
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary with key metrics"""
        total_messages = self.system_metrics.total_messages_sent + self.system_metrics.total_messages_received
        
        # Calculate average response time across all connections
        all_response_times = []
        for metrics in self.connection_metrics.values():
            all_response_times.extend(metrics.response_times)
        
        avg_response_time = sum(all_response_times) / len(all_response_times) if all_response_times else 0
        
        # Calculate error rate
        error_rate = (self.system_metrics.total_errors / total_messages * 100) if total_messages > 0 else 0
        
        return {
            "uptime_hours": self.system_metrics.get_uptime_seconds() / 3600,
            "active_connections": self.system_metrics.active_connections,
            "active_calls": self.system_metrics.active_calls,
            "total_messages": total_messages,
            "avg_response_time_ms": avg_response_time * 1000,
            "error_rate_percent": error_rate,
            "throughput_msg_per_sec": total_messages / self.system_metrics.get_uptime_seconds() if self.system_metrics.get_uptime_seconds() > 0 else 0,
            "hospitals_served": len(self.hospital_metrics),
            "peak_concurrent_connections": self.system_metrics.peak_connections,
            "peak_concurrent_calls": self.system_metrics.peak_calls
        }

# Global metrics collector instance
metrics_collector = WebSocketMetricsCollector()
