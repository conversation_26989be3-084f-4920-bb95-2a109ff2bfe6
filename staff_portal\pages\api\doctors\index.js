import { withAuth } from '../../../lib/auth';
import { getDoctorsByHospitalId } from '../../../lib/firebase';

export default withAuth(async (req, res) => {
  // GET - Fetch doctors for a hospital
  if (req.method === 'GET') {
    try {
      const { hospital_id } = req.query;
      
      // If hospital_id is not provided, use the authenticated user's hospital
      const hospitalId = hospital_id || req.user.hospital_id;
      
      // Verify hospital ID matches the authenticated user's hospital
      if (hospitalId !== req.user.hospital_id) {
        return res.status(403).json({
          success: false,
          message: 'You are not authorized to access doctors for this hospital'
        });
      }
      
      // Get doctors from Firestore ONLY
      const firestoreResult = await getDoctorsByHospitalId(hospitalId);
      
      let doctors = [];
      
      if (firestoreResult.success) {
        doctors = firestoreResult.data;
      } else {
        // If fetching from Firestore failed, return an error
        return res.status(500).json({
          success: false,
          message: firestoreResult.error || 'Failed to fetch doctors from Firebase.'
        });
      }
      
      return res.status(200).json({
        success: true,
        data: doctors
      });
    } catch (error) {
      console.error('Fetch doctors error:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
  
  // Method not allowed
  return res.status(405).json({
    success: false,
    message: 'Method not allowed'
  });
});