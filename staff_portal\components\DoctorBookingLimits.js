import React, { useState, useEffect } from 'react';
import { User, Calendar, Clock, RefreshCw, Save, AlertCircle, CheckCircle, XCircle } from 'react-feather';
import { BookingLimitToast } from './notifications/BookingLimitToast';

/**
 * Doctor Booking Limits Management Component
 * Allows admin and receptionist to manage daily booking limits for doctors
 */
export function DoctorBookingLimits({ hospitalId, userRole }) {
  const [doctors, setDoctors] = useState([]);
  const [selectedDoctor, setSelectedDoctor] = useState(null);
  const [dailyLimits, setDailyLimits] = useState({
    monday: 10,
    tuesday: 10,
    wednesday: 10,
    thursday: 10,
    friday: 10,
    saturday: 0,
    sunday: 0
  });
  const [originalLimits, setOriginalLimits] = useState({});
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [syncing, setSyncing] = useState(false);
  const [toast, setToast] = useState(null);
  const [redisStatus, setRedisStatus] = useState(null);

  const dayNames = {
    monday: 'Monday',
    tuesday: 'Tuesday', 
    wednesday: 'Wednesday',
    thursday: 'Thursday',
    friday: 'Friday',
    saturday: 'Saturday',
    sunday: 'Sunday'
  };

  const dayAbbreviations = {
    monday: 'Mon',
    tuesday: 'Tue',
    wednesday: 'Wed', 
    thursday: 'Thu',
    friday: 'Fri',
    saturday: 'Sat',
    sunday: 'Sun'
  };

  // Load doctors on component mount
  useEffect(() => {
    loadDoctors();
  }, [hospitalId]);

  // Load doctors list
  const loadDoctors = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/doctors/booking-limits');
      const result = await response.json();
      
      if (result.success) {
        setDoctors(result.data);
        setRedisStatus(result.redisConnected);
        
        // Auto-select first doctor if available
        if (result.data.length > 0 && !selectedDoctor) {
          selectDoctor(result.data[0]);
        }
      } else {
        showToast('error', null, null, null, null, result.message);
      }
    } catch (error) {
      console.error('Error loading doctors:', error);
      showToast('error', null, null, null, null, 'Failed to load doctors');
    } finally {
      setLoading(false);
    }
  };

  // Select a doctor and load their limits
  const selectDoctor = async (doctor) => {
    try {
      setSelectedDoctor(doctor);
      setLoading(true);
      
      const response = await fetch(`/api/doctors/${doctor.id}/booking-limits`);
      const result = await response.json();
      
      if (result.success) {
        const limits = result.data.dailyLimits;
        setDailyLimits(limits);
        setOriginalLimits(limits);
        
        // Update Redis status if available
        if (result.data.syncStatus) {
          setRedisStatus(result.data.syncStatus.isInSync);
        }
      } else {
        showToast('error', doctor.name, null, null, null, result.message);
      }
    } catch (error) {
      console.error('Error loading doctor limits:', error);
      showToast('error', doctor.name, null, null, null, 'Failed to load doctor limits');
    } finally {
      setLoading(false);
    }
  };

  // Update a specific day's limit
  const updateDayLimit = (day, value) => {
    const numValue = parseInt(value) || 0;
    if (numValue >= 0 && numValue <= 50) {
      setDailyLimits(prev => ({
        ...prev,
        [day]: numValue
      }));
    }
  };

  // Check if limits have changed
  const hasChanges = () => {
    return JSON.stringify(dailyLimits) !== JSON.stringify(originalLimits);
  };

  // Save booking limits
  const saveBookingLimits = async () => {
    if (!selectedDoctor) return;
    
    try {
      setSaving(true);
      setSyncing(true);
      
      const response = await fetch(`/api/doctors/${selectedDoctor.id}/booking-limits`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          dailyLimits,
          forceSync: true
        }),
      });
      
      const result = await response.json();
      
      if (result.success) {
        setOriginalLimits(dailyLimits);
        
        // Show success toast with details
        showToast(
          'success',
          result.data.doctorName,
          result.data.oldLimits,
          result.data.newLimits,
          result.data.syncStatus
        );
        
        // Update Redis status based on sync result
        setRedisStatus(result.data.syncStatus?.status === 'success');
        
      } else {
        showToast('error', selectedDoctor.name, null, null, null, result.message);
      }
    } catch (error) {
      console.error('Error saving booking limits:', error);
      showToast('error', selectedDoctor.name, null, null, null, 'Failed to save booking limits');
    } finally {
      setSaving(false);
      setSyncing(false);
    }
  };

  // Reset to original limits
  const resetLimits = () => {
    setDailyLimits(originalLimits);
  };

  // Apply preset patterns
  const applyPreset = (preset) => {
    const presets = {
      weekdays: { monday: 12, tuesday: 12, wednesday: 12, thursday: 12, friday: 12, saturday: 0, sunday: 0 },
      fullWeek: { monday: 10, tuesday: 10, wednesday: 10, thursday: 10, friday: 10, saturday: 8, sunday: 8 },
      reduced: { monday: 6, tuesday: 6, wednesday: 6, thursday: 6, friday: 6, saturday: 0, sunday: 0 },
      weekend: { monday: 0, tuesday: 0, wednesday: 0, thursday: 0, friday: 0, saturday: 12, sunday: 12 }
    };
    
    if (presets[preset]) {
      setDailyLimits(presets[preset]);
    }
  };

  // Show toast notification
  const showToast = (type, doctorName, oldLimits, newLimits, syncStatus, errorDetails = null) => {
    setToast({
      type,
      doctorName,
      oldLimits,
      newLimits,
      syncStatus,
      errorDetails,
      id: Date.now()
    });
  };

  // Close toast
  const closeToast = () => {
    setToast(null);
  };

  if (loading && !selectedDoctor) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="w-6 h-6 animate-spin mr-2" />
        <span>Loading doctors...</span>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Calendar className="w-5 h-5 text-blue-600 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">Booking Limits Management</h3>
          </div>
          
          {/* Redis Status Indicator */}
          <div className="flex items-center space-x-2">
            {redisStatus === true && (
              <div className="flex items-center text-green-600">
                <CheckCircle className="w-4 h-4 mr-1" />
                <span className="text-sm">Synced</span>
              </div>
            )}
            {redisStatus === false && (
              <div className="flex items-center text-red-600">
                <XCircle className="w-4 h-4 mr-1" />
                <span className="text-sm">Not Synced</span>
              </div>
            )}
            {redisStatus === null && (
              <div className="flex items-center text-gray-500">
                <AlertCircle className="w-4 h-4 mr-1" />
                <span className="text-sm">Unknown</span>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* Doctor Selection */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Select Doctor
          </label>
          <select
            value={selectedDoctor?.id || ''}
            onChange={(e) => {
              const doctor = doctors.find(d => d.id === e.target.value);
              if (doctor) selectDoctor(doctor);
            }}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={loading}
          >
            <option value="">Choose a doctor...</option>
            {doctors.map(doctor => (
              <option key={doctor.id} value={doctor.id}>
                {doctor.name} - {doctor.specialty}
              </option>
            ))}
          </select>
        </div>

        {selectedDoctor && (
          <>
            {/* Quick Presets */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Quick Presets
              </label>
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={() => applyPreset('weekdays')}
                  className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors"
                >
                  Weekdays Only (12)
                </button>
                <button
                  onClick={() => applyPreset('fullWeek')}
                  className="px-3 py-1 text-sm bg-green-100 text-green-700 rounded-md hover:bg-green-200 transition-colors"
                >
                  Full Week (10/8)
                </button>
                <button
                  onClick={() => applyPreset('reduced')}
                  className="px-3 py-1 text-sm bg-yellow-100 text-yellow-700 rounded-md hover:bg-yellow-200 transition-colors"
                >
                  Reduced (6)
                </button>
                <button
                  onClick={() => applyPreset('weekend')}
                  className="px-3 py-1 text-sm bg-purple-100 text-purple-700 rounded-md hover:bg-purple-200 transition-colors"
                >
                  Weekend Only (12)
                </button>
              </div>
            </div>

            {/* Daily Limits Grid */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Daily Booking Limits
              </label>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {Object.keys(dayNames).map(day => (
                  <div key={day} className="bg-gray-50 rounded-lg p-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {dayNames[day]}
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="50"
                      value={dailyLimits[day]}
                      onChange={(e) => updateDayLimit(day, e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-center font-medium"
                      disabled={loading || saving}
                    />
                    <div className="text-xs text-gray-500 mt-1 text-center">
                      {dailyLimits[day] === 0 ? 'Closed' : `${dailyLimits[day]} appointments`}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-between pt-4 border-t border-gray-200">
              <div className="flex items-center space-x-3">
                <button
                  onClick={resetLimits}
                  disabled={!hasChanges() || saving}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Reset
                </button>
                
                <button
                  onClick={() => selectDoctor(selectedDoctor)}
                  disabled={loading || saving}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <RefreshCw className={`w-4 h-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
                  Refresh
                </button>
              </div>

              <button
                onClick={saveBookingLimits}
                disabled={!hasChanges() || saving}
                className="px-6 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {saving ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    {syncing ? 'Syncing...' : 'Saving...'}
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Save Changes
                  </>
                )}
              </button>
            </div>
          </>
        )}
      </div>

      {/* Toast Notification */}
      {toast && (
        <BookingLimitToast
          type={toast.type}
          doctorName={toast.doctorName}
          oldLimits={toast.oldLimits}
          newLimits={toast.newLimits}
          syncStatus={toast.syncStatus}
          errorDetails={toast.errorDetails}
          onClose={closeToast}
        />
      )}
    </div>
  );
}

export default DoctorBookingLimits;
