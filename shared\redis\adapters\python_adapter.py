"""
Python Adapter for Shared Redis Implementation

Provides a high-level Python interface for voice_agent to use the shared Redis
implementation while maintaining compatibility with existing code.
"""

import logging
import threading
from typing import Dict, Any, List, Optional
from ..base_operations import RedisOperations
from ..semantic.indic_bert_cache import IndicBertCache
from ..semantic.multilingual_cache import MultilingualCache
from ..cache.monitor import get_cache_monitor
from ..cache.optimizer import get_cache_optimizer
from ..cache.ttl_manager import get_ttl_manager
from ..connection_manager import get_redis_connection
from ..config import get_redis_config

logger = logging.getLogger(__name__)


class PythonRedisAdapter:
    """
    High-level Python adapter for shared Redis implementation.
    Provides voice_agent-compatible interface with enhanced features.
    """
    
    def __init__(self):
        """Initialize Python Redis adapter."""
        self.config = get_redis_config()
        self.connection_manager = get_redis_connection()
        self.redis_ops = RedisOperations()
        
        # Semantic caches
        self.indic_bert_cache = IndicBertCache(self.redis_ops)
        self.multilingual_cache = MultilingualCache(self.redis_ops)
        
        # Management components
        self.monitor = get_cache_monitor()
        self.optimizer = get_cache_optimizer()
        self.ttl_manager = get_ttl_manager()
        
        # Track adapter usage
        self.monitor.record_operation("voice_agent", 0, None)
    
    # Basic Redis operations (compatible with existing voice_agent code)
    
    def set(self, key: str, value: Any, expiry: Optional[int] = None) -> bool:
        """
        Set a key-value pair (compatible with existing voice_agent code).
        
        Args:
            key: Redis key
            value: Value to store
            expiry: Optional expiry time in seconds
            
        Returns:
            bool: True if successful, False otherwise
        """
        ttl = expiry or self.ttl_manager.get_optimal_ttl(key)
        return self.redis_ops.set(key, value, ttl=ttl)
    
    def get(self, key: str) -> Any:
        """
        Get value from Redis (compatible with existing voice_agent code).
        
        Args:
            key: Redis key
            
        Returns:
            Value or None if not found
        """
        return self.redis_ops.get(key)
    
    def delete(self, *keys: str) -> int:
        """
        Delete keys from Redis.
        
        Args:
            keys: Redis keys to delete
            
        Returns:
            Number of keys deleted
        """
        return self.redis_ops.delete(*keys)
    
    def exists(self, *keys: str) -> int:
        """
        Check if keys exist.
        
        Args:
            keys: Redis keys to check
            
        Returns:
            Number of existing keys
        """
        return self.redis_ops.exists(*keys)
    
    def keys(self, pattern: str = "*") -> List[str]:
        """
        Get keys matching pattern.
        
        Args:
            pattern: Redis key pattern
            
        Returns:
            List of matching keys
        """
        return self.redis_ops.keys(pattern)
    
    # Async operations
    
    async def set_async(self, key: str, value: Any, expiry: Optional[int] = None) -> bool:
        """
        Asynchronously set a key-value pair.
        
        Args:
            key: Redis key
            value: Value to store
            expiry: Optional expiry time in seconds
            
        Returns:
            bool: True if successful, False otherwise
        """
        ttl = expiry or self.ttl_manager.get_optimal_ttl(key)
        return await self.redis_ops.set_async(key, value, ttl=ttl)
    
    async def get_async(self, key: str) -> Any:
        """
        Asynchronously get value from Redis.
        
        Args:
            key: Redis key
            
        Returns:
            Value or None if not found
        """
        return await self.redis_ops.get_async(key)
    
    async def delete_async(self, *keys: str) -> int:
        """
        Asynchronously delete keys from Redis.
        
        Args:
            keys: Redis keys to delete
            
        Returns:
            Number of keys deleted
        """
        return await self.redis_ops.delete_async(*keys)
    
    # Semantic caching operations
    
    async def cache_semantic_response_async(self, query: str, response: str, 
                                          hospital_id: str, category: str = "general") -> bool:
        """
        Cache response with IndicBERT semantic matching.
        
        Args:
            query: User query text
            response: Response to cache
            hospital_id: Hospital identifier
            category: Response category
            
        Returns:
            bool: True if cached successfully
        """
        return await self.indic_bert_cache.cache_response_async(query, response, hospital_id, category)
    
    async def semantic_search_async(self, query: str, hospital_id: str, 
                                  category: str = "general", limit: int = 5) -> List[Dict[str, Any]]:
        """
        Search for semantically similar cached responses.
        
        Args:
            query: User query text
            hospital_id: Hospital identifier
            category: Response category
            limit: Maximum number of results
            
        Returns:
            List of similar responses with similarity scores
        """
        return await self.indic_bert_cache.search_similar_async(query, hospital_id, category, limit)
    
    async def get_best_semantic_match_async(self, query: str, hospital_id: str, 
                                          category: str = "general") -> Optional[Dict[str, Any]]:
        """
        Get the best semantic match for a query.
        
        Args:
            query: User query text
            hospital_id: Hospital identifier
            category: Response category
            
        Returns:
            Best matching response or None
        """
        return await self.indic_bert_cache.get_best_match_async(query, hospital_id, category)
    
    # Call context management (compatible with existing voice_agent code)
    
    async def save_call_context(self, call_id: str, context_data: Dict[str, Any], 
                               ttl: Optional[int] = None) -> bool:
        """
        Save call context data.
        
        Args:
            call_id: Call identifier
            context_data: Context data to save
            ttl: Optional TTL (uses call_context_ttl if not provided)
            
        Returns:
            bool: True if saved successfully
        """
        key = f"call:context:{call_id}"
        ttl = ttl or self.config.call_context_ttl
        return await self.set_async(key, context_data, expiry=ttl)
    
    async def load_call_context(self, call_id: str) -> Optional[Dict[str, Any]]:
        """
        Load call context data.
        
        Args:
            call_id: Call identifier
            
        Returns:
            Context data or None if not found
        """
        key = f"call:context:{call_id}"
        return await self.get_async(key)
    
    async def delete_call_context(self, call_id: str) -> bool:
        """
        Delete call context data.
        
        Args:
            call_id: Call identifier
            
        Returns:
            bool: True if deleted successfully
        """
        key = f"call:context:{call_id}"
        return await self.delete_async(key) > 0
    
    # Hospital data caching
    
    async def cache_hospital_data_async(self, hospital_id: str, data_type: str, 
                                       data: Any, ttl: Optional[int] = None) -> bool:
        """
        Cache hospital-specific data.
        
        Args:
            hospital_id: Hospital identifier
            data_type: Type of data (doctors, tests, etc.)
            data: Data to cache
            ttl: Optional TTL
            
        Returns:
            bool: True if cached successfully
        """
        key = f"hospital:{hospital_id}:{data_type}"
        ttl = ttl or self.ttl_manager.get_optimal_ttl(key)
        return await self.set_async(key, data, expiry=ttl)
    
    async def get_cached_hospital_data_async(self, hospital_id: str, data_type: str) -> Any:
        """
        Get cached hospital data.
        
        Args:
            hospital_id: Hospital identifier
            data_type: Type of data
            
        Returns:
            Cached data or None if not found
        """
        key = f"hospital:{hospital_id}:{data_type}"
        return await self.get_async(key)
    
    # Doctor and test caching (compatible with existing voice_agent code)
    
    async def cache_doctor_info_async(self, query: str, doctor_data: Dict[str, Any], 
                                     hospital_id: str) -> bool:
        """
        Cache doctor information with semantic matching.
        
        Args:
            query: Query that led to this doctor info
            doctor_data: Doctor information
            hospital_id: Hospital identifier
            
        Returns:
            bool: True if cached successfully
        """
        response = f"Dr. {doctor_data.get('name', 'Unknown')} is available from {doctor_data.get('schedule', 'regular hours')}."
        return await self.cache_semantic_response_async(query, response, hospital_id, "doctors")
    
    async def cache_test_info_async(self, query: str, test_data: Dict[str, Any], 
                                   hospital_id: str) -> bool:
        """
        Cache test information with semantic matching.
        
        Args:
            query: Query that led to this test info
            test_data: Test information
            hospital_id: Hospital identifier
            
        Returns:
            bool: True if cached successfully
        """
        response = f"Test {test_data.get('name', 'Unknown')} is available. Cost: {test_data.get('cost', 'Contact hospital')}."
        return await self.cache_semantic_response_async(query, response, hospital_id, "tests")
    
    # Availability caching

    async def cache_availability_async(self, hospital_id: str, item_id: str,
                                      item_type: str, date: str, is_available: bool,
                                      ttl: Optional[int] = None) -> bool:
        """
        Cache availability information.

        Args:
            hospital_id: Hospital identifier
            item_id: Doctor or test ID
            item_type: 'doctor' or 'test'
            date: Date string
            is_available: Availability status
            ttl: Optional TTL

        Returns:
            bool: True if cached successfully
        """
        key = f"availability:{hospital_id}:{item_type}:{item_id}:{date}"
        ttl = ttl or 86400  # 24 hours default for availability
        return await self.set_async(key, is_available, expiry=ttl)

    async def get_availability_async(self, hospital_id: str, item_id: str,
                                    item_type: str, date: str) -> Optional[bool]:
        """
        Get availability information.

        Args:
            hospital_id: Hospital identifier
            item_id: Doctor or test ID
            item_type: 'doctor' or 'test'
            date: Date string

        Returns:
            Availability status or None if not cached
        """
        key = f"availability:{hospital_id}:{item_type}:{item_id}:{date}"
        value = await self.get_async(key)

        # Convert string representation to boolean
        if value is None:
            return None
        elif isinstance(value, bool):
            return value
        elif isinstance(value, str):
            return value.lower() == 'true'
        else:
            return bool(value)

    async def get_available_items_async(self, hospital_id: str, item_type: str, date: str) -> List[str]:
        """
        Get list of available doctor or test IDs for a specific date.

        Args:
            hospital_id: Hospital identifier
            item_type: 'doctor' or 'test'
            date: Date in YYYY-MM-DD format

        Returns:
            List of available item IDs
        """
        try:
            async_client = self.connection_manager.get_async_client()
            if not async_client:
                logger.error("Async Redis client not available")
                return []

            pattern = f"availability:{hospital_id}:{item_type}:*:{date}"
            keys = []

            # Use SCAN to get keys matching pattern (more efficient than KEYS)
            cursor = 0
            while True:
                cursor, batch_keys = await async_client.scan(cursor, match=pattern, count=100)
                keys.extend(batch_keys)
                if cursor == 0:
                    break

            available_items = []
            for key in keys:
                try:
                    # Decode key if it's bytes
                    if isinstance(key, bytes):
                        key = key.decode('utf-8')

                    is_available_raw = await async_client.get(key)

                    if is_available_raw:
                        # Use the proper deserialization method
                        is_available = self.redis_ops._deserialize_value(is_available_raw, as_json=True)

                        # Handle both boolean and string representations
                        if is_available is True or (isinstance(is_available, str) and is_available.lower() == 'true'):
                            # Extract item ID from key: availability:hospital:type:item_id:date
                            parts = key.split(':')
                            if len(parts) >= 5:  # availability:hospital:type:item_id:date
                                item_id = parts[3]
                                available_items.append(item_id)
                except Exception as e:
                    logger.error(f"Error processing availability key {key}: {e}")
                    continue

            return available_items
        except Exception as e:
            logger.error(f"Error getting available items: {e}")
            return []

    async def cache_multiple_availability_async(self, hospital_id: str, date: str,
                                              availability_data: List[Dict[str, Any]],
                                              ttl: Optional[int] = None) -> bool:
        """
        Cache availability for multiple doctors and tests efficiently using pipeline.

        Args:
            hospital_id: Hospital identifier
            date: Date in YYYY-MM-DD format
            availability_data: List of dicts with keys: id, type, available
            ttl: Time to live in seconds

        Returns:
            bool: Success or failure
        """
        try:
            async_client = self.connection_manager.get_async_client()
            if not async_client:
                logger.error("Async Redis client not available")
                return False

            ttl = ttl or 86400  # 24 hours default

            # Use pipeline for efficient batch operations
            pipe = async_client.pipeline()

            for item in availability_data:
                item_id = item.get('id')
                item_type = item.get('type')
                is_available = item.get('available', True)

                if item_id and item_type:
                    key = f"availability:{hospital_id}:{item_type}:{item_id}:{date}"
                    pipe.setex(key, ttl, self.redis_ops._serialize_value(is_available))

            await pipe.execute()
            logger.info(f"Cached availability for {len(availability_data)} items on {date}")
            return True
        except Exception as e:
            logger.error(f"Error caching multiple availability: {e}")
            return False

    # ==================== BOOKING LIMIT MANAGEMENT ====================

    async def cache_doctor_daily_limits_async(self, hospital_id: str, doctor_id: str,
                                            daily_limits: Dict[str, int], ttl: Optional[int] = None) -> bool:
        """
        Cache doctor's daily booking limits for fast lookup.

        Args:
            hospital_id: Hospital identifier
            doctor_id: Doctor identifier
            daily_limits: Dict with day names as keys and limits as values
            ttl: Time to live in seconds (default: 24 hours)

        Returns:
            bool: Success or failure
        """
        try:
            key = f"booking_limits:{hospital_id}:{doctor_id}:daily_limits"
            ttl = ttl or 86400  # 24 hours default
            return await self.set_async(key, daily_limits, expiry=ttl)
        except Exception as e:
            logger.error(f"Error caching doctor daily limits: {e}")
            return False

    async def get_doctor_daily_limits_async(self, hospital_id: str, doctor_id: str) -> Optional[Dict[str, int]]:
        """
        Get doctor's daily booking limits from cache.

        Args:
            hospital_id: Hospital identifier
            doctor_id: Doctor identifier

        Returns:
            Dict with daily limits or None if not cached
        """
        try:
            key = f"booking_limits:{hospital_id}:{doctor_id}:daily_limits"
            return await self.get_async(key)
        except Exception as e:
            logger.error(f"Error getting doctor daily limits: {e}")
            return None

    async def cache_next_available_date_async(self, hospital_id: str, doctor_id: str,
                                            next_date: str, ttl: Optional[int] = None) -> bool:
        """
        Cache next available date for a doctor.

        Args:
            hospital_id: Hospital identifier
            doctor_id: Doctor identifier
            next_date: Next available date in YYYY-MM-DD format
            ttl: Time to live in seconds (default: 24 hours)

        Returns:
            bool: Success or failure
        """
        try:
            key = f"booking_limits:{hospital_id}:{doctor_id}:next_available"
            ttl = ttl or 86400  # 24 hours default
            return await self.set_async(key, next_date, expiry=ttl)
        except Exception as e:
            logger.error(f"Error caching next available date: {e}")
            return False

    async def get_next_available_date_async(self, hospital_id: str, doctor_id: str) -> Optional[str]:
        """
        Get next available date for a doctor from cache.

        Args:
            hospital_id: Hospital identifier
            doctor_id: Doctor identifier

        Returns:
            Next available date string or None if not cached
        """
        try:
            key = f"booking_limits:{hospital_id}:{doctor_id}:next_available"
            return await self.get_async(key)
        except Exception as e:
            logger.error(f"Error getting next available date: {e}")
            return None

    # ==================== BOOKING COUNTER MANAGEMENT ====================

    def _get_seconds_until_end_of_day(self, date_str: str) -> int:
        """
        Calculate seconds until end of day for a given date.

        Args:
            date_str: Date in YYYY-MM-DD format

        Returns:
            Seconds until end of day
        """
        try:
            from datetime import datetime

            # Parse the date
            date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()

            # Get end of day (23:59:59)
            end_of_day = datetime.combine(date_obj, datetime.max.time())

            # Calculate seconds until end of day
            now = datetime.now()
            if now.date() == date_obj:
                # Same day - calculate actual seconds remaining
                seconds_remaining = int((end_of_day - now).total_seconds())
                return max(seconds_remaining, 3600)  # At least 1 hour
            else:
                # Different day - use 24 hours
                return 86400
        except Exception as e:
            logger.error(f"Error calculating seconds until end of day: {e}")
            return 86400  # Default to 24 hours

    async def increment_booking_counter_async(self, hospital_id: str, doctor_id: str, date: str) -> int:
        """
        Atomically increment booking counter for a doctor on a specific date.

        Args:
            hospital_id: Hospital identifier
            doctor_id: Doctor identifier
            date: Date in YYYY-MM-DD format

        Returns:
            New counter value or -1 on error
        """
        try:
            async_client = self.connection_manager.get_async_client()
            if not async_client:
                logger.error("Failed to get async Redis client")
                return -1

            key = f"booking_counter:{hospital_id}:{doctor_id}:{date}"

            # Use pipeline for atomic operations
            async with async_client.pipeline() as pipe:
                # Increment counter
                await pipe.incr(key)
                # Set expiry to end of day (if not already set)
                await pipe.expire(key, self._get_seconds_until_end_of_day(date))
                results = await pipe.execute()

            new_count = results[0] if results else -1
            logger.info(f"Incremented booking counter for {doctor_id} on {date}: {new_count}")
            return new_count

        except Exception as e:
            logger.error(f"Error incrementing booking counter: {e}")
            return -1

    async def get_booking_counter_async(self, hospital_id: str, doctor_id: str, date: str) -> int:
        """
        Get current booking counter for a doctor on a specific date.

        Args:
            hospital_id: Hospital identifier
            doctor_id: Doctor identifier
            date: Date in YYYY-MM-DD format

        Returns:
            Current counter value (0 if not found)
        """
        try:
            key = f"booking_counter:{hospital_id}:{doctor_id}:{date}"
            count = await self.get_async(key)
            return int(count) if count is not None else 0
        except Exception as e:
            logger.error(f"Error getting booking counter: {e}")
            return 0

    async def check_booking_availability_redis_async(self, hospital_id: str, doctor_id: str, date: str) -> Dict[str, Any]:
        """
        Check booking availability for a doctor on a specific date using Redis cache.

        Args:
            hospital_id: Hospital identifier
            doctor_id: Doctor identifier
            date: Date in YYYY-MM-DD format

        Returns:
            Dict with availability info: {
                'is_available': bool,
                'current_count': int,
                'limit': int,
                'next_available_date': str or None
            }
        """
        try:
            # Get current count and daily limits from Redis
            current_count = await self.get_booking_counter_async(hospital_id, doctor_id, date)
            daily_limits = await self.get_doctor_daily_limits_async(hospital_id, doctor_id)

            if not daily_limits:
                logger.warning(f"No daily limits cached for doctor {doctor_id}")
                return {
                    'is_available': True,  # Default to available if no limits cached
                    'current_count': current_count,
                    'limit': 10,  # Default limit
                    'next_available_date': None
                }

            # Get day name from date
            from datetime import datetime
            try:
                date_obj = datetime.strptime(date, '%Y-%m-%d')
                day_name = date_obj.strftime('%A')  # Full day name (Monday, Tuesday, etc.)
            except ValueError:
                logger.error(f"Invalid date format: {date}")
                return {
                    'is_available': False,
                    'current_count': current_count,
                    'limit': 0,
                    'next_available_date': None
                }

            # Get limit for this day
            limit = daily_limits.get(day_name, 0)

            # Check availability
            is_available = current_count < limit if limit > 0 else False

            # Get next available date if not available today
            next_available_date = None
            if not is_available:
                next_available_date = await self.get_next_available_date_async(hospital_id, doctor_id)

            return {
                'is_available': is_available,
                'current_count': current_count,
                'limit': limit,
                'next_available_date': next_available_date
            }

        except Exception as e:
            logger.error(f"Error checking booking availability: {e}")
            return {
                'is_available': False,
                'current_count': 0,
                'limit': 0,
                'next_available_date': None
            }
    
    # Cache management and monitoring
    
    async def clear_cache_async(self, pattern: str) -> int:
        """
        Clear cache entries matching pattern.
        
        Args:
            pattern: Redis key pattern
            
        Returns:
            Number of keys deleted
        """
        keys = await self.redis_ops.keys_async(pattern)
        if keys:
            return await self.delete_async(*keys)
        return 0
    
    async def get_cache_stats_async(self) -> Dict[str, Any]:
        """
        Get comprehensive cache statistics.
        
        Returns:
            Dict with cache statistics
        """
        return {
            "redis_operations": self.redis_ops.get_stats(),
            "indic_bert_cache": self.indic_bert_cache.get_stats(),
            "connection_manager": self.connection_manager.get_stats(),
            "monitor": self.monitor.get_application_metrics(),
            "config": self.config.to_dict()
        }
    
    def is_connected(self) -> bool:
        """
        Check if Redis connection is healthy.
        
        Returns:
            bool: True if connected and healthy
        """
        return self.connection_manager.is_healthy()
    
    def reconnect(self) -> bool:
        """
        Attempt to reconnect to Redis.

        Returns:
            bool: True if reconnection successful
        """
        return self.connection_manager.reconnect()

    # Sync wrapper functions for compatibility

    def get_cache_stats(self) -> Dict[str, Any]:
        """
        Get comprehensive cache usage statistics (sync version).

        Returns:
            Dict with cache statistics including:
            - total_keys: Actual number of keys in Redis
            - total_requests: Total cache operations (hits + misses)
            - hits: Number of cache hits
            - misses: Number of cache misses
            - hit_rate: Cache hit rate percentage
            - memory_used: Memory usage in human-readable format
            - keyspace_info: Per-database key information
        """
        try:
            import asyncio
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(self.get_cache_stats_async())
        except RuntimeError:
            # No event loop running, create a new one
            return asyncio.run(self.get_cache_stats_async())

    def clear_cache(self, pattern: str = "*") -> int:
        """
        Clear cache entries matching a pattern (sync version).

        Args:
            pattern: Redis key pattern (default: "*" for all keys)

        Returns:
            int: Number of keys deleted
        """
        try:
            import asyncio
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(self.clear_cache_async(pattern))
        except RuntimeError:
            return asyncio.run(self.clear_cache_async(pattern))

    def batched_scan_delete(self, pattern: str, batch_size: int = 1000,
                           scan_count: int = 100) -> int:
        """
        Perform batched SCAN and DELETE operations (sync version).

        Args:
            pattern: Redis key pattern to match
            batch_size: Number of keys to delete in each batch
            scan_count: Number of keys to scan per SCAN operation

        Returns:
            Total number of keys deleted
        """
        try:
            import asyncio
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(
                self.batched_scan_delete_async(pattern, batch_size, scan_count)
            )
        except RuntimeError:
            return asyncio.run(
                self.batched_scan_delete_async(pattern, batch_size, scan_count)
            )

    # Additional compatibility functions

    def save_call_context_sync(self, call_id: str, context_data: Dict[str, Any],
                              ttl: Optional[int] = None) -> bool:
        """Save call context data (sync version)."""
        try:
            import asyncio
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(
                self.save_call_context(call_id, context_data, ttl)
            )
        except RuntimeError:
            return asyncio.run(
                self.save_call_context(call_id, context_data, ttl)
            )

    def load_call_context_sync(self, call_id: str) -> Optional[Dict[str, Any]]:
        """Load call context data (sync version)."""
        try:
            import asyncio
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(self.load_call_context(call_id))
        except RuntimeError:
            return asyncio.run(self.load_call_context(call_id))

    def delete_call_context_sync(self, call_id: str) -> bool:
        """Delete call context data (sync version)."""
        try:
            import asyncio
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(self.delete_call_context(call_id))
        except RuntimeError:
            return asyncio.run(self.delete_call_context(call_id))
    
    # Preloading and optimization
    
    async def preload_hospital_data_async(self, hospital_id: str, 
                                         hospital_data: Optional[Dict[str, Any]] = None) -> bool:
        """
        Preload hospital data into cache.
        
        Args:
            hospital_id: Hospital identifier
            hospital_data: Optional hospital data to preload
            
        Returns:
            bool: True if preloading successful
        """
        return self.optimizer.preload_hospital_cache(hospital_id, hospital_data)
    
    def optimize_cache(self, hospital_id: str) -> Dict[str, Any]:
        """
        Optimize cache for a hospital.
        
        Args:
            hospital_id: Hospital identifier
            
        Returns:
            Dict with optimization results
        """
        return self.optimizer.optimize_ttl_settings(hospital_id)

    # ==================== SEMANTIC CACHING FOR DATA LOADERS ====================

    async def cache_semantic_response_async(self, query: str, response_data: Dict[str, Any],
                                          hospital_id: str, category: str, ttl: Optional[int] = None) -> bool:
        """
        Cache semantic response for voice agent queries.

        Args:
            query: User query text
            response_data: Response data to cache
            hospital_id: Hospital identifier
            category: Category of response (doctor_info, test_info, common)
            ttl: Time to live in seconds

        Returns:
            bool: Success or failure
        """
        try:
            # Create semantic cache key
            key = f"semantic:{hospital_id}:{category}:{self._normalize_query(query)}"
            ttl = ttl or 86400  # 24 hours default

            # Add metadata to response
            import time
            enhanced_response = {
                **response_data,
                "cached_at": time.time(),
                "hospital_id": hospital_id,
                "category": category,
                "query": query
            }

            success = await self.set_async(key, enhanced_response, ttl)
            if success:
                logger.debug(f"Cached semantic response for query: {query[:50]}...")

            return success

        except Exception as e:
            logger.error(f"Error caching semantic response: {e}")
            return False

    async def get_semantic_response_async(self, query: str, hospital_id: str,
                                        category: str = None) -> Optional[Dict[str, Any]]:
        """
        Get semantic response for voice agent queries.

        Args:
            query: User query text
            hospital_id: Hospital identifier
            category: Category of response (optional for broader search)

        Returns:
            Response data or None if not found
        """
        try:
            normalized_query = self._normalize_query(query)

            if category:
                # Direct lookup with category
                key = f"semantic:{hospital_id}:{category}:{normalized_query}"
                response = await self.get_async(key)
                if response:
                    return response

            # Fallback: search across all categories
            pattern = f"semantic:{hospital_id}:*:{normalized_query}"
            async_client = self.connection_manager.get_async_client()

            if not async_client:
                return None

            # Use SCAN to find matching keys
            cursor = 0
            while True:
                cursor, keys = await async_client.scan(cursor, match=pattern, count=10)

                for key in keys:
                    if isinstance(key, bytes):
                        key = key.decode('utf-8')

                    response = await self.get_async(key)
                    if response:
                        return response

                if cursor == 0:
                    break

            return None

        except Exception as e:
            logger.error(f"Error getting semantic response: {e}")
            return None

    def _normalize_query(self, query: str) -> str:
        """Normalize query for consistent caching."""
        import re

        # Convert to lowercase
        normalized = query.lower().strip()

        # Remove extra spaces
        normalized = re.sub(r'\s+', ' ', normalized)

        # Remove common punctuation
        normalized = re.sub(r'[.,!?;:]', '', normalized)

        # Create a hash for very long queries
        if len(normalized) > 100:
            import hashlib
            return hashlib.md5(normalized.encode()).hexdigest()

        return normalized

    async def clear_semantic_cache_async(self, hospital_id: str, category: str = None) -> bool:
        """
        Clear semantic cache for a hospital.

        Args:
            hospital_id: Hospital identifier
            category: Specific category to clear (optional)

        Returns:
            bool: Success or failure
        """
        try:
            if category:
                pattern = f"semantic:{hospital_id}:{category}:*"
            else:
                pattern = f"semantic:{hospital_id}:*"

            return await self.clear_cache_async(pattern)

        except Exception as e:
            logger.error(f"Error clearing semantic cache: {e}")
            return False

    # Statistics and monitoring

    def get_stats(self) -> Dict[str, Any]:
        """
        Get adapter statistics.

        Returns:
            Dictionary with adapter statistics
        """
        return {
            "operations": self.monitor.get_stats(),
            "cache_performance": self.optimizer.get_performance_metrics(),
            "ttl_optimization": self.ttl_manager.get_optimization_stats()
        }

    async def get_cache_stats_async(self) -> Dict[str, Any]:
        """
        Get comprehensive cache usage statistics (async version).

        Returns:
            Dict with cache statistics including:
            - total_keys: Actual number of keys in Redis
            - total_requests: Total cache operations (hits + misses)
            - hits: Number of cache hits
            - misses: Number of cache misses
            - hit_rate: Cache hit rate percentage
            - memory_used: Memory usage in human-readable format
            - keyspace_info: Per-database key information
        """
        try:
            async_client = self.connection_manager.get_async_client()
            if not async_client:
                return {
                    "success": False,
                    "message": "Async Redis client not available"
                }

            info = await async_client.info()

            # Get hit/miss statistics (these represent operation counts, not key counts)
            hits = info.get("keyspace_hits", 0)
            misses = info.get("keyspace_misses", 0)
            total_requests = hits + misses
            hit_rate = (hits / total_requests * 100) if total_requests > 0 else 0.0

            # Get actual key count from keyspace info
            total_keys = 0
            keyspace_info = {}

            # Parse keyspace information for each database
            for key, value in info.items():
                if key.startswith("db"):
                    try:
                        # Parse keyspace info format: "keys=X,expires=Y,avg_ttl=Z"
                        if isinstance(value, str):
                            # Parse string format
                            parts = value.split(',')
                            db_info = {}
                            for part in parts:
                                if '=' in part:
                                    k, v = part.split('=', 1)
                                    try:
                                        db_info[k.strip()] = int(v.strip())
                                    except ValueError:
                                        db_info[k.strip()] = v.strip()

                            db_keys = db_info.get("keys", 0)
                            total_keys += db_keys
                            keyspace_info[key] = db_info

                        elif isinstance(value, dict):
                            # Direct dict format (some Redis configurations)
                            db_keys = value.get("keys", 0)
                            total_keys += db_keys
                            keyspace_info[key] = value

                    except Exception as e:
                        logger.warning(f"Error parsing keyspace info for {key}: {e}")
                        continue

            return {
                "success": True,
                "total_keys": total_keys,
                "total_requests": total_requests,
                "hits": hits,
                "misses": misses,
                "hit_rate": round(hit_rate, 2),
                "memory_used": info.get("used_memory_human", "Unknown"),
                "memory_used_bytes": info.get("used_memory", 0),
                "connected_clients": info.get("connected_clients", 0),
                "keyspace_info": keyspace_info,
                "redis_version": info.get("redis_version", "Unknown"),
                "uptime_seconds": info.get("uptime_in_seconds", 0)
            }

        except Exception as e:
            logger.error(f"Error getting async cache stats: {e}")
            return {
                "success": False,
                "message": f"Failed to get cache stats: {str(e)}"
            }

    async def batched_scan_delete_async(self, pattern: str, batch_size: int = 1000,
                                       scan_count: int = 100) -> int:
        """
        Perform batched SCAN and DELETE operations for better performance with large datasets.

        This method prevents Redis blocking by:
        1. Using SCAN with count parameter to batch the scanning
        2. Deleting keys in batches to avoid memory issues
        3. Providing detailed logging for monitoring

        Args:
            pattern: Redis key pattern to match (e.g., "hospital_config:*")
            batch_size: Number of keys to delete in each batch (default: 1000)
            scan_count: Number of keys to scan per SCAN operation (default: 100)

        Returns:
            Total number of keys deleted
        """
        try:
            async_client = self.connection_manager.get_async_client()
            if not async_client:
                logger.error("Async Redis client not available for batched scan delete")
                return 0

            keys_to_delete = []
            total_deleted = 0

            logger.debug(f"Starting batched scan delete for pattern: {pattern}")

            # Batch the scan operation for better performance
            async for key in async_client.scan_iter(match=pattern, count=scan_count):
                keys_to_delete.append(key)

                # Delete in batches to avoid memory issues and Redis blocking
                if len(keys_to_delete) >= batch_size:
                    deleted_count = await async_client.delete(*keys_to_delete)
                    total_deleted += deleted_count
                    logger.debug(f"Deleted batch of {deleted_count} keys for pattern {pattern}")
                    keys_to_delete = []

            # Delete remaining keys
            if keys_to_delete:
                deleted_count = await async_client.delete(*keys_to_delete)
                total_deleted += deleted_count
                logger.debug(f"Deleted final batch of {deleted_count} keys for pattern {pattern}")

            logger.info(f"Batched scan delete completed: {total_deleted} keys deleted for pattern {pattern}")
            return total_deleted

        except Exception as e:
            logger.error(f"Error in batched scan delete for pattern {pattern}: {e}")
            return 0

    async def clear_cache_async(self, pattern: str = "*") -> int:
        """
        Clear cache entries matching a pattern using optimized batched SCAN.

        This method uses the optimized batched_scan_delete_async for better performance
        with large datasets and to prevent Redis blocking.

        Args:
            pattern: Redis key pattern (default: "*" for all keys)

        Returns:
            int: Number of keys deleted
        """
        # Use the optimized batched scan delete method
        return await self.batched_scan_delete_async(pattern)

    # Data preloading functionality

    async def preload_hospital_data_from_config_async(self, hospital_id: str, hospital_data: dict = None) -> bool:
        """
        Preload hospital data from provided configuration or Firebase.

        Args:
            hospital_id: Hospital identifier
            hospital_data: Optional dict containing doctors and tests data

        Returns:
            bool: Success or failure
        """
        try:
            from ..data_loaders.cache_preloader import CachePreloader
            preloader = CachePreloader(redis_adapter=self)
            return await preloader.preload_hospital_data_async(hospital_id, hospital_data)
        except ImportError as e:
            logger.error(f"Failed to import data loader: {e}")
            return False
        except Exception as e:
            logger.error(f"Error preloading hospital data from config: {e}")
            return False

    # Connection management

    def is_connected(self) -> bool:
        """Check if Redis connection is active."""
        try:
            client = self.connection_manager.get_sync_client()
            if not client:
                return False
            client.ping()
            return True
        except Exception:
            return False

    def reconnect(self) -> bool:
        """Attempt to reconnect to Redis if connection was lost."""
        try:
            # Re-initialize connection manager
            self.connection_manager = get_redis_connection()
            return self.is_connected()
        except Exception as e:
            logger.error(f"Error reconnecting to Redis: {e}")
            return False

    # WebSocket context management

    async def save_websocket_context(self, call_id: str, ws_data: Dict[str, Any],
                                    ttl: Optional[int] = None) -> bool:
        """
        Save WebSocket-specific context data.

        Args:
            call_id: Call identifier
            ws_data: WebSocket context data
            ttl: Optional TTL (uses call_context_ttl if not provided)

        Returns:
            bool: True if saved successfully
        """
        key = f"call:websocket:{call_id}"
        ttl = ttl or self.config.call_context_ttl
        return await self.set_async(key, ws_data, expiry=ttl)

    async def load_websocket_context(self, call_id: str) -> Optional[Dict[str, Any]]:
        """
        Load WebSocket-specific context data.

        Args:
            call_id: Call identifier

        Returns:
            WebSocket context data or None if not found
        """
        key = f"call:websocket:{call_id}"
        return await self.get_async(key)

    async def delete_websocket_context(self, call_id: str) -> bool:
        """
        Delete WebSocket-specific context data.

        Args:
            call_id: Call identifier

        Returns:
            bool: True if deleted successfully
        """
        key = f"call:websocket:{call_id}"
        return await self.delete_async(key) > 0

    # Utility functions for compatibility

    async def list_active_calls(self) -> List[str]:
        """
        List all active call IDs in the system using async Redis scan with batching.

        Returns:
            List of active call IDs
        """
        pattern = "call:context:*"
        call_ids = []

        try:
            async_client = self.connection_manager.get_async_client()
            if not async_client:
                logger.error("Async Redis client not available")
                return []

            # Use batched scan for better performance with large datasets
            async for key in async_client.scan_iter(match=pattern, count=100):
                # Extract call_id from key format: call:context:{call_id}
                if isinstance(key, bytes):
                    try:
                        key = key.decode('utf-8')
                    except UnicodeDecodeError:
                        logger.warning(f"Failed to decode call context key: {key!r}. Skipping.")
                        continue

                call_id = key.split(":")[-1]
                call_ids.append(call_id)

            logger.debug(f"Found {len(call_ids)} active calls using batched scan")
            return call_ids
        except Exception as e:
            logger.error(f"Error listing active calls from Redis: {e}")
            return []


# Global Python adapter instance
_python_adapter: Optional[PythonRedisAdapter] = None
_adapter_lock = threading.Lock()


def get_python_adapter() -> PythonRedisAdapter:
    """
    Get global Python Redis adapter instance.
    
    Returns:
        PythonRedisAdapter instance
    """
    global _python_adapter
    
    with _adapter_lock:
        if _python_adapter is None:
            _python_adapter = PythonRedisAdapter()
            logger.info("Initialized global Python Redis adapter")
    
    return _python_adapter
