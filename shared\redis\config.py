"""
Centralized Redis configuration for Voice Health Portal

Provides unified configuration management for Redis across all applications.
Supports environment-based configuration with production-ready defaults.
"""

import os
import logging
from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
from urllib.parse import urlparse

logger = logging.getLogger(__name__)


@dataclass
class RedisConfig:
    """
    Centralized Redis configuration with production-ready defaults.
    Supports both URL-based and individual parameter configuration.
    """
    
    # Connection settings
    url: str = field(default_factory=lambda: os.environ.get("REDIS_URL", "redis://localhost:6379/0"))
    host: str = field(default_factory=lambda: os.environ.get("REDIS_HOST", "localhost"))
    port: int = field(default_factory=lambda: int(os.environ.get("REDIS_PORT", "6379")))
    password: Optional[str] = field(default_factory=lambda: os.environ.get("REDIS_PASSWORD"))
    db: int = field(default_factory=lambda: int(os.environ.get("REDIS_DB", "0")))
    
    # Connection pool settings
    max_connections: int = field(default_factory=lambda: int(os.environ.get("REDIS_MAX_CONNECTIONS", "50")))
    retry_on_timeout: bool = field(default_factory=lambda: os.environ.get("REDIS_RETRY_ON_TIMEOUT", "true").lower() == "true")
    socket_timeout: float = field(default_factory=lambda: float(os.environ.get("REDIS_SOCKET_TIMEOUT", "5.0")))
    socket_connect_timeout: float = field(default_factory=lambda: float(os.environ.get("REDIS_SOCKET_CONNECT_TIMEOUT", "5.0")))
    health_check_interval: int = field(default_factory=lambda: int(os.environ.get("REDIS_HEALTH_CHECK_INTERVAL", "30")))
    
    # Encoding settings
    decode_responses: bool = field(default_factory=lambda: os.environ.get("REDIS_DECODE_RESPONSES", "false").lower() == "true")
    encoding: str = field(default_factory=lambda: os.environ.get("REDIS_ENCODING", "utf-8"))
    
    # Cache settings
    default_ttl: int = field(default_factory=lambda: int(os.environ.get("REDIS_DEFAULT_TTL", "3600")))  # 1 hour
    semantic_cache_ttl: int = field(default_factory=lambda: int(os.environ.get("REDIS_SEMANTIC_CACHE_TTL", "86400")))  # 24 hours
    call_context_ttl: int = field(default_factory=lambda: int(os.environ.get("REDIS_CALL_CONTEXT_TTL", "600")))  # 10 minutes
    
    # Monitoring settings
    enable_monitoring: bool = field(default_factory=lambda: os.environ.get("REDIS_ENABLE_MONITORING", "true").lower() == "true")
    metrics_interval: int = field(default_factory=lambda: int(os.environ.get("REDIS_METRICS_INTERVAL", "60")))
    
    # Application-specific settings
    voice_agent_enabled: bool = field(default_factory=lambda: os.environ.get("REDIS_VOICE_AGENT_ENABLED", "true").lower() == "true")
    whatsapp_agent_enabled: bool = field(default_factory=lambda: os.environ.get("REDIS_WHATSAPP_AGENT_ENABLED", "true").lower() == "true")
    staff_portal_enabled: bool = field(default_factory=lambda: os.environ.get("REDIS_STAFF_PORTAL_ENABLED", "true").lower() == "true")
    
    # Semantic processing settings
    indic_bert_enabled: bool = field(default_factory=lambda: os.environ.get("REDIS_INDIC_BERT_ENABLED", "true").lower() == "true")
    semantic_similarity_threshold: float = field(default_factory=lambda: float(os.environ.get("REDIS_SEMANTIC_SIMILARITY_THRESHOLD", "0.8")))
    embedding_dimension: int = field(default_factory=lambda: int(os.environ.get("REDIS_EMBEDDING_DIMENSION", "768")))
    
    def __post_init__(self):
        """Validate and process configuration after initialization."""
        self._validate_config()
        self._parse_url_if_provided()
    
    def _validate_config(self):
        """Validate configuration parameters."""
        if self.port < 1 or self.port > 65535:
            raise ValueError(f"Invalid Redis port: {self.port}")
        
        if self.db < 0 or self.db > 15:
            raise ValueError(f"Invalid Redis database: {self.db}")
        
        if self.max_connections < 1:
            raise ValueError(f"Invalid max_connections: {self.max_connections}")
        
        if self.socket_timeout <= 0:
            raise ValueError(f"Invalid socket_timeout: {self.socket_timeout}")
        
        if self.semantic_similarity_threshold < 0 or self.semantic_similarity_threshold > 1:
            raise ValueError(f"Invalid semantic_similarity_threshold: {self.semantic_similarity_threshold}")
    
    def _parse_url_if_provided(self):
        """Parse Redis URL and override individual parameters if URL is provided."""
        if self.url and self.url != "redis://localhost:6379/0":
            try:
                parsed = urlparse(self.url)
                if parsed.hostname:
                    self.host = parsed.hostname
                if parsed.port:
                    self.port = parsed.port
                if parsed.password:
                    self.password = parsed.password
                if parsed.path and len(parsed.path) > 1:
                    # Remove leading '/' from path
                    db_str = parsed.path[1:]
                    if db_str.isdigit():
                        self.db = int(db_str)
                        
                logger.info(f"Parsed Redis URL: {self.host}:{self.port}/{self.db}")
            except Exception as e:
                logger.warning(f"Failed to parse Redis URL {self.url}: {e}")
    
    def get_connection_kwargs(self) -> Dict[str, Any]:
        """Get connection parameters for Redis client."""
        kwargs = {
            "host": self.host,
            "port": self.port,
            "db": self.db,
            "socket_timeout": self.socket_timeout,
            "socket_connect_timeout": self.socket_connect_timeout,
            "retry_on_timeout": self.retry_on_timeout,
            "health_check_interval": self.health_check_interval,
            "decode_responses": self.decode_responses,
            "encoding": self.encoding
        }
        
        if self.password:
            kwargs["password"] = self.password
            
        return kwargs
    
    def get_pool_kwargs(self) -> Dict[str, Any]:
        """Get connection pool parameters."""
        kwargs = self.get_connection_kwargs()
        kwargs["max_connections"] = self.max_connections
        return kwargs
    
    def get_url(self) -> str:
        """Get Redis URL for connection."""
        if self.url and self.url != "redis://localhost:6379/0":
            return self.url
        
        # Construct URL from individual parameters
        auth = f":{self.password}@" if self.password else ""
        return f"redis://{auth}{self.host}:{self.port}/{self.db}"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            "connection": {
                "url": self.get_url(),
                "host": self.host,
                "port": self.port,
                "db": self.db,
                "max_connections": self.max_connections
            },
            "timeouts": {
                "socket_timeout": self.socket_timeout,
                "socket_connect_timeout": self.socket_connect_timeout,
                "health_check_interval": self.health_check_interval
            },
            "cache": {
                "default_ttl": self.default_ttl,
                "semantic_cache_ttl": self.semantic_cache_ttl,
                "call_context_ttl": self.call_context_ttl
            },
            "features": {
                "voice_agent_enabled": self.voice_agent_enabled,
                "whatsapp_agent_enabled": self.whatsapp_agent_enabled,
                "staff_portal_enabled": self.staff_portal_enabled,
                "indic_bert_enabled": self.indic_bert_enabled
            },
            "semantic": {
                "similarity_threshold": self.semantic_similarity_threshold,
                "embedding_dimension": self.embedding_dimension
            }
        }


# Global configuration instance
_redis_config: Optional[RedisConfig] = None


def get_redis_config() -> RedisConfig:
    """Get global Redis configuration instance."""
    global _redis_config
    if _redis_config is None:
        _redis_config = RedisConfig()
        logger.info("Initialized Redis configuration")
    return _redis_config


def reload_redis_config() -> RedisConfig:
    """Reload Redis configuration from environment variables."""
    global _redis_config
    _redis_config = RedisConfig()
    logger.info("Reloaded Redis configuration from environment")
    return _redis_config


def validate_environment() -> Dict[str, Any]:
    """
    Validate Redis environment configuration.
    
    Returns:
        Dict with validation results and recommendations
    """
    config = get_redis_config()
    validation_result = {
        "valid": True,
        "warnings": [],
        "errors": [],
        "recommendations": []
    }
    
    # Check for production readiness
    if config.url == "redis://localhost:6379/0":
        validation_result["warnings"].append("Using default localhost Redis URL - not suitable for production")
    
    if not config.password and "localhost" not in config.host:
        validation_result["warnings"].append("No Redis password configured for remote connection")
    
    if config.max_connections < 20:
        validation_result["recommendations"].append("Consider increasing max_connections for production workloads")
    
    if config.socket_timeout > 10:
        validation_result["warnings"].append("High socket timeout may cause performance issues")
    
    # Check feature flags
    enabled_features = []
    if config.voice_agent_enabled:
        enabled_features.append("voice_agent")
    if config.whatsapp_agent_enabled:
        enabled_features.append("whatsapp_agent")
    if config.staff_portal_enabled:
        enabled_features.append("staff_portal")
    
    validation_result["enabled_features"] = enabled_features
    validation_result["config_summary"] = config.to_dict()
    
    if validation_result["errors"]:
        validation_result["valid"] = False
    
    return validation_result
