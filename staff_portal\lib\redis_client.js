/**
 * Enhanced Redis Client for Staff Portal
 * Now uses shared Redis implementation with enhanced features:
 * - Automatic connection pooling and health monitoring
 * - Integration with voice agent and WhatsApp agent caches
 * - Advanced doctor availability management
 * - Semantic caching capabilities
 */

import { getNodeJSAdapter } from '../../shared/redis/adapters/nodejs_adapter.js';
import { logger } from './logger';

class RedisClient {
  constructor() {
    this.sharedAdapter = null;
    this.isConnected = false;
    this.connectionAttempts = 0;
    this.maxRetries = 3;
    this.initialized = false;
  }

  /**
   * Initialize enhanced Redis connection with shared implementation
   */
  async connect() {
    if (this.isConnected && this.sharedAdapter) {
      return this.sharedAdapter;
    }

    try {
      // Get shared Redis adapter
      this.sharedAdapter = getNodeJSAdapter();

      // Initialize with enhanced features
      const success = await this.sharedAdapter.initialize();

      if (success) {
        this.isConnected = true;
        this.connectionAttempts = 0;
        this.initialized = true;

        logger.info('[REDIS_CLIENT] Connected to enhanced Redis successfully');

        // Initialize staff portal specific features
        await this.initializeStaffPortalFeatures();

        return this.sharedAdapter;
      } else {
        throw new Error('Failed to initialize shared Redis adapter');
      }
    } catch (error) {
      this.connectionAttempts++;
      logger.error(`[REDIS_CLIENT] Failed to connect to enhanced Redis (attempt ${this.connectionAttempts}):`, error);

      if (this.connectionAttempts >= this.maxRetries) {
        logger.error('[REDIS_CLIENT] Max Redis connection attempts reached');
        throw new Error('Enhanced Redis connection failed after maximum retries');
      }

      throw error;
    }
  }

  /**
   * Initialize staff portal specific Redis features
   */
  async initializeStaffPortalFeatures() {
    try {
      // Pre-cache common staff portal queries
      const commonQueries = [
        {
          query: 'booking limits',
          response: 'Current booking limits are configured per doctor and time slot.',
          category: 'booking_management'
        },
        {
          query: 'doctor schedule',
          response: 'Doctor schedules can be updated through the staff portal.',
          category: 'schedule_management'
        }
      ];

      for (const item of commonQueries) {
        const cacheKey = `semantic:staff:common:${this.sharedAdapter._hashString(item.query)}`;
        await this.sharedAdapter.set(
          cacheKey,
          {
            query: item.query,
            response: item.response,
            category: item.category,
            source: 'staff_portal',
            timestamp: Date.now()
          },
          this.sharedAdapter.config.semantic_cache_ttl
        );
      }

      logger.info('[REDIS_CLIENT] Staff portal features initialized');
    } catch (error) {
      logger.error('[REDIS_CLIENT] Error initializing staff portal features:', error);
    }
  }

  /**
   * Get enhanced Redis adapter instance
   */
  async getClient() {
    if (!this.isConnected || !this.sharedAdapter) {
      await this.connect();
    }
    return this.sharedAdapter;
  }

  /**
   * Check if Redis is connected
   */
  async isRedisConnected() {
    try {
      if (!this.client) return false;
      await this.client.ping();
      return true;
    } catch (error) {
      logger.error('[REDIS_CLIENT] Redis ping failed:', error);
      return false;
    }
  }

  /**
   * Production-safe key scanning using SCAN instead of KEYS
   * @param {string} pattern - Redis key pattern to match
   * @param {number} count - Number of keys to return per iteration (default: 100)
   * @returns {Promise<string[]>} Array of matching keys
   */
  async scanKeys(pattern, count = 100) {
    try {
      const client = await this.getClient();
      const keys = [];
      const stream = client.scanStream({
        match: pattern,
        count: count
      });

      await new Promise((resolve, reject) => {
        stream.on('data', (resultKeys) => {
          keys.push(...resultKeys);
        });
        stream.on('end', resolve);
        stream.on('error', reject);
      });

      return keys;
    } catch (error) {
      logger.error('[REDIS_CLIENT] Error scanning keys with pattern:', pattern, error);
      return [];
    }
  }

  /**
   * Get doctor's daily booking limits from Redis
   */
  async getDoctorDailyLimits(hospitalId, doctorId) {
    try {
      const client = await this.getClient();
      const key = `booking_limits:${hospitalId}:${doctorId}:daily_limits`;
      const data = await client.get(key);
      
      if (data) {
        return JSON.parse(data);
      }
      return null;
    } catch (error) {
      logger.error('[REDIS_CLIENT] Error getting doctor daily limits:', error);
      return null;
    }
  }

  /**
   * Get current booking counter for a doctor on a specific date
   */
  async getBookingCounter(hospitalId, doctorId, date) {
    try {
      const client = await this.getClient();
      const key = `booking_counter:${hospitalId}:${doctorId}:${date}`;
      const count = await client.get(key);
      return count ? parseInt(count) : 0;
    } catch (error) {
      logger.error('[REDIS_CLIENT] Error getting booking counter:', error);
      return 0;
    }
  }

  /**
   * Get next available date for a doctor
   */
  async getNextAvailableDate(hospitalId, doctorId) {
    try {
      const client = await this.getClient();
      const key = `booking_limits:${hospitalId}:${doctorId}:next_available`;
      return await client.get(key);
    } catch (error) {
      logger.error('[REDIS_CLIENT] Error getting next available date:', error);
      return null;
    }
  }

  /**
   * Trigger manual refresh of booking limits in voice agent
   */
  async triggerBookingLimitRefresh(hospitalId = null) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    try {
      const voiceAgentUrl = process.env.VOICE_AGENT_URL;
      if (!voiceAgentUrl) {
        throw new Error('VOICE_AGENT_URL environment variable is required');
      }
      const refreshUrl = `${voiceAgentUrl}/api/booking-limits/refresh`;

      const response = await fetch(refreshUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          hospital_id: hospitalId
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Voice agent refresh failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      logger.info('[REDIS_CLIENT] Voice agent booking limit refresh triggered:', result);

      return {
        success: true,
        data: result
      };
    } catch (error) {
      clearTimeout(timeoutId);

      if (error.name === 'AbortError') {
        logger.error('[REDIS_CLIENT] Voice agent refresh request timed out after 10 seconds');
        return {
          success: false,
          error: 'Request timed out after 10 seconds'
        };
      }

      logger.error('[REDIS_CLIENT] Error triggering voice agent refresh:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get booking availability status for a doctor
   */
  async getBookingAvailabilityStatus(hospitalId, doctorId, date = null) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

    try {
      if (!date) {
        date = new Date().toISOString().split('T')[0]; // Today's date in YYYY-MM-DD format
      }

      const voiceAgentUrl = process.env.VOICE_AGENT_URL;
      if (!voiceAgentUrl) {
        throw new Error('VOICE_AGENT_URL environment variable is required');
      }
      const statusUrl = `${voiceAgentUrl}/api/booking-limits/status/${hospitalId}/${doctorId}?date=${date}`;

      const response = await fetch(statusUrl, {
        method: 'GET',
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Status check failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      return {
        success: true,
        data: result
      };
    } catch (error) {
      clearTimeout(timeoutId);

      if (error.name === 'AbortError') {
        logger.error('[REDIS_CLIENT] Booking availability status request timed out after 5 seconds');
        return {
          success: false,
          error: 'Request timed out after 5 seconds'
        };
      }

      logger.error('[REDIS_CLIENT] Error getting booking availability status:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get availability status for a doctor or test on a specific date
   */
  async getAvailabilityStatus(hospitalId, itemId, itemType, date) {
    try {
      const client = await this.getClient();
      const key = `availability:${hospitalId}:${itemType}:${itemId}:${date}`;
      const status = await client.get(key);
      return status !== null ? JSON.parse(status) : null;
    } catch (error) {
      logger.error('[REDIS_CLIENT] Error getting availability status:', error);
      return null;
    }
  }

  /**
   * Set availability status for a doctor or test on a specific date
   */
  async setAvailabilityStatus(hospitalId, itemId, itemType, date, isAvailable) {
    try {
      const client = await this.getClient();
      const key = `availability:${hospitalId}:${itemType}:${itemId}:${date}`;
      await client.setex(key, 86400, JSON.stringify(isAvailable)); // 24 hour TTL
      return true;
    } catch (error) {
      logger.error('[REDIS_CLIENT] Error setting availability status:', error);
      return false;
    }
  }

  /**
   * Get all available doctors for a hospital on a specific date
   */
  async getAvailableDoctors(hospitalId, date) {
    try {
      const client = await this.getClient();
      const pattern = `availability:${hospitalId}:doctor:*:${date}`;

      // Use production-safe SCAN instead of KEYS
      const keys = await this.scanKeys(pattern);

      const availableDoctors = [];
      for (const key of keys) {
        const isAvailable = await client.get(key);
        if (isAvailable && JSON.parse(isAvailable) === true) {
          // Extract doctor ID from key
          const doctorId = key.split(':')[3];
          availableDoctors.push(doctorId);
        }
      }

      return availableDoctors;
    } catch (error) {
      logger.error('[REDIS_CLIENT] Error getting available doctors:', error);
      return [];
    }
  }

  /**
   * Get all available tests for a hospital on a specific date
   */
  async getAvailableTests(hospitalId, date) {
    try {
      const client = await this.getClient();
      const pattern = `availability:${hospitalId}:test:*:${date}`;

      // Use production-safe SCAN instead of KEYS
      const keys = await this.scanKeys(pattern);

      const availableTests = [];
      for (const key of keys) {
        const isAvailable = await client.get(key);
        if (isAvailable && JSON.parse(isAvailable) === true) {
          // Extract test ID from key
          const testId = key.split(':')[3];
          availableTests.push(testId);
        }
      }

      return availableTests;
    } catch (error) {
      logger.error('[REDIS_CLIENT] Error getting available tests:', error);
      return [];
    }
  }

  /**
   * Trigger manual refresh of availability data in voice agent
   */
  async triggerAvailabilityRefresh(hospitalId, date = null) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    try {
      const voiceAgentUrl = process.env.VOICE_AGENT_URL;
      if (!voiceAgentUrl) {
        throw new Error('VOICE_AGENT_URL environment variable is required');
      }
      const refreshUrl = `${voiceAgentUrl}/api/availability/refresh`;

      const response = await fetch(refreshUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          hospital_id: hospitalId,
          date: date
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Voice agent availability refresh failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      logger.info('[REDIS_CLIENT] Voice agent availability refresh triggered:', result);

      return {
        success: true,
        data: result
      };
    } catch (error) {
      clearTimeout(timeoutId);

      if (error.name === 'AbortError') {
        logger.error('[REDIS_CLIENT] Voice agent availability refresh request timed out after 10 seconds');
        return {
          success: false,
          error: 'Request timed out after 10 seconds'
        };
      }

      logger.error('[REDIS_CLIENT] Error triggering voice agent availability refresh:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Trigger manual refresh of scheduling configuration in voice agent
   */
  async triggerSchedulingConfigRefresh(hospitalId = null) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    try {
      const voiceAgentUrl = process.env.VOICE_AGENT_URL;
      if (!voiceAgentUrl) {
        throw new Error('VOICE_AGENT_URL environment variable is required');
      }
      const refreshUrl = `${voiceAgentUrl}/api/scheduling/config/refresh`;

      const response = await fetch(refreshUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          hospital_id: hospitalId
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Voice agent scheduling config refresh failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      logger.info('[REDIS_CLIENT] Voice agent scheduling config refresh triggered:', result);

      return {
        success: true,
        data: result
      };
    } catch (error) {
      clearTimeout(timeoutId);

      if (error.name === 'AbortError') {
        logger.error('[REDIS_CLIENT] Voice agent scheduling config refresh request timed out after 10 seconds');
        return {
          success: false,
          error: 'Request timed out after 10 seconds'
        };
      }

      logger.error('[REDIS_CLIENT] Error triggering voice agent scheduling config refresh:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Enhanced doctor availability management for staff portal
   */
  async updateDoctorAvailability(hospitalId, doctorId, status, estimatedTime = null, metadata = {}) {
    try {
      const adapter = await this.getClient();

      // Update availability using shared adapter
      const success = await adapter.cacheDoctorAvailability(
        hospitalId,
        doctorId,
        status,
        estimatedTime
      );

      if (success) {
        // Also update in staff portal specific cache for tracking
        const staffUpdateKey = `staff:update:${hospitalId}:${doctorId}`;
        await adapter.set(staffUpdateKey, {
          doctor_id: doctorId,
          status,
          estimated_time: estimatedTime,
          updated_by: metadata.staff_id || 'unknown',
          updated_at: Date.now(),
          source: 'staff_portal'
        }, 86400); // 24 hours

        logger.info(`[REDIS_CLIENT] Updated doctor availability: ${doctorId} -> ${status}`);
        return { success: true, doctor_id: doctorId, status, estimated_time: estimatedTime };
      } else {
        logger.error(`[REDIS_CLIENT] Failed to update doctor availability for ${doctorId}`);
        return { success: false, error: 'Failed to update availability' };
      }
    } catch (error) {
      logger.error(`[REDIS_CLIENT] Error updating doctor availability:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Batch update multiple doctor availabilities
   */
  async batchUpdateDoctorAvailability(hospitalId, doctorUpdates, metadata = {}) {
    try {
      const adapter = await this.getClient();
      const results = {
        successful: 0,
        failed: 0,
        updates: [],
        errors: []
      };

      // Process updates concurrently for better performance
      const updatePromises = doctorUpdates.map(async (update) => {
        try {
          const result = await this.updateDoctorAvailability(
            hospitalId,
            update.doctorId,
            update.status,
            update.estimatedTime,
            metadata
          );

          if (result.success) {
            results.successful++;
            results.updates.push(result);
          } else {
            results.failed++;
            results.errors.push(`${update.doctorId}: ${result.error}`);
          }
        } catch (error) {
          results.failed++;
          results.errors.push(`${update.doctorId}: ${error.message}`);
        }
      });

      await Promise.all(updatePromises);

      logger.info(`[REDIS_CLIENT] Batch update completed: ${results.successful} successful, ${results.failed} failed`);
      return results;
    } catch (error) {
      logger.error(`[REDIS_CLIENT] Error in batch update:`, error);
      return {
        successful: 0,
        failed: doctorUpdates.length,
        updates: [],
        errors: [error.message]
      };
    }
  }

  /**
   * Get doctor availability status
   */
  async getDoctorAvailability(hospitalId, doctorId) {
    try {
      const adapter = await this.getClient();
      return await adapter.getDoctorAvailability(hospitalId, doctorId);
    } catch (error) {
      logger.error(`[REDIS_CLIENT] Error getting doctor availability:`, error);
      return null;
    }
  }

  /**
   * Get cache statistics for monitoring
   */
  async getCacheStats() {
    try {
      const adapter = await this.getClient();
      return await adapter.getStats();
    } catch (error) {
      logger.error(`[REDIS_CLIENT] Error getting cache stats:`, error);
      return { error: error.message };
    }
  }

  /**
   * Clear cache for specific hospital
   */
  async clearHospitalCache(hospitalId, category = null) {
    try {
      const adapter = await this.getClient();
      const pattern = category
        ? `*:${hospitalId}:${category}:*`
        : `*:${hospitalId}:*`;

      return await adapter.clearCache(pattern);
    } catch (error) {
      logger.error(`[REDIS_CLIENT] Error clearing hospital cache:`, error);
      return 0;
    }
  }

  /**
   * Close enhanced Redis connection
   */
  async disconnect() {
    if (this.sharedAdapter) {
      try {
        await this.sharedAdapter.close();
        logger.info('[REDIS_CLIENT] Enhanced Redis connection closed gracefully');
      } catch (error) {
        logger.error('[REDIS_CLIENT] Error closing enhanced Redis connection:', error);
      } finally {
        this.sharedAdapter = null;
        this.isConnected = false;
        this.initialized = false;
      }
    }
  }
}

// Create singleton instance
const redisClient = new RedisClient();

export default redisClient;
