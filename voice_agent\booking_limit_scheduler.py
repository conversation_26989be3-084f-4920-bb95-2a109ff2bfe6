"""
Booking Limit Scheduler for Voice Agent System.

This module handles the daily refresh of booking limits and next available dates
from Firebase to Redis cache. It runs at 12 PM daily to ensure fresh data.
"""

import asyncio
import logging
from datetime import datetime, timedelta, time
from typing import Dict, Any, List, Optional
from shared.redis.migration_helper import get_shared_redis_manager
redis_manager = get_shared_redis_manager()
from .database import get_firestore_db
from .datetime_utils import (
    get_date_string_cached, get_day_name_from_date,
    calculate_date_offset, parse_date_robust
)

logger = logging.getLogger(__name__)


class BookingLimitScheduler:
    """
    Scheduler for daily booking limit cache refresh.
    
    This class manages the daily task of:
    1. Querying Firebase for doctor booking limits
    2. Calculating next available dates for each doctor
    3. Caching the data in Redis for fast lookups
    4. Resetting daily booking counters
    """

    def __init__(self):
        """Initialize the booking limit scheduler."""
        self.is_running = False
        self.task = None
        self.refresh_time = time(12, 0)  # 12:00 PM daily

    async def start_scheduler(self):
        """Start the daily booking limit refresh scheduler."""
        if self.is_running:
            logger.warning("Booking limit scheduler is already running")
            return

        self.is_running = True
        self.task = asyncio.create_task(self._scheduler_loop())
        logger.info("Booking limit scheduler started - will refresh daily at 12:00 PM")

    async def stop_scheduler(self):
        """Stop the booking limit refresh scheduler."""
        if not self.is_running:
            return

        self.is_running = False
        if self.task:
            self.task.cancel()
            try:
                await self.task
            except asyncio.CancelledError:
                pass
        logger.info("Booking limit scheduler stopped")

    async def _scheduler_loop(self):
        """Main scheduler loop that runs daily at 12 PM."""
        while self.is_running:
            try:
                # Calculate time until next refresh (12 PM)
                now = datetime.now()
                next_refresh = self._get_next_refresh_time(now)
                sleep_seconds = (next_refresh - now).total_seconds()

                logger.info(f"Next booking limit refresh scheduled for: {next_refresh}")
                
                # Sleep until refresh time
                await asyncio.sleep(sleep_seconds)
                
                if self.is_running:  # Check if still running after sleep
                    await self._refresh_all_booking_limits()

            except asyncio.CancelledError:
                logger.info("Booking limit scheduler cancelled")
                break
            except Exception as e:
                logger.error(f"Error in booking limit scheduler loop: {e}")
                # Sleep for 1 hour before retrying on error
                await asyncio.sleep(3600)

    def _get_next_refresh_time(self, current_time: datetime) -> datetime:
        """
        Calculate the next refresh time (12 PM today or tomorrow).
        
        Args:
            current_time: Current datetime
            
        Returns:
            Next refresh datetime
        """
        today_refresh = current_time.replace(
            hour=self.refresh_time.hour,
            minute=self.refresh_time.minute,
            second=0,
            microsecond=0
        )
        
        if current_time >= today_refresh:
            # If it's already past 12 PM today, schedule for tomorrow
            return today_refresh + timedelta(days=1)
        else:
            # Schedule for today at 12 PM
            return today_refresh

    async def _refresh_all_booking_limits(self):
        """
        Refresh booking limits for all hospitals and doctors.
        This is the main daily refresh operation.
        """
        try:
            logger.info("Starting daily booking limit refresh...")
            
            # Get all hospitals from Firebase
            hospital_ids = await self._get_all_hospitals()
            
            if not hospital_ids:
                logger.warning("No hospitals found for booking limit refresh")
                return

            total_doctors = 0
            successful_refreshes = 0

            for hospital_id in hospital_ids:
                try:
                    doctors_refreshed = await self._refresh_hospital_booking_limits(hospital_id)
                    total_doctors += doctors_refreshed
                    successful_refreshes += doctors_refreshed
                    logger.info(f"Refreshed booking limits for {doctors_refreshed} doctors in hospital {hospital_id}")
                    
                except Exception as e:
                    logger.error(f"Error refreshing booking limits for hospital {hospital_id}: {e}")

            logger.info(f"Daily booking limit refresh completed: {successful_refreshes}/{total_doctors} doctors updated")

        except Exception as e:
            logger.error(f"Error in daily booking limit refresh: {e}")

    async def _get_all_hospitals(self) -> List[str]:
        """
        Get all hospital IDs from Firebase.
        
        Returns:
            List of hospital IDs
        """
        try:
            db = get_firestore_db()
            loop = asyncio.get_running_loop()
            
            # Get all collections that match hospital pattern
            collections = await loop.run_in_executor(None, db.collections)
            
            hospital_ids = []
            for collection in collections:
                collection_id = collection.id
                if collection_id.startswith('hospital_') and collection_id.endswith('_data'):
                    # Extract hospital ID from collection name
                    hospital_id = collection_id.replace('hospital_', '').replace('_data', '')
                    hospital_ids.append(hospital_id)
            
            logger.info(f"Found {len(hospital_ids)} hospitals for booking limit refresh")
            return hospital_ids
            
        except Exception as e:
            logger.error(f"Error getting hospital IDs: {e}")
            return []

    async def _refresh_hospital_booking_limits(self, hospital_id: str) -> int:
        """
        Refresh booking limits for all doctors in a specific hospital.
        
        Args:
            hospital_id: Hospital identifier
            
        Returns:
            Number of doctors successfully refreshed
        """
        try:
            db = get_firestore_db()
            loop = asyncio.get_running_loop()
            
            # Get all doctors for this hospital
            doctors_ref = db.collection(f'hospital_{hospital_id}_data').document('doctors').collection('doctors')
            doctors_docs = await loop.run_in_executor(None, doctors_ref.get)
            
            doctors_refreshed = 0
            
            for doctor_doc in doctors_docs:
                try:
                    doctor_data = doctor_doc.to_dict()
                    doctor_id = doctor_doc.id
                    
                    # Extract booking limits and schedule
                    daily_limits = doctor_data.get('daily_booking_limits', {})
                    schedule = doctor_data.get('schedule', {})
                    
                    if daily_limits:
                        # Cache daily limits in Redis
                        await redis_manager.cache_doctor_daily_limits_async(
                            hospital_id, doctor_id, daily_limits, ttl=86400
                        )
                        
                        # Calculate and cache next available date
                        next_date = await self._calculate_next_available_date(
                            hospital_id, doctor_id, daily_limits, schedule
                        )
                        
                        if next_date:
                            await redis_manager.cache_next_available_date_async(
                                hospital_id, doctor_id, next_date, ttl=86400
                            )

                        # Cache doctor availability for the next 7 days
                        await self._cache_doctor_availability(hospital_id, doctor_id, doctor_data)

                        doctors_refreshed += 1
                        logger.debug(f"Refreshed booking limits and availability for doctor {doctor_id}")
                    
                except Exception as e:
                    logger.error(f"Error refreshing doctor {doctor_doc.id}: {e}")
            
            # Also refresh test availability for this hospital
            await self._refresh_hospital_test_availability(hospital_id)

            return doctors_refreshed

        except Exception as e:
            logger.error(f"Error refreshing hospital {hospital_id} booking limits: {e}")
            return 0

    async def _calculate_next_available_date(self, hospital_id: str, doctor_id: str, 
                                           daily_limits: Dict[str, int], 
                                           schedule: Dict[str, Any]) -> Optional[str]:
        """
        Calculate the next available date for a doctor based on current bookings and limits.
        
        Args:
            hospital_id: Hospital identifier
            doctor_id: Doctor identifier
            daily_limits: Doctor's daily booking limits
            schedule: Doctor's schedule information
            
        Returns:
            Next available date in YYYY-MM-DD format or None
        """
        try:
            current_date = datetime.now().date()
            max_check_date = current_date + timedelta(days=30)  # Check up to 30 days ahead

            check_date = current_date
            while check_date <= max_check_date:
                date_str = check_date.strftime('%Y-%m-%d')
                day_name = get_day_name_from_date(check_date)
                
                # Check if doctor works on this day
                limit = daily_limits.get(day_name, 0)
                
                if limit > 0:  # Doctor works on this day
                    # Get current booking count from Redis
                    current_count = await redis_manager.get_booking_counter_async(
                        hospital_id, doctor_id, date_str
                    )
                    
                    if current_count < limit:
                        logger.debug(f"Next available date for doctor {doctor_id}: {date_str}")
                        return date_str
                
                check_date += timedelta(days=1)
            
            logger.warning(f"No available dates found for doctor {doctor_id} within 30 days")
            return None
            
        except Exception as e:
            logger.error(f"Error calculating next available date for doctor {doctor_id}: {e}")
            return None

    async def _cache_doctor_availability(self, hospital_id: str, doctor_id: str, doctor_data: Dict[str, Any]):
        """
        Cache doctor availability for the next 7 days based on Firebase data.

        Args:
            hospital_id: Hospital identifier
            doctor_id: Doctor identifier
            doctor_data: Doctor data from Firebase
        """
        try:
            daily_availability = doctor_data.get('daily_availability', {})

            # Cache availability for the next 7 days
            current_date = datetime.now().date()

            for i in range(7):
                check_date = current_date + timedelta(days=i)
                date_str = check_date.strftime('%Y-%m-%d')

                # Get availability for this date (default to True if not specified)
                is_available = daily_availability.get(date_str, True)

                # Cache in Redis
                await redis_manager.cache_availability_async(
                    hospital_id, doctor_id, 'doctor', date_str, is_available, ttl=86400
                )

            logger.debug(f"Cached 7-day availability for doctor {doctor_id}")
        except Exception as e:
            logger.error(f"Error caching doctor availability for {doctor_id}: {e}")

    async def _refresh_hospital_test_availability(self, hospital_id: str):
        """
        Refresh test availability for all tests in a specific hospital.

        Args:
            hospital_id: Hospital identifier
        """
        try:
            db = get_firestore_db()
            loop = asyncio.get_running_loop()

            # Get all tests for this hospital
            tests_ref = db.collection(f'hospital_{hospital_id}_data').document('test_info').collection('tests')
            tests_docs = await loop.run_in_executor(None, tests_ref.get)

            for test_doc in tests_docs:
                try:
                    test_data = test_doc.to_dict()
                    test_id = test_doc.id

                    # Cache test availability for the next 7 days
                    await self._cache_test_availability(hospital_id, test_id, test_data)

                except Exception as e:
                    logger.error(f"Error refreshing test {test_doc.id}: {e}")

            logger.debug(f"Refreshed test availability for hospital {hospital_id}")
        except Exception as e:
            logger.error(f"Error refreshing hospital {hospital_id} test availability: {e}")

    async def _cache_test_availability(self, hospital_id: str, test_id: str, test_data: Dict[str, Any]):
        """
        Cache test availability for the next 7 days based on Firebase data.

        Args:
            hospital_id: Hospital identifier
            test_id: Test identifier
            test_data: Test data from Firebase
        """
        try:
            daily_availability = test_data.get('daily_availability', {})

            # Cache availability for the next 7 days
            current_date = datetime.now().date()

            for i in range(7):
                check_date = current_date + timedelta(days=i)
                date_str = check_date.strftime('%Y-%m-%d')

                # Get availability for this date (default to True if not specified)
                is_available = daily_availability.get(date_str, True)

                # Cache in Redis
                await redis_manager.cache_availability_async(
                    hospital_id, test_id, 'test', date_str, is_available, ttl=86400
                )

            logger.debug(f"Cached 7-day availability for test {test_id}")
        except Exception as e:
            logger.error(f"Error caching test availability for {test_id}: {e}")

    async def manual_refresh(self, hospital_id: str = None) -> Dict[str, Any]:
        """
        Manually trigger a booking limit refresh for testing or immediate updates.
        
        Args:
            hospital_id: Optional specific hospital ID to refresh
            
        Returns:
            Dict with refresh results
        """
        try:
            logger.info(f"Manual booking limit refresh triggered for hospital: {hospital_id or 'all'}")
            
            if hospital_id:
                # Refresh specific hospital
                doctors_refreshed = await self._refresh_hospital_booking_limits(hospital_id)
                return {
                    'success': True,
                    'hospital_id': hospital_id,
                    'doctors_refreshed': doctors_refreshed,
                    'message': f'Refreshed {doctors_refreshed} doctors'
                }
            else:
                # Refresh all hospitals
                await self._refresh_all_booking_limits()
                return {
                    'success': True,
                    'message': 'Manual refresh completed for all hospitals'
                }
                
        except Exception as e:
            logger.error(f"Error in manual booking limit refresh: {e}")
            return {
                'success': False,
                'error': str(e)
            }


# Global scheduler instance
booking_scheduler = BookingLimitScheduler()
