"""
Example integration of WebSocket functionality with existing main.py

This file shows how to integrate the WebSocket implementation with your existing
FastAPI application while maintaining backward compatibility with webhooks.
"""

import asyncio
import logging
from datetime import datetime
from contextlib import asynccontextmanager
from fastapi import FastAPI, Request, HTTPException
from voice_agent.websocket_integration import integrate_websocket_with_lifespan
from voice_agent.websocket_config import websocket_config, WebSocketMode

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Example of how to modify your existing lifespan function
@asynccontextmanager
async def enhanced_lifespan(app: FastAPI):
    """
    Enhanced lifespan function that includes WebSocket server management
    """
    try:
        # Existing startup code (keep your current initialization)
        logger.info("Starting application...")
        
        # Initialize your existing components
        # await initialize_database()
        # await initialize_cache()
        # await initialize_llm_services()
        
        # Add WebSocket integration
        if websocket_config.is_websocket_enabled():
            logger.info("Initializing WebSocket integration...")
            websocket_integration = await integrate_websocket_with_lifespan(app)
            logger.info(f"WebSocket server started on {websocket_config.host}:{websocket_config.port}")
        else:
            logger.info("WebSocket disabled, running in webhook-only mode")
        
        logger.info("Application startup complete")
        yield
        
    except Exception as e:
        logger.error(f"Error during startup: {e}")
        raise
    finally:
        # Cleanup code (keep your existing cleanup)
        logger.info("Shutting down application...")
        
        # WebSocket cleanup is handled automatically by the integration
        
        # Your existing cleanup code
        # await cleanup_database()
        # await cleanup_cache()
        
        logger.info("Application shutdown complete")

# Create FastAPI app with enhanced lifespan
app = FastAPI(
    title="Voice Health Portal with WebSocket Support",
    description="Enhanced voice agent with WebSocket and webhook support",
    version="2.0.0",
    lifespan=enhanced_lifespan
)

# Example of how to handle both webhook and WebSocket modes
@app.middleware("http")
async def mode_detection_middleware(request: Request, call_next):
    """
    Middleware to detect and handle different communication modes
    """
    # Add mode information to request state
    if hasattr(app.state, 'websocket_integration'):
        integration = app.state.websocket_integration
        request.state.websocket_enabled = integration.is_websocket_running
        request.state.mode = websocket_config.mode
    else:
        request.state.websocket_enabled = False
        request.state.mode = WebSocketMode.WEBHOOK_ONLY
    
    response = await call_next(request)
    
    # Add mode information to response headers
    response.headers["X-Communication-Mode"] = request.state.mode.value
    response.headers["X-WebSocket-Enabled"] = str(request.state.websocket_enabled)
    
    return response

# Enhanced webhook endpoints that can redirect to WebSocket if needed
@app.post("/webhook/call")
async def enhanced_webhook_call(request: Request, data: dict):
    """
    Enhanced webhook endpoint that can redirect to WebSocket in hybrid mode
    """
    # Check if we should use WebSocket instead
    if hasattr(app.state, 'websocket_integration'):
        integration = app.state.websocket_integration
        
        if integration.should_use_websocket():
            # In hybrid mode, we might want to suggest WebSocket usage
            logger.info("WebSocket available but received webhook call")
            
            # You could implement logic here to:
            # 1. Process the webhook normally
            # 2. Suggest WebSocket upgrade to Jambonz
            # 3. Handle gracefully based on your requirements
    
    # Process webhook normally (keep your existing logic)
    # This is where your existing handle_call function would go
    return await handle_webhook_call_legacy(data)

@app.post("/webhook/gather")
async def enhanced_webhook_gather(request: Request, data: dict):
    """
    Enhanced gather webhook endpoint
    """
    # Similar logic as above
    if hasattr(app.state, 'websocket_integration'):
        integration = app.state.websocket_integration
        
        if integration.should_use_websocket():
            logger.info("WebSocket available but received webhook gather")
    
    # Process webhook normally (keep your existing logic)
    return await handle_webhook_gather_legacy(data)

# New endpoints for WebSocket management
@app.get("/api/communication/status")
async def communication_status():
    """
    Get current communication mode and capabilities
    """
    if hasattr(app.state, 'websocket_integration'):
        integration = app.state.websocket_integration
        return integration.get_integration_status()
    else:
        return {
            "mode": "webhook_only",
            "websocket_enabled": False,
            "webhook_enabled": True
        }

@app.get("/api/jambonz/application-url/{hospital_id}")
async def get_jambonz_application_url(hospital_id: str):
    """
    Get the appropriate Jambonz application URL for a hospital
    """
    if hasattr(app.state, 'websocket_integration'):
        integration = app.state.websocket_integration
        url = integration.get_jambonz_application_url(hospital_id)
        
        return {
            "hospital_id": hospital_id,
            "application_url": url,
            "mode": websocket_config.mode.value,
            "websocket_enabled": websocket_config.is_websocket_enabled()
        }
    else:
        # Fallback to webhook URL
        return {
            "hospital_id": hospital_id,
            "application_url": f"http://localhost:8000/webhook/call",
            "mode": "webhook_only",
            "websocket_enabled": False
        }

# Legacy functions (placeholders for your existing implementation)
async def handle_webhook_call_legacy(data: dict):
    """
    Placeholder for your existing webhook call handler
    Replace this with your actual implementation
    """
    # Your existing handle_call logic goes here
    return {"status": "processed", "mode": "webhook"}

async def handle_webhook_gather_legacy(data: dict):
    """
    Placeholder for your existing webhook gather handler
    Replace this with your actual implementation
    """
    # Your existing handle_gather logic goes here
    return {"status": "processed", "mode": "webhook"}

# Example of how to use WebSocket functionality programmatically
async def send_message_to_call_websocket(call_sid: str, message: dict):
    """
    Example function to send a message to a specific call via WebSocket
    """
    if hasattr(app.state, 'websocket_integration'):
        integration = app.state.websocket_integration
        if integration.is_websocket_running:
            success = await integration.websocket_server.manager.send_message_to_call(
                call_sid, message
            )
            return success
    return False

async def broadcast_to_hospital_websocket(hospital_id: str, message: dict):
    """
    Example function to broadcast a message to all connections for a hospital
    """
    if hasattr(app.state, 'websocket_integration'):
        integration = app.state.websocket_integration
        if integration.is_websocket_running:
            count = await integration.websocket_server.manager.broadcast_to_hospital(
                hospital_id, message
            )
            return count
    return 0

# Health check endpoints
@app.get("/health")
async def health_check():
    """
    Enhanced health check that includes WebSocket status
    """
    health_status = {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "mode": websocket_config.mode.value
    }
    
    if hasattr(app.state, 'websocket_integration'):
        integration = app.state.websocket_integration
        websocket_health = await integration.websocket_server.get_server_stats()
        health_status["websocket"] = {
            "enabled": websocket_config.is_websocket_enabled(),
            "running": integration.is_websocket_running,
            "connections": websocket_health["websocket_manager"]["active_connections"],
            "calls": websocket_health["websocket_manager"]["active_calls"]
        }
    
    return health_status

# Example startup script
if __name__ == "__main__":
    import uvicorn
    
    # Configuration based on environment
    host = websocket_config.host
    port = 8000  # HTTP port (WebSocket uses different port)
    
    logger.info(f"Starting Voice Health Portal")
    logger.info(f"HTTP server: {host}:{port}")
    logger.info(f"WebSocket mode: {websocket_config.mode.value}")
    
    if websocket_config.is_websocket_enabled():
        logger.info(f"WebSocket server: {websocket_config.host}:{websocket_config.port}")
    
    uvicorn.run(
        "voice_agent.websocket_example_integration:app",
        host=host,
        port=port,
        reload=False,  # Set to True for development
        log_level="info"
    )

"""
Environment Variables for Configuration:

# Basic WebSocket Configuration
WEBSOCKET_MODE=hybrid                    # hybrid, websocket_only, webhook_only
WEBSOCKET_HOST=0.0.0.0
WEBSOCKET_PORT=8765

# Connection Limits
MAX_WEBSOCKET_CONNECTIONS=1000
MAX_CONNECTIONS_PER_HOSPITAL=100

# Performance Tuning
WEBSOCKET_HEARTBEAT_INTERVAL=30.0
WEBSOCKET_CLEANUP_INTERVAL=300.0
WEBSOCKET_MAX_MESSAGE_SIZE=1048576
WEBSOCKET_ENABLE_COMPRESSION=false

# Error Handling
WEBSOCKET_MAX_ERROR_RATE=0.1
WEBSOCKET_ERROR_WINDOW=60

# Monitoring
WEBSOCKET_ENABLE_METRICS=true
WEBSOCKET_METRICS_INTERVAL=60.0

# Security
WEBSOCKET_REQUIRE_SUBPROTOCOL=true
JAMBONZ_SUBPROTOCOL=ws.jambonz.org

# Default Hospital (for testing)
DEFAULT_HOSPITAL_ID=1
"""
