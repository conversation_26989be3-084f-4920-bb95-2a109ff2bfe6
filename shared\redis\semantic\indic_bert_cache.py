"""
IndicBERT Semantic Cache for Voice Agent

Provides semantic caching functionality using IndicBERT embeddings for enhanced
Indian language support in the voice agent system.
Enhanced with date-aware caching for time-sensitive queries.
"""

import json
import logging
import time
from datetime import date, datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
import numpy as np
from ..base_operations import RedisOperations
from ..date_aware_operations import DateAwareRedisOperations, DateContext, get_date_aware_redis
from ..config import get_redis_config
from .embedding_manager import get_embedding_manager

logger = logging.getLogger(__name__)


class IndicBertCache:
    """
    Semantic cache using IndicBERT embeddings for voice agent.
    Optimized for Indian languages with sub-100ms response times.
    Enhanced with date-aware caching for time-sensitive queries.
    """

    def __init__(self, redis_ops: Optional[RedisOperations] = None,
                 hospital_timezone: str = "Asia/Kolkata"):
        """
        Initialize IndicBERT semantic cache with date awareness.

        Args:
            redis_ops: Optional Redis operations instance
            hospital_timezone: Hospital timezone for date calculations
        """
        self.redis_ops = redis_ops or RedisOperations()
        self.date_aware_ops = get_date_aware_redis(default_timezone=hospital_timezone)
        self.config = get_redis_config()
        self.embedding_manager = get_embedding_manager()
        self.hospital_timezone = hospital_timezone

        # Cache configuration
        self.similarity_threshold = self.config.semantic_similarity_threshold
        self.cache_ttl = self.config.semantic_cache_ttl

        # Statistics
        self._stats = {
            "cache_requests": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "embeddings_stored": 0,
            "similarity_searches": 0,
            "date_aware_requests": 0,
            "availability_checks": 0,
            "total_time": 0.0
        }
    
    def _get_cache_key(self, hospital_id: str, category: str, query_hash: str,
                      date_context: Optional[DateContext] = None) -> str:
        """Generate date-aware cache key for semantic entry."""
        base_key = f"semantic:indic:{hospital_id}:{category}:{query_hash}"
        if date_context and date_context.target_date:
            return self.date_aware_ops.generate_date_aware_key(base_key, date_context)
        return base_key

    def _get_embedding_key(self, hospital_id: str, category: str, query_hash: str,
                          date_context: Optional[DateContext] = None) -> str:
        """Generate date-aware key for storing embeddings."""
        base_key = f"embedding:indic:{hospital_id}:{category}:{query_hash}"
        if date_context and date_context.target_date:
            return self.date_aware_ops.generate_date_aware_key(base_key, date_context)
        return base_key

    def _get_index_key(self, hospital_id: str, category: str,
                      date_context: Optional[DateContext] = None) -> str:
        """Generate date-aware key for semantic index."""
        base_key = f"index:indic:{hospital_id}:{category}"
        if date_context and date_context.target_date:
            return self.date_aware_ops.generate_date_aware_key(base_key, date_context)
        return base_key
    
    def _serialize_embedding(self, embedding: np.ndarray) -> str:
        """Serialize embedding for Redis storage."""
        return json.dumps(embedding.tolist())
    
    def _deserialize_embedding(self, data: str) -> np.ndarray:
        """Deserialize embedding from Redis."""
        return np.array(json.loads(data))
    
    async def cache_response_async(self, query: str, response: str, hospital_id: str,
                                 category: str = "general", metadata: Optional[Dict[str, Any]] = None,
                                 extract_date_context: bool = True) -> bool:
        """
        Cache response with IndicBERT embedding for semantic matching.
        Enhanced with date-aware caching for time-sensitive queries.

        Args:
            query: User query text
            response: Response to cache
            hospital_id: Hospital identifier
            category: Response category
            metadata: Optional metadata
            extract_date_context: Whether to extract and use date context

        Returns:
            bool: True if cached successfully, False otherwise
        """
        start_time = time.time()

        try:
            # Extract date context if enabled
            date_context = None
            if extract_date_context:
                date_context = self.date_aware_ops.extract_query_date_context(
                    query, self.hospital_timezone
                )
                self._stats["date_aware_requests"] += 1

            # Generate embedding
            embedding = self.embedding_manager.generate_indic_bert_embedding(query)
            if embedding is None:
                logger.warning(f"Failed to generate embedding for query: {query}")
                return False

            # Create cache entry with date context
            query_hash = str(hash(query))
            cache_key = self._get_cache_key(hospital_id, category, query_hash, date_context)
            embedding_key = self._get_embedding_key(hospital_id, category, query_hash, date_context)
            index_key = self._get_index_key(hospital_id, category, date_context)
            
            cache_data = {
                "query": query,
                "response": response,
                "hospital_id": hospital_id,
                "category": category,
                "metadata": metadata or {},
                "timestamp": time.time(),
                "query_hash": query_hash,
                "date_context": date_context.to_dict() if date_context else None
            }

            # Calculate intelligent TTL based on date context
            if date_context:
                cache_ttl = self.date_aware_ops.calculate_date_aware_ttl(date_context, self.cache_ttl)
            else:
                cache_ttl = self.cache_ttl

            # Store cache entry, embedding, and update index with date-aware TTL
            success1 = await self.redis_ops.set_async(cache_key, cache_data, ttl=cache_ttl)
            success2 = await self.redis_ops.set_async(embedding_key, self._serialize_embedding(embedding), ttl=cache_ttl)

            # Add to index for similarity search
            index_data = await self.redis_ops.get_async(index_key, as_json=True) or []
            index_data.append({
                "query_hash": query_hash,
                "query": query[:100],  # Store truncated query for debugging
                "timestamp": time.time(),
                "date_context": date_context.to_dict() if date_context else None
            })

            # Limit index size
            if len(index_data) > 1000:
                index_data = index_data[-800:]  # Keep most recent 800 entries

            success3 = await self.redis_ops.set_async(index_key, index_data, ttl=cache_ttl)
            
            if success1 and success2 and success3:
                self._stats["embeddings_stored"] += 1
                logger.info(f"Cached semantic response for hospital {hospital_id}, category {category}")
                return True
            else:
                logger.error("Failed to store some cache components")
                return False
            
        except Exception as e:
            logger.error(f"Error caching semantic response: {e}")
            return False
        finally:
            self._stats["total_time"] += time.time() - start_time
    
    async def search_similar_async(self, query: str, hospital_id: str,
                                 category: str = "general", limit: int = 5,
                                 check_availability: bool = True) -> List[Dict[str, Any]]:
        """
        Search for semantically similar cached responses.
        Enhanced with date-aware search and availability checking.

        Args:
            query: User query text
            hospital_id: Hospital identifier
            category: Response category
            limit: Maximum number of results
            check_availability: Whether to check current availability

        Returns:
            List of similar responses with similarity scores
        """
        start_time = time.time()
        self._stats["cache_requests"] += 1
        self._stats["similarity_searches"] += 1

        try:
            # Extract date context from query
            date_context = self.date_aware_ops.extract_query_date_context(
                query, self.hospital_timezone
            )
            self._stats["date_aware_requests"] += 1

            # Generate query embedding
            query_embedding = self.embedding_manager.generate_indic_bert_embedding(query)
            if query_embedding is None:
                self._stats["cache_misses"] += 1
                return []

            # Get date-aware index of cached entries
            index_key = self._get_index_key(hospital_id, category, date_context)
            index_data = await self.redis_ops.get_async(index_key, as_json=True)

            # Also check general index if date-specific index is empty
            if not index_data:
                general_index_key = self._get_index_key(hospital_id, category)
                index_data = await self.redis_ops.get_async(general_index_key, as_json=True)
            
            if not index_data:
                self._stats["cache_misses"] += 1
                return []

            # Load embeddings and calculate similarities
            candidates = []
            for entry in index_data:
                query_hash = entry["query_hash"]
                entry_date_context = None

                # Get date context from entry if available
                if entry.get("date_context"):
                    entry_date_context = DateContext.from_dict(entry["date_context"])

                embedding_key = self._get_embedding_key(hospital_id, category, query_hash, entry_date_context)

                embedding_data = await self.redis_ops.get_async(embedding_key, as_json=False)
                if embedding_data:
                    try:
                        embedding = self._deserialize_embedding(embedding_data)
                        candidates.append((query_hash, embedding, entry_date_context))
                    except Exception as e:
                        logger.warning(f"Failed to deserialize embedding for {query_hash}: {e}")

            if not candidates:
                self._stats["cache_misses"] += 1
                return []

            # Find most similar (adjust for date-aware candidates)
            embedding_candidates = [(qh, emb) for qh, emb, _ in candidates]
            similar_entries = self.embedding_manager.find_most_similar(
                query_embedding, embedding_candidates, self.similarity_threshold
            )

            if not similar_entries:
                self._stats["cache_misses"] += 1
                return []

            # Retrieve cached responses with availability checking
            results = []
            for query_hash, similarity in similar_entries[:limit]:
                # Find the corresponding date context
                entry_date_context = None
                for qh, _, dc in candidates:
                    if qh == query_hash:
                        entry_date_context = dc
                        break

                cache_key = self._get_cache_key(hospital_id, category, query_hash, entry_date_context)
                cache_data = await self.redis_ops.get_async(cache_key, as_json=True)

                if cache_data:
                    cache_data["similarity"] = similarity

                    # Check availability if enabled and relevant
                    if check_availability and date_context and date_context.target_date:
                        availability_status = await self._check_availability_for_response(
                            cache_data, hospital_id, date_context
                        )
                        cache_data["availability_status"] = availability_status
                        self._stats["availability_checks"] += 1

                    results.append(cache_data)
            
            if results:
                self._stats["cache_hits"] += 1
                logger.info(f"Found {len(results)} similar responses for query in hospital {hospital_id}")
            else:
                self._stats["cache_misses"] += 1
            
            return results
            
        except Exception as e:
            logger.error(f"Error searching similar responses: {e}")
            self._stats["cache_misses"] += 1
            return []
        finally:
            self._stats["total_time"] += time.time() - start_time

    async def _check_availability_for_response(self, cache_data: Dict[str, Any],
                                              hospital_id: str, date_context: DateContext) -> Dict[str, Any]:
        """
        Check current availability for cached response data.

        Args:
            cache_data: Cached response data
            hospital_id: Hospital identifier
            date_context: Date context for availability check

        Returns:
            Availability status information
        """
        try:
            availability_status = {
                "checked": True,
                "available": True,
                "reason": None,
                "checked_at": time.time()
            }

            # Extract doctor/test information from metadata
            metadata = cache_data.get("metadata", {})
            doctor_id = metadata.get("doctor_id")
            test_id = metadata.get("test_id")

            if not date_context.target_date:
                return availability_status

            date_str = date_context.target_date.isoformat()

            # Check doctor availability
            if doctor_id:
                availability_key = f"availability:{hospital_id}:doctor:{doctor_id}:{date_str}"
                is_available = await self.redis_ops.get_async(availability_key, as_json=True)

                if is_available is not None:
                    availability_status["available"] = bool(is_available)
                    if not is_available:
                        availability_status["reason"] = f"Doctor not available on {date_str}"

            # Check test availability
            elif test_id:
                availability_key = f"availability:{hospital_id}:test:{test_id}:{date_str}"
                is_available = await self.redis_ops.get_async(availability_key, as_json=True)

                if is_available is not None:
                    availability_status["available"] = bool(is_available)
                    if not is_available:
                        availability_status["reason"] = f"Test not available on {date_str}"

            return availability_status

        except Exception as e:
            logger.error(f"Error checking availability: {e}")
            return {
                "checked": False,
                "available": True,  # Default to available on error
                "reason": f"Availability check failed: {str(e)}",
                "checked_at": time.time()
            }
    
    async def get_best_match_async(self, query: str, hospital_id: str,
                                 category: str = "general",
                                 check_availability: bool = True) -> Optional[Dict[str, Any]]:
        """
        Get the best matching cached response with date and availability awareness.

        Args:
            query: User query text
            hospital_id: Hospital identifier
            category: Response category
            check_availability: Whether to check current availability

        Returns:
            Best matching response or None if no good match found
        """
        similar_responses = await self.search_similar_async(
            query, hospital_id, category, limit=1, check_availability=check_availability
        )

        if similar_responses and similar_responses[0]["similarity"] >= self.similarity_threshold:
            best_match = similar_responses[0]

            # Filter out unavailable responses if availability checking is enabled
            if check_availability and "availability_status" in best_match:
                if not best_match["availability_status"].get("available", True):
                    logger.info(f"Best match filtered out due to availability: {best_match['availability_status']['reason']}")
                    return None

            return best_match

        return None

    async def cleanup_expired_date_cache(self, hospital_id: str,
                                        category: Optional[str] = None,
                                        days_to_keep: int = 7) -> int:
        """
        Clean up expired date-aware cache entries.

        Args:
            hospital_id: Hospital identifier
            category: Optional category filter
            days_to_keep: Number of days to keep (default: 7)

        Returns:
            Number of entries cleaned up
        """
        try:
            cutoff_date = datetime.now(self.date_aware_ops.default_timezone).date() - timedelta(days=days_to_keep)

            if category:
                patterns = [
                    f"semantic:indic:{hospital_id}:{category}*",
                    f"embedding:indic:{hospital_id}:{category}*",
                    f"index:indic:{hospital_id}:{category}*"
                ]
            else:
                patterns = [
                    f"semantic:indic:{hospital_id}*",
                    f"embedding:indic:{hospital_id}*",
                    f"index:indic:{hospital_id}*"
                ]

            total_cleaned = 0
            for pattern in patterns:
                cleaned = await self.date_aware_ops.cleanup_expired_dates(pattern, cutoff_date)
                total_cleaned += cleaned

            logger.info(f"Cleaned up {total_cleaned} expired date-aware cache entries for hospital {hospital_id}")
            return total_cleaned

        except Exception as e:
            logger.error(f"Error cleaning up expired date cache: {e}")
            return 0
    
    async def clear_hospital_cache_async(self, hospital_id: str, category: Optional[str] = None) -> int:
        """
        Clear cached data for a hospital.
        
        Args:
            hospital_id: Hospital identifier
            category: Optional category filter
            
        Returns:
            Number of entries cleared
        """
        try:
            if category:
                patterns = [
                    f"semantic:indic:{hospital_id}:{category}:*",
                    f"embedding:indic:{hospital_id}:{category}:*",
                    f"index:indic:{hospital_id}:{category}"
                ]
            else:
                patterns = [
                    f"semantic:indic:{hospital_id}:*",
                    f"embedding:indic:{hospital_id}:*",
                    f"index:indic:{hospital_id}:*"
                ]
            
            total_deleted = 0
            for pattern in patterns:
                keys = await self.redis_ops.keys_async(pattern)
                if keys:
                    deleted = await self.redis_ops.delete_async(*keys)
                    total_deleted += deleted
            
            logger.info(f"Cleared {total_deleted} IndicBERT cache entries for hospital {hospital_id}")
            return total_deleted
            
        except Exception as e:
            logger.error(f"Error clearing hospital cache: {e}")
            return 0
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get enhanced cache statistics including date-aware metrics.

        Returns:
            Dict with cache statistics
        """
        stats = self._stats.copy()

        if stats["cache_requests"] > 0:
            stats["hit_rate"] = stats["cache_hits"] / stats["cache_requests"]
            stats["average_time_ms"] = (stats["total_time"] / stats["cache_requests"]) * 1000
            stats["date_aware_percentage"] = (stats["date_aware_requests"] / stats["cache_requests"]) * 100
        else:
            stats["hit_rate"] = 0
            stats["average_time_ms"] = 0
            stats["date_aware_percentage"] = 0

        # Add date-aware Redis operations stats
        if hasattr(self.date_aware_ops, 'get_date_stats'):
            stats["date_operations"] = self.date_aware_ops.get_date_stats()

        stats["embedding_stats"] = self.embedding_manager.get_stats()
        stats["similarity_threshold"] = self.similarity_threshold
        stats["cache_ttl"] = self.cache_ttl
        stats["hospital_timezone"] = self.hospital_timezone

        return stats
    
    def reset_stats(self):
        """Reset cache statistics including date-aware metrics."""
        self._stats = {
            "cache_requests": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "embeddings_stored": 0,
            "similarity_searches": 0,
            "date_aware_requests": 0,
            "availability_checks": 0,
            "total_time": 0.0
        }

        # Reset date-aware operations stats
        if hasattr(self.date_aware_ops, 'reset_date_stats'):
            self.date_aware_ops.reset_date_stats()
