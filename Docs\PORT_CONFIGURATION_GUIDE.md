# Voice Agent Port Configuration Guide

## Overview

The Voice Agent system uses a **dual-port architecture** where two servers run simultaneously to handle different aspects of the application:

- **Port 8000**: FastAPI HTTP Server (Management & APIs)
- **Port 8765**: WebSocket Server (Real-time Voice Communication)

## Port Breakdown

### Port 8000 - FastAPI HTTP Server

**Purpose**: Application management, REST APIs, health checks, and configuration endpoints

**Services Provided**:
- Health check endpoints (`/health`, `/health/websocket`)
- WebSocket status and metrics (`/websocket/status`, `/websocket/metrics`)
- Jambonz application URL endpoint (`/jambonz/application-url/{hospital_id}`)
- Administrative endpoints for monitoring and control
- Static file serving and documentation

**Access Methods**:
```bash
# Health check
curl http://localhost:8000/health

# WebSocket status
curl http://localhost:8000/websocket/status

# Metrics
curl http://localhost:8000/websocket/metrics/summary
```

**NOT used for**: Voice calls, Jambonz communication, real-time audio processing

### Port 8765 - WebSocket Server

**Purpose**: Real-time communication with Jambonz for voice call handling

**Services Provided**:
- WebSocket connections for Jambonz integration
- Real-time voice call processing
- Hospital-specific call routing
- Call state management and context persistence
- Bidirectional communication with Jambonz platform

**WebSocket Endpoints**:
```
ws://localhost:8765/ws/jambonz/{hospital_id}
wss://your-domain.com:8765/ws/jambonz/{hospital_id}  # Production with SSL
```

**Message Types Handled**:
- `session:new` - New call initiation
- `verb:hook` - Voice command processing
- `call:status` - Call status updates
- `session:reconnect` - Connection recovery
- `session:redirect` - Call routing

## Jambonz Configuration

### ✅ CORRECT Configuration

Configure Jambonz applications to connect to the **WebSocket server (Port 8765)**:

```json
{
  "name": "Hospital Voice Agent",
  "call_hook": {
    "url": "wss://your-domain.com:8765/ws/jambonz/hospital_1",
    "method": "websocket"
  },
  "call_status_hook": {
    "url": "wss://your-domain.com:8765/ws/jambonz/hospital_1",
    "method": "websocket"
  }
}
```

### ❌ INCORRECT Configuration

**DO NOT** configure Jambonz to use Port 8000:
```json
{
  "call_hook": {
    "url": "http://your-domain.com:8000/webhook/call",  // WRONG!
    "method": "POST"
  }
}
```

## URL Patterns

### WebSocket URLs for Different Hospitals

```bash
# Hospital 1
wss://your-domain.com:8765/ws/jambonz/hospital_1

# Hospital 2  
wss://your-domain.com:8765/ws/jambonz/hospital_2

# Hospital 3
wss://your-domain.com:8765/ws/jambonz/hospital_3
```

### Development vs Production

**Development (Local)**:
```
ws://localhost:8765/ws/jambonz/{hospital_id}
```

**Production (SSL)**:
```
wss://your-domain.com:8765/ws/jambonz/{hospital_id}
```

## Server Coordination

### How Both Servers Work Together

1. **Single Startup Command**: Only one command starts both servers
   ```bash
   python -m uvicorn voice_agent.main:app --host 0.0.0.0 --port 8000
   ```

2. **Automatic Coordination**: FastAPI lifespan management automatically starts WebSocket server
   ```python
   # In main.py lifespan function
   await websocket_server.start()  # Starts WebSocket server on port 8765
   ```

3. **Shared Resources**: Both servers share:
   - Redis connections for caching and state management
   - Database connections for hospital configurations
   - Logging and monitoring systems
   - Error handling and recovery mechanisms

### Startup Sequence

1. FastAPI application starts on port 8000
2. Lifespan startup event triggers
3. WebSocket server automatically starts on port 8765
4. Both servers run simultaneously
5. System is ready for both management (8000) and voice calls (8765)

## Monitoring and Health Checks

### Port 8000 Monitoring Endpoints

```bash
# Overall system health
curl http://localhost:8000/health

# WebSocket server health
curl http://localhost:8000/health/websocket

# WebSocket connection status
curl http://localhost:8000/websocket/status

# Performance metrics
curl http://localhost:8000/websocket/metrics/summary

# Active connections
curl http://localhost:8000/websocket/connections
```

### Port 8765 Monitoring

WebSocket server status is monitored through Port 8000 endpoints. Direct monitoring of Port 8765:

```bash
# Test WebSocket connection (requires WebSocket client)
wscat -c ws://localhost:8765/ws/jambonz/test_hospital
```

## Troubleshooting

### Common Issues

1. **"Connection Refused on Port 8765"**
   - Check if FastAPI application started properly on port 8000
   - WebSocket server starts automatically with FastAPI
   - Review startup logs for WebSocket initialization errors

2. **"Jambonz Cannot Connect"**
   - Verify Jambonz is configured with WebSocket URL (port 8765)
   - Check firewall settings for port 8765
   - Ensure SSL certificates are properly configured for WSS

3. **"Port Already in Use"**
   ```bash
   # Check what's using the ports
   netstat -tulpn | grep :8000
   netstat -tulpn | grep :8765
   
   # Kill processes if needed
   sudo fuser -k 8000/tcp
   sudo fuser -k 8765/tcp
   ```

### Verification Steps

1. **Verify Both Servers Are Running**:
   ```bash
   # Check FastAPI server
   curl http://localhost:8000/health
   
   # Check WebSocket server status
   curl http://localhost:8000/websocket/status
   ```

2. **Test WebSocket Connectivity**:
   ```bash
   # Install wscat if not available
   npm install -g wscat
   
   # Test WebSocket connection
   wscat -c ws://localhost:8765/ws/jambonz/test_hospital
   ```

3. **Verify Jambonz Integration**:
   - Make a test call to configured phone number
   - Check logs for WebSocket message processing
   - Monitor connection metrics

## Environment Variables

```bash
# FastAPI Configuration
FASTAPI_HOST=0.0.0.0
FASTAPI_PORT=8000

# WebSocket Configuration  
WEBSOCKET_HOST=0.0.0.0
WEBSOCKET_PORT=8765
MAX_WEBSOCKET_CONNECTIONS=1000
WEBSOCKET_HEARTBEAT_INTERVAL=30.0

# Jambonz Integration
JAMBONZ_SUBPROTOCOL=ws.jambonz.org
```

## Security Considerations

### Production Deployment

1. **Use WSS (WebSocket Secure)**:
   ```json
   {
     "call_hook": {
       "url": "wss://your-domain.com:8765/ws/jambonz/{hospital_id}",
       "method": "websocket"
     }
   }
   ```

2. **Firewall Configuration**:
   - Allow inbound connections on port 8765 for Jambonz
   - Restrict port 8000 to internal management access
   - Use reverse proxy (nginx) for SSL termination

3. **SSL Certificate Setup**:
   ```bash
   # Example nginx configuration for WebSocket proxy
   location /ws/ {
       proxy_pass http://localhost:8765;
       proxy_http_version 1.1;
       proxy_set_header Upgrade $http_upgrade;
       proxy_set_header Connection "upgrade";
   }
   ```

## Quick Reference

| Purpose | Port | Protocol | Used By | Example URL |
|---------|------|----------|---------|-------------|
| Management APIs | 8000 | HTTP/HTTPS | Admins, Monitoring | `http://localhost:8000/health` |
| Voice Calls | 8765 | WebSocket | Jambonz | `ws://localhost:8765/ws/jambonz/hospital_1` |

**Remember**: 
- ✅ Start with ONE command: `uvicorn voice_agent.main:app --host 0.0.0.0 --port 8000`
- ✅ Configure Jambonz with WebSocket URL on port 8765
- ✅ Monitor both servers through port 8000 endpoints
- ❌ Never configure Jambonz to use port 8000
