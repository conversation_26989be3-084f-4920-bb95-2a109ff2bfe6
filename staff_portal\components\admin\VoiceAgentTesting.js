import { useState } from 'react';
import { CheckCircle, XCircle } from 'react-feather';

// Utility functions for accessible status indicators
const getStatusIcon = (status, type = 'general') => {
  if (type === 'test' || type === 'health') {
    return status === 'healthy' || status === true || status === 'PASS'
      ? <CheckCircle className="h-3 w-3 mr-1" aria-hidden="true" />
      : <XCircle className="h-3 w-3 mr-1" aria-hidden="true" />;
  }
  return null;
};

const getAccessibleStatusText = (status, type = 'general') => {
  if (type === 'test' || type === 'health') {
    const isHealthy = status === 'healthy' || status === true || status === 'PASS';
    return isHealthy ? 'Success' : 'Error';
  }
  return status;
};

export default function VoiceAgentTesting({
  user,
  showError,
  showSuccess
}) {
  const [testResults, setTestResults] = useState(null);
  const [testLoading, setTestLoading] = useState(false);

  // Test voice agent integration
  const testVoiceAgent = async (testType) => {
    if (!user) return;

    try {
      setTestLoading(true);
      const res = await fetch('/api/admin/test-voice-agent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          hospital_id: user.hospital_id,
          test_type: testType
        })
      });

      const data = await res.json();

      if (data.success) {
        setTestResults(data);
        showSuccess('Test Complete', `Voice agent test completed successfully. Overall status: ${data.overall_status}`);
      } else {
        showError('Test Failed', `Voice agent test failed: ${data.message}`);
        setTestResults({
          success: false,
          overall_status: 'failed',
          error: data.message
        });
      }
    } catch (error) {
      console.error('Voice agent test error:', error);
      showError('Error', 'An error occurred while testing voice agent');
      setTestResults({
        success: false,
        overall_status: 'error',
        error: error.message
      });
    } finally {
      setTestLoading(false);
    }
  };
  return (
    <div className="bg-white rounded-lg shadow overflow-hidden">
      <div className="px-6 py-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900">Voice Agent Integration Testing</h2>
        <p className="text-sm text-gray-600 mt-1">
          Test the integration between the staff portal and voice agent system to ensure everything is working correctly.
        </p>
      </div>

      <div className="px-6 py-4">
        <div className="space-y-6">
          {/* Test Controls */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-md font-medium text-gray-900 mb-4">Run Tests</h3>
            <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3">
              <button
                onClick={() => testVoiceAgent('connectivity')}
                disabled={testLoading}
                className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {testLoading ? 'Testing...' : 'Test Connectivity'}
              </button>

              <button
                onClick={() => testVoiceAgent('configuration')}
                disabled={testLoading}
                className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
              >
                {testLoading ? 'Testing...' : 'Test Configuration Sync'}
              </button>

              <button
                onClick={() => testVoiceAgent('scheduling')}
                disabled={testLoading}
                className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50"
              >
                {testLoading ? 'Testing...' : 'Test Scheduling System'}
              </button>

              <button
                onClick={() => testVoiceAgent('performance')}
                disabled={testLoading}
                className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 disabled:opacity-50"
              >
                {testLoading ? 'Testing...' : 'Test Performance'}
              </button>

              <button
                onClick={() => testVoiceAgent('all')}
                disabled={testLoading}
                className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 sm:col-span-2"
              >
                {testLoading ? 'Running All Tests...' : 'Run All Tests'}
              </button>
            </div>
          </div>

          {/* Test Results */}
          {testResults && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-md font-medium text-gray-900">Test Results</h3>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  testResults.overall_status === 'healthy'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`} role="status" aria-label={`System status: ${getAccessibleStatusText(testResults.overall_status, 'health')}`}>
                  {getStatusIcon(testResults.overall_status, 'health')}
                  <span className="sr-only">Status: </span>
                  {testResults.overall_status}
                </span>
              </div>

              <div className="space-y-4">
                {testResults.results && Object.entries(testResults.results).map(([testName, result]) => (
                  <div key={testName} className="bg-white p-3 rounded border">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-sm font-medium text-gray-900 capitalize">{testName} Test</h4>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`} role="status" aria-label={`${testName} test: ${result.success ? 'Passed' : 'Failed'}`}>
                        {getStatusIcon(result.success, 'test')}
                        <span className="sr-only">Test result: </span>
                        {result.success ? 'PASS' : 'FAIL'}
                      </span>
                    </div>

                    <div className="text-xs text-gray-600">
                      <p><strong>Status:</strong> {result.status}</p>
                      {result.error && <p><strong>Error:</strong> {result.error}</p>}
                      {result.metrics && (
                        <div className="mt-2">
                          <p><strong>Metrics:</strong></p>
                          <ul className="ml-4 list-disc">
                            {Object.entries(result.metrics).map(([key, value]) => (
                              <li key={key}>{key}: {value}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                ))}

                {testResults.error && (
                  <div className="bg-red-50 border border-red-200 rounded p-3">
                    <p className="text-sm text-red-800">
                      <strong>Error:</strong> {testResults.error}
                    </p>
                  </div>
                )}
              </div>

              <div className="mt-4 text-xs text-gray-500">
                Test completed at: {testResults.timestamp ? new Date(testResults.timestamp).toLocaleString() : 'Unknown'}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
