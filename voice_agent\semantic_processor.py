import logging
import asyncio
import time
from typing import Dict, Any, List, Optional, Tuple
from shared.redis.migration_helper import get_shared_redis_manager
redis_manager = get_shared_redis_manager()
from .fuzzy_matcher import fuzzy_matcher
from .language_config import language_config, get_primary_language, get_supported_languages
from .language_utils import contains_indian_script, detect_language_by_script, detect_language_async
import re

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("semantic_processor")

class SemanticQueryProcessor:
    """
    High-performance semantic query processor for voice agent.
    Optimized for real-time responses with sub-100ms latency.
    Enhanced with IndicBERT support for 12 Indian languages.
    """
    
    def __init__(self):
        self.cache_manager = redis_manager
        self.fuzzy_matcher = fuzzy_matcher
        
        # Common query patterns for different categories
        # Expanded to include more Indian language patterns
        raw_patterns = {
            "doctor_schedule": [
                # Hindi patterns (primary language)
                r"(?:डॉक्टर|डॉ|वैद्य)\s*([\w\s]+?)\s*(?:कब|कितने बजे)\s*(?:आते हैं|आएंगे|मिलेंगे)",
                r"([\w\s]+?)\s*(?:डॉक्टर|वैद्य)\s*(?:का समय|की टाइमिंग|कब आते हैं)",
                r"(?:कब|कितने बजे)\s*(?:डॉक्टर|डॉ)\s*([\w\s]+?)\s*(?:उपलब्ध हैं|मिलेंगे)",

                # Bengali patterns
                r"(?:ডাক্তার|ডাঃ)\s*([\w\s]+?)\s*(?:কখন|কয়টায়)\s*(?:আসেন|আসবেন|পাওয়া যাবে)",
                r"([\w\s]+?)\s*(?:ডাক্তার|চিকিৎসক)\s*(?:এর সময়|কখন আসেন)",

                # Tamil patterns
                r"(?:மருத்துவர்|டாக்டர்)\s*([\w\s]+?)\s*(?:எப்போது|எத்தனை மணிக்கு)\s*(?:வருகிறார்|கிடைப்பார்)",
                
                # Telugu patterns
                r"(?:డాక్టర్|వైద్యుడు)\s*([\w\s]+?)\s*(?:ఎప్పుడు|ఎన్ని గంటలకు)\s*(?:వస్తారు|అందుబాటులో ఉంటారు)",

                # English patterns
                r"when (?:does|will) (?:dr|doctor|dr\.)\s*([\w\s]+?)(?=\s|$)",
                r"what time (?:does|will) (?:dr|doctor|dr\.)\s*([\w\s]+?)(?=\s|$)",
                r"schedule (?:of|for) (?:dr|doctor|dr\.)\s*([\w\s]+?)(?=\s|$)",
                r"(?:dr|doctor|dr\.)\s*([\w\s]+?)\s*(?:schedule|timing|time)",
                r"([\w\s]+?) doctor (?:time|schedule|timing)",
                r"when (?:is|will be) (?:the\s+)?([\w\s]+?) (?:doctor|specialist)"
            ],
            "doctor_availability": [
                # Hindi
                r"क्या (?:डॉक्टर|डॉ)\s*([\w\s]+?)(?=\s|$) (?:उपलब्ध हैं|मौजूद हैं)",
                # Bengali
                r"(?:ডাক্তার|ডাঃ)\s*([\w\s]+?)(?=\s|$) (?:কি উপলব্ধ|আছেন)",
                # Tamil
                r"(?:மருத்துவர்|டாக்டர்)\s*([\w\s]+?)(?=\s|$) (?:இருக்கிறாரா|கிடைப்பாரா)",
                # Telugu
                r"(?:డాక్టర్|వైద్యుడు)\s*([\w\s]+?)(?=\s|$) (?:అందుబాటులో ఉన్నారా|ఉన్నారా)",
                # English
                r"is (?:dr|doctor|dr\.)\s*([\w\s]+?)(?=\s|$) (?:available|here|present)",
                r"([\w\s]+?) doctor (?:available|here|present)",
                r"can i (?:see|meet) (?:dr|doctor|dr\.)\s*([\w\s]+?)(?=\s|$)"
            ],
            "test_price": [
                # Hindi
                r"([\w\s]+?) (?:टेस्ट|परीक्षण|जांच) (?:का|की) (?:कीमत|मूल्य|शुल्क)",
                # Bengali
                r"([\w\s]+?) (?:পরীক্ষা|টেস্ট) (?:এর দাম|খরচ|ফি)",
                # Tamil
                r"([\w\s]+?) (?:பரிசோதனை|டெஸ்ட்) (?:விலை|கட்டணம்)",
                # Telugu
                r"([\w\s]+?) (?:పరీక్ష|టెస్ట్) (?:ధర|ఖర్చు|ఫీజు)",
                # English
                r"(?:price|cost|fee|charge) (?:of|for) ([\w\s]+?) (?:test|examination)",
                r"how much (?:for|is) ([\w\s]+?) (?:test|examination)",
                r"([\w\s]+?) (?:test|examination) (?:price|cost|fee)",
                r"(?:cost|price) (?:of|for) ([\w\s-]+?)(?:\s|$)",
                r"how much (?:for|is) ([\w\s-]+?)(?:\s|$)"
            ],
            "test_duration": [
                # Hindi
                r"([\w\s]+?) (?:टेस्ट|परीक्षण|जांच) में (?:कितना समय|कितना वक्त) लगता है",
                # Bengali
                r"([\w\s]+?) (?:পরীক্ষা|টেস্ট) (?:কত সময়|কতক্ষণ) লাগে",
                # Tamil
                r"([\w\s]+?) (?:பரிசோதனை|டெஸ்ட்) (?:எவ்வளவு நேரம்) ஆகும்",
                # Telugu
                r"([\w\s]+?) (?:పరీక్ష|టెస్ట్) (?:ఎంత సమయం) పడుతుంది",
                # English
                r"how long (?:does|will) ([\w\s]+?) (?:test|examination) take",
                r"duration (?:of|for) ([\w\s]+?) (?:test|examination)",
                r"([\w\s]+?) (?:test|examination) (?:time|duration)"
            ]
        }

        # Pre-compile regex patterns once during __init__ for 10-20x faster lookups
        self.query_patterns = {
            cat: [re.compile(p, flags=re.IGNORECASE) for p in pats]
            for cat, pats in raw_patterns.items()
        }
    
    async def process_query(self, query: str, hospital_id: str,
                          language: str = None, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Process user query with semantic caching and fast fallback.
        Enhanced with IndicBERT support for better Indian language understanding.

        Args:
            query: User query text
            hospital_id: Hospital identifier
            language: Language code for proper pattern matching
            context: Call context for personalization

        Returns:
            Dict containing response and metadata
        """
        start_time = time.time()
        
        try:
            # Step 1: Check if text contains Indian language script
            # This helps optimize processing for Indian language queries
            has_indian_script = contains_indian_script(query)

            # Step 1.5: Enhanced language detection for better processing
            language_info = await detect_language_async(query, hospital_id)
            detected_language = language_info.get('primary_language') or language
            
            # Step 2: Quick pattern matching for common queries
            pattern_result = await self._quick_pattern_match(query, hospital_id, detected_language)
            if pattern_result:
                processing_time = (time.time() - start_time) * 1000
                logger.info(f"Pattern match response in {processing_time:.2f}ms")
                return {
                    "response": pattern_result["response"],
                    "source": "pattern_match",
                    "confidence": pattern_result["confidence"],
                    "processing_time_ms": processing_time,
                    "language_detected": detected_language or get_primary_language(),
                    "has_indian_script": has_indian_script,
                    "language_info": language_info
                }
            
            # Step 3: Semantic cache lookup using async method
            semantic_results = await self.cache_manager.semantic_search_async(
                query, hospital_id, "general", 1
            )
            
            if semantic_results and semantic_results[0]["similarity"] > 0.85:
                processing_time = (time.time() - start_time) * 1000
                logger.info(f"Semantic cache hit in {processing_time:.2f}ms")
                return {
                    "response": semantic_results[0]["response"],
                    "source": "semantic_cache",
                    "confidence": semantic_results[0]["similarity"],
                    "processing_time_ms": processing_time,
                    "language_detected": language or get_primary_language(),
                    "has_indian_script": has_indian_script
                }
            
            # Step 4: Fuzzy matching against hospital data
            fuzzy_result = await self._fuzzy_match_hospital_data(query, hospital_id)
            if fuzzy_result:
                # Cache this result for future queries using async method
                await self.cache_manager.cache_semantic_response_async(
                    query, fuzzy_result["response"], hospital_id, "general"
                )
                
                processing_time = (time.time() - start_time) * 1000
                logger.info(f"Fuzzy match response in {processing_time:.2f}ms")
                return {
                    "response": fuzzy_result["response"],
                    "source": "fuzzy_match",
                    "confidence": fuzzy_result["confidence"],
                    "processing_time_ms": processing_time,
                    "language_detected": language or get_primary_language(),
                    "has_indian_script": has_indian_script
                }
            
            # Step 5: No match found
            processing_time = (time.time() - start_time) * 1000
            return {
                "response": language_config.get_error_message(
                    error_type="not_understood",
                    language=language or get_primary_language()
                ),
                "source": "fallback",
                "confidence": 0.0,
                "processing_time_ms": processing_time,
                "language_detected": language or get_primary_language(),
                "has_indian_script": has_indian_script
            }
            
        except Exception as e:
            logger.error(f"Error processing query: {e}")
            processing_time = (time.time() - start_time) * 1000
            return {
                "response": language_config.get_error_message(
                    error_type="technical_error",
                    language=language or get_primary_language()
                ),
                "source": "error",
                "confidence": 0.0,
                "processing_time_ms": processing_time
            }
    

    
    async def _quick_pattern_match(self, query: str, hospital_id: str, language: str = None) -> Optional[Dict[str, Any]]:
        """
        Quick pattern matching for common queries.
        Enhanced with more Indian language patterns.

        Args:
            query: User query
            hospital_id: Hospital identifier
            language: Language code for proper pattern matching

        Returns:
            Match result or None
        """
        # Default to Hindi (primary language) if not specified
        from .language_config import get_primary_language
        if not language:
            language = get_primary_language()

        # Use pre-compiled patterns for 10-20x faster lookups
        for category, compiled_patterns in self.query_patterns.items():
            for pattern in compiled_patterns:
                match = pattern.search(query)  # No need for lower() - IGNORECASE flag handles it
                if match:
                    entity = (match.group(1) or "").strip().lower()  # normalise for key matching

                    # Get cached response for this pattern and entity (language-aware)
                    cache_key = f"pattern:{hospital_id}:{language}:{category}:{entity}"
                    cached_response = await self.cache_manager.get_async(cache_key)
                    
                    if cached_response:
                        # Ensure cached_response is a string, decode if bytes
                        if isinstance(cached_response, bytes):
                            try:
                                cached_response = cached_response.decode('utf-8')
                            except Exception:
                                logger.warning(f"Could not decode cached response for key {cache_key}, returning as-is.")
                        return {
                            "response": cached_response,
                            "confidence": 0.95,
                            "entity": entity,
                            "category": category
                        }
        
        return None
    
    async def _fuzzy_match_hospital_data(self, query: str, hospital_id: str) -> Optional[Dict[str, Any]]:
        """
        Fuzzy match against hospital-specific data.
        Enhanced for better Indian language matching.
        
        Args:
            query: User query
            hospital_id: Hospital identifier
            
        Returns:
            Match result or None
        """
        try:
            # Get hospital data from cache using async methods
            doctors = await self.cache_manager.get_cached_hospital_data_async(hospital_id, "doctors")
            tests = await self.cache_manager.get_cached_hospital_data_async(hospital_id, "tests")
            
            if not doctors and not tests:
                return None
            
            # Try to match doctors
            if doctors:
                doctor_match, score = self.fuzzy_matcher.match(
                    query, doctors, "name", threshold=0.7
                )
                if doctor_match and score > 0.7:
                    response = f"Dr. {doctor_match['name']} is available from {doctor_match.get('schedule', 'regular hours')}."
                    return {
                        "response": response,
                        "confidence": score,
                        "entity": doctor_match,
                        "category": "doctor"
                    }
            
            # Try to match tests
            if tests:
                test_match, score = self.fuzzy_matcher.match(
                    query, tests, "name", threshold=0.7
                )
                if test_match and score > 0.7:
                    response = f"{test_match['name']} costs {test_match.get('price', 'standard rate')}."
                    return {
                        "response": response,
                        "confidence": score,
                        "entity": test_match,
                        "category": "test"
                    }
            
            return None
            
        except Exception as e:
            logger.error(f"Error in fuzzy matching: {e}")
            return None
    
    async def preload_common_patterns(self, hospital_id: str, hospital_data: Dict[str, Any],
                                     language: str = None) -> bool:
        """
        Preload common query patterns for a hospital.
        Enhanced with support for more Indian languages.

        Args:
            hospital_id: Hospital identifier
            hospital_data: Hospital data including doctors, tests, etc.
            language: Language code for cache keys (defaults to primary language)

        Returns:
            bool: Success or failure
        """
        # Default to Hindi (primary language) if not specified
        from .language_config import get_primary_language
        if not language:
            language = get_primary_language()

        try:
            doctors = hospital_data.get("doctors", [])
            tests = hospital_data.get("tests", [])

            # Collect all cache operations to run concurrently using async methods
            cache_tasks = []

            # Preload doctor schedule patterns
            for doctor in doctors:
                # Extract and normalize doctor name to match runtime extraction
                full_name = doctor.get("name", "")
                # Remove any form of "Dr" prefix (case-insensitive) to match what regex patterns extract
                # Handles: "Dr.", "Dr ", "DR.", "Dr", "dr.", "dr ", etc.
                name_cleaned = re.sub(r"(?i)^dr\.?\s*", "", full_name).strip().lower()
                # Use the full cleaned name, not just last name, to match regex extraction
                name = name_cleaned if name_cleaned else ""
                schedule = doctor.get("schedule", "regular hours")
                response = f"Dr. {doctor.get('name')} is available from {schedule}."

                # Set cache entry once per doctor (language-aware cache key, using async method)
                cache_key = f"pattern:{hospital_id}:{language}:doctor_schedule:{name}"
                task = self.cache_manager.set_async(cache_key, response, 86400)
                cache_tasks.append(task)

            # Preload test price patterns
            for test in tests:
                # Extract and normalize test name to match runtime extraction
                full_name = test.get("name", "")
                # Remove any form of "test" suffix (case-insensitive) to match what regex patterns extract
                # The regex patterns capture the test name before "test|examination"
                name_cleaned = re.sub(r"(?i)\s*tests?\s*$", "", full_name).strip().lower()
                # Use the full cleaned name to match regex extraction
                name = name_cleaned if name_cleaned else full_name.lower().strip()
                price = test.get("price", "standard rate")
                response = f"{test.get('name')} costs {price}."

                # Set cache entry once per test (language-aware cache key, using async method)
                cache_key = f"pattern:{hospital_id}:{language}:test_price:{name}"
                task = self.cache_manager.set_async(cache_key, response, 86400)
                cache_tasks.append(task)

            # Execute all cache operations concurrently
            if cache_tasks:
                await asyncio.gather(*cache_tasks)
            
            logger.info(f"Preloaded patterns for hospital {hospital_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error preloading patterns: {e}")
            return False

# Global instance
semantic_processor = SemanticQueryProcessor()

