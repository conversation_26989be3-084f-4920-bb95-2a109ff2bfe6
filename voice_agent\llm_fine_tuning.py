# This file has been deprecated and replaced with the unified LLM service
# All LLM functionality has been moved to shared/llm_service.py
# Fine-tuning has been replaced with function calling approach
# This file is kept for backward compatibility but should not be used

import logging

logger = logging.getLogger("llm_fine_tuning_deprecated")
logger.warning("llm_fine_tuning.py is deprecated. Use shared/llm_service.py with function calling instead.")

# Placeholder functions for backward compatibility
def complete_hospital_fine_tuning(*args, **kwargs):
    """Deprecated: Fine-tuning replaced with function calling approach"""
    logger.warning("complete_hospital_fine_tuning() is deprecated. Use shared.llm_service with function calling instead.")
    return {"success": False, "message": "Fine-tuning deprecated, use function calling"}

class GPT4oFineTuner:
    """Deprecated: Use shared.llm_service.LLMService instead"""
    def __init__(self):
        logger.warning("GPT4oFineTuner is deprecated. Use shared.llm_service.LLMService instead.")

    def is_configured(self):
        return False

# Global instance for backward compatibility
gpt4o_fine_tuner = GPT4oFineTuner()