"""
Centralized Configuration Manager for Shared Redis Implementation

Provides unified configuration management across all applications:
- Environment-based configuration with validation
- Application-specific settings
- Runtime configuration updates
- Configuration monitoring and alerts
"""

import os
import json
import logging
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, asdict
from pathlib import Path
from .config import RedisConfig, get_redis_config

logger = logging.getLogger(__name__)


@dataclass
class ApplicationConfig:
    """Configuration for a specific application."""
    name: str
    enabled: bool
    redis_db: int
    cache_ttl: int
    semantic_enabled: bool
    monitoring_enabled: bool
    optimization_enabled: bool
    custom_settings: Dict[str, Any]


@dataclass
class GlobalRedisConfig:
    """Global Redis configuration for all applications."""
    redis_url: str
    max_connections: int
    socket_timeout: float
    health_check_interval: int
    semantic_similarity_threshold: float
    embedding_dimension: int
    applications: Dict[str, ApplicationConfig]


class ConfigurationManager:
    """
    Centralized configuration manager for shared Redis implementation.
    Manages configuration across voice_agent, whatsapp_agent, and staff_portal.
    """
    
    def __init__(self, config_file: Optional[str] = None):
        """
        Initialize configuration manager.
        
        Args:
            config_file: Optional path to configuration file
        """
        self.config_file = config_file or os.environ.get(
            "REDIS_CONFIG_FILE", 
            "shared/redis/config.json"
        )
        self.config_dir = Path(self.config_file).parent
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        self._global_config: Optional[GlobalRedisConfig] = None
        self._config_cache = {}
        self._watchers = []
        
        # Load configuration
        self._load_configuration()
    
    def _get_default_application_configs(self) -> Dict[str, ApplicationConfig]:
        """Get default application configurations."""
        return {
            "voice_agent": ApplicationConfig(
                name="voice_agent",
                enabled=True,
                redis_db=0,
                cache_ttl=3600,
                semantic_enabled=True,
                monitoring_enabled=True,
                optimization_enabled=True,
                custom_settings={
                    "indic_bert_enabled": True,
                    "call_context_ttl": 600,
                    "semantic_cache_ttl": 86400,
                    "max_concurrent_calls": 100,
                    "language_support": ["hi", "en", "ta", "te", "bn", "gu", "kn", "ml", "mr", "or", "pa", "ur"]
                }
            ),
            "whatsapp_agent": ApplicationConfig(
                name="whatsapp_agent",
                enabled=True,
                redis_db=1,
                cache_ttl=1800,
                semantic_enabled=True,
                monitoring_enabled=True,
                optimization_enabled=True,
                custom_settings={
                    "multilingual_enabled": True,
                    "doctor_availability_ttl": 1800,
                    "message_cache_ttl": 3600,
                    "max_concurrent_messages": 50,
                    "language_support": ["en", "hi", "regional_english"]
                }
            ),
            "staff_portal": ApplicationConfig(
                name="staff_portal",
                enabled=True,
                redis_db=2,
                cache_ttl=7200,
                semantic_enabled=False,
                monitoring_enabled=True,
                optimization_enabled=False,
                custom_settings={
                    "booking_limit_ttl": 86400,
                    "staff_session_ttl": 3600,
                    "audit_log_ttl": 604800,  # 7 days
                    "max_concurrent_staff": 20
                }
            )
        }
    
    def _load_configuration(self):
        """Load configuration from file or create default."""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config_data = json.load(f)
                
                # Parse applications
                applications = {}
                for app_name, app_data in config_data.get("applications", {}).items():
                    applications[app_name] = ApplicationConfig(**app_data)
                
                self._global_config = GlobalRedisConfig(
                    redis_url=config_data.get("redis_url", os.environ.get("REDIS_URL", "redis://localhost:6379/0")),
                    max_connections=config_data.get("max_connections", 50),
                    socket_timeout=config_data.get("socket_timeout", 5.0),
                    health_check_interval=config_data.get("health_check_interval", 30),
                    semantic_similarity_threshold=config_data.get("semantic_similarity_threshold", 0.8),
                    embedding_dimension=config_data.get("embedding_dimension", 768),
                    applications=applications
                )
                
                logger.info(f"Loaded configuration from {self.config_file}")
            else:
                # Create default configuration
                self._create_default_configuration()
                
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            self._create_default_configuration()
    
    def _create_default_configuration(self):
        """Create default configuration."""
        try:
            redis_config = get_redis_config()
            
            self._global_config = GlobalRedisConfig(
                redis_url=redis_config.get_url(),
                max_connections=redis_config.max_connections,
                socket_timeout=redis_config.socket_timeout,
                health_check_interval=redis_config.health_check_interval,
                semantic_similarity_threshold=redis_config.semantic_similarity_threshold,
                embedding_dimension=redis_config.embedding_dimension,
                applications=self._get_default_application_configs()
            )
            
            # Save default configuration
            self.save_configuration()
            logger.info("Created default configuration")
            
        except Exception as e:
            logger.error(f"Error creating default configuration: {e}")
            raise
    
    def save_configuration(self):
        """Save current configuration to file."""
        try:
            if not self._global_config:
                raise ValueError("No configuration to save")
            
            # Convert to dictionary
            config_data = {
                "redis_url": self._global_config.redis_url,
                "max_connections": self._global_config.max_connections,
                "socket_timeout": self._global_config.socket_timeout,
                "health_check_interval": self._global_config.health_check_interval,
                "semantic_similarity_threshold": self._global_config.semantic_similarity_threshold,
                "embedding_dimension": self._global_config.embedding_dimension,
                "applications": {
                    name: asdict(config) 
                    for name, config in self._global_config.applications.items()
                }
            }
            
            # Save to file
            with open(self.config_file, 'w') as f:
                json.dump(config_data, f, indent=2)
            
            logger.info(f"Saved configuration to {self.config_file}")
            
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")
            raise
    
    def get_global_config(self) -> GlobalRedisConfig:
        """Get global Redis configuration."""
        if not self._global_config:
            self._load_configuration()
        return self._global_config
    
    def get_application_config(self, app_name: str) -> Optional[ApplicationConfig]:
        """
        Get configuration for a specific application.
        
        Args:
            app_name: Application name
            
        Returns:
            Application configuration or None if not found
        """
        global_config = self.get_global_config()
        return global_config.applications.get(app_name)
    
    def update_application_config(self, app_name: str, updates: Dict[str, Any]):
        """
        Update configuration for a specific application.
        
        Args:
            app_name: Application name
            updates: Configuration updates
        """
        try:
            global_config = self.get_global_config()
            
            if app_name not in global_config.applications:
                raise ValueError(f"Application {app_name} not found")
            
            app_config = global_config.applications[app_name]
            
            # Update fields
            for key, value in updates.items():
                if hasattr(app_config, key):
                    setattr(app_config, key, value)
                elif key == "custom_settings":
                    app_config.custom_settings.update(value)
                else:
                    app_config.custom_settings[key] = value
            
            # Save updated configuration
            self.save_configuration()
            
            # Notify watchers
            self._notify_watchers(app_name, app_config)
            
            logger.info(f"Updated configuration for {app_name}")
            
        except Exception as e:
            logger.error(f"Error updating configuration for {app_name}: {e}")
            raise
    
    def update_global_config(self, updates: Dict[str, Any]):
        """
        Update global Redis configuration.
        
        Args:
            updates: Global configuration updates
        """
        try:
            global_config = self.get_global_config()
            
            # Update fields
            for key, value in updates.items():
                if hasattr(global_config, key) and key != "applications":
                    setattr(global_config, key, value)
            
            # Save updated configuration
            self.save_configuration()
            
            # Notify watchers
            self._notify_watchers("global", global_config)
            
            logger.info("Updated global configuration")
            
        except Exception as e:
            logger.error(f"Error updating global configuration: {e}")
            raise
    
    def add_config_watcher(self, callback):
        """
        Add a configuration change watcher.
        
        Args:
            callback: Function to call when configuration changes
        """
        self._watchers.append(callback)
    
    def _notify_watchers(self, scope: str, config: Union[GlobalRedisConfig, ApplicationConfig]):
        """Notify configuration watchers of changes."""
        for watcher in self._watchers:
            try:
                watcher(scope, config)
            except Exception as e:
                logger.error(f"Error in configuration watcher: {e}")
    
    def validate_configuration(self) -> Dict[str, Any]:
        """
        Validate current configuration.
        
        Returns:
            Validation results
        """
        validation_result = {
            "valid": True,
            "warnings": [],
            "errors": [],
            "recommendations": []
        }
        
        try:
            global_config = self.get_global_config()
            
            # Validate global settings
            if global_config.max_connections < 10:
                validation_result["warnings"].append("Low max_connections setting may impact performance")
            
            if global_config.socket_timeout > 10:
                validation_result["warnings"].append("High socket_timeout may cause delays")
            
            if global_config.semantic_similarity_threshold < 0.5:
                validation_result["warnings"].append("Low similarity threshold may return irrelevant results")
            
            # Validate application settings
            for app_name, app_config in global_config.applications.items():
                if not app_config.enabled:
                    continue
                
                if app_config.cache_ttl < 300:
                    validation_result["warnings"].append(f"{app_name}: Very low cache TTL may impact performance")
                
                if app_config.semantic_enabled and not global_config.semantic_similarity_threshold:
                    validation_result["errors"].append(f"{app_name}: Semantic enabled but no similarity threshold set")
            
            # Check for Redis database conflicts
            db_usage = {}
            for app_name, app_config in global_config.applications.items():
                if app_config.enabled:
                    db = app_config.redis_db
                    if db in db_usage:
                        validation_result["errors"].append(
                            f"Redis database {db} used by both {db_usage[db]} and {app_name}"
                        )
                    else:
                        db_usage[db] = app_name
            
            if validation_result["errors"]:
                validation_result["valid"] = False
            
        except Exception as e:
            validation_result["valid"] = False
            validation_result["errors"].append(f"Configuration validation error: {e}")
        
        return validation_result
    
    def get_environment_overrides(self) -> Dict[str, str]:
        """
        Get environment variable overrides for Redis configuration.
        
        Returns:
            Dictionary of environment overrides
        """
        env_vars = {
            "REDIS_URL": "redis_url",
            "REDIS_MAX_CONNECTIONS": "max_connections",
            "REDIS_SOCKET_TIMEOUT": "socket_timeout",
            "REDIS_HEALTH_CHECK_INTERVAL": "health_check_interval",
            "REDIS_SEMANTIC_SIMILARITY_THRESHOLD": "semantic_similarity_threshold",
            "REDIS_EMBEDDING_DIMENSION": "embedding_dimension"
        }
        
        overrides = {}
        for env_var, config_key in env_vars.items():
            if env_var in os.environ:
                overrides[config_key] = os.environ[env_var]
        
        return overrides
    
    def export_configuration(self, format: str = "json") -> str:
        """
        Export configuration in specified format.
        
        Args:
            format: Export format ('json', 'yaml', 'env')
            
        Returns:
            Configuration in specified format
        """
        global_config = self.get_global_config()
        
        if format == "json":
            config_data = {
                "redis_url": global_config.redis_url,
                "max_connections": global_config.max_connections,
                "socket_timeout": global_config.socket_timeout,
                "health_check_interval": global_config.health_check_interval,
                "semantic_similarity_threshold": global_config.semantic_similarity_threshold,
                "embedding_dimension": global_config.embedding_dimension,
                "applications": {
                    name: asdict(config) 
                    for name, config in global_config.applications.items()
                }
            }
            return json.dumps(config_data, indent=2)
        
        elif format == "env":
            lines = [
                f"REDIS_URL={global_config.redis_url}",
                f"REDIS_MAX_CONNECTIONS={global_config.max_connections}",
                f"REDIS_SOCKET_TIMEOUT={global_config.socket_timeout}",
                f"REDIS_HEALTH_CHECK_INTERVAL={global_config.health_check_interval}",
                f"REDIS_SEMANTIC_SIMILARITY_THRESHOLD={global_config.semantic_similarity_threshold}",
                f"REDIS_EMBEDDING_DIMENSION={global_config.embedding_dimension}"
            ]
            
            for app_name, app_config in global_config.applications.items():
                app_prefix = f"REDIS_{app_name.upper()}"
                lines.extend([
                    f"{app_prefix}_ENABLED={str(app_config.enabled).lower()}",
                    f"{app_prefix}_DB={app_config.redis_db}",
                    f"{app_prefix}_CACHE_TTL={app_config.cache_ttl}",
                    f"{app_prefix}_SEMANTIC_ENABLED={str(app_config.semantic_enabled).lower()}"
                ])
            
            return "\n".join(lines)
        
        else:
            raise ValueError(f"Unsupported export format: {format}")


# Global configuration manager instance
_config_manager: Optional[ConfigurationManager] = None


def get_config_manager() -> ConfigurationManager:
    """
    Get global configuration manager instance.
    
    Returns:
        ConfigurationManager instance
    """
    global _config_manager
    
    if _config_manager is None:
        _config_manager = ConfigurationManager()
        logger.info("Initialized configuration manager")
    
    return _config_manager
