import { logger } from './logger';
import { withPerformanceTracking } from './performance_monitor';
import { withRateLimit } from './rate_limiter';

/**
 * API response wrapper to ensure consistent error handling and response format
 * @param {Function} handler - API request handler function
 * @param {Object} options - Options for the wrapper
 * @returns {Function} Enhanced API handler
 */
export function withApiWrapper(handler, options = {}) {
  // Create the wrapped handler
  const wrappedHandler = async (req, res) => {
    try {
      // Execute the original handler
      await handler(req, res);
      
      // If the response has already been sent, return
      if (res.writableEnded) {
        return;
      }
      
      // If we get here, the handler didn't send a response, which is an error
      logger.error(`API handler ${req.url} didn't send a response`);
      
      return res.status(500).json({
        success: false,
        message: 'Internal server error: No response was sent by the API handler'
      });
    } catch (error) {
      // Log the error
      logger.error(`API error in ${req.url}:`, error);
      
      // Don't attempt to send a response if one has already been sent
      if (res.writableEnded) {
        return;
      }
      
      // Send a standardized error response
      return res.status(500).json({
        success: false,
        message: options.exposeErrors 
          ? error.message 
          : 'Internal server error',
        ...(options.exposeErrors && process.env.NODE_ENV !== 'production' 
          ? { stack: error.stack } 
          : {})
      });
    }
  };
  
  // Apply performance tracking if enabled
  const performanceTrackedHandler = options.trackPerformance !== false
    ? withPerformanceTracking(wrappedHandler)
    : wrappedHandler;
  
  // Apply rate limiting if enabled
  return options.rateLimit !== false
    ? withRateLimit(performanceTrackedHandler, options.rateLimitOptions)
    : performanceTrackedHandler;
}

/**
 * API response success formatter
 * @param {Object} res - Express response object
 * @param {Object} data - Data to include in the response
 * @param {string} message - Success message
 * @param {number} status - HTTP status code (default: 200)
 */
export function apiSuccess(res, data = null, message = 'Success', status = 200) {
  return res.status(status).json({
    success: true,
    message,
    ...(data !== null ? { data } : {})
  });
}

/**
 * API response error formatter
 * @param {Object} res - Express response object
 * @param {string} message - Error message
 * @param {number} status - HTTP status code (default: 400)
 * @param {Object} details - Optional error details
 */
export function apiError(res, message = 'Error', status = 400, details = null) {
  return res.status(status).json({
    success: false,
    message,
    ...(details !== null ? { details } : {})
  });
}

/**
 * Input validation middleware
 * @param {Object} schema - Validation schema (e.g., Joi schema)
 * @returns {Function} Express middleware
 */
export function validateInput(schema) {
  return (req, res, next) => {
    // If no schema provided, skip validation
    if (!schema) {
      return next();
    }
    
    // Determine what to validate based on HTTP method
    let dataToValidate;
    
    switch (req.method) {
      case 'GET':
        dataToValidate = req.query;
        break;
      case 'POST':
      case 'PUT':
      case 'PATCH':
        dataToValidate = req.body;
        break;
      default:
        dataToValidate = req.body;
    }
    
    try {
      // If schema has validate method (e.g., Joi)
      if (typeof schema.validate === 'function') {
        const { error, value } = schema.validate(dataToValidate);
        
        if (error) {
          return apiError(res, 'Invalid input', 400, {
            details: error.details.map(d => d.message)
          });
        }
        
        // Replace the data with the validated data
        if (req.method === 'GET') {
          req.query = value;
        } else {
          req.body = value;
        }
      } 
      // If schema is a validator function
      else if (typeof schema === 'function') {
        const result = schema(dataToValidate);
        
        if (result !== true) {
          return apiError(res, 'Invalid input', 400, {
            details: result
          });
        }
      }
      
      return next();
    } catch (error) {
      logger.error('Validation error:', error);
      return apiError(res, 'Validation error', 400, {
        error: error.message
      });
    }
  };
}

/**
 * Combine multiple middleware functions into one
 * @param {...Function} middlewares - Middleware functions to combine
 * @returns {Function} Combined middleware
 */
export function combineMiddleware(...middlewares) {
  return async (req, res, next) => {
    // Filter out undefined or null middlewares
    const validMiddlewares = middlewares.filter(m => typeof m === 'function');
    
    if (validMiddlewares.length === 0) {
      return next();
    }
    
    // Execute middlewares in sequence
    const executeMiddleware = async (index) => {
      // If we've executed all middlewares, call next()
      if (index >= validMiddlewares.length) {
        return next();
      }
      
      // Get the current middleware
      const middleware = validMiddlewares[index];
      
      // Create a wrapper for next() that executes the next middleware
      const nextMiddleware = async () => {
        return executeMiddleware(index + 1);
      };
      
      try {
        // Execute the middleware with a custom next function
        return await middleware(req, res, nextMiddleware);
      } catch (error) {
        logger.error(`Error in middleware ${index}:`, error);
        
        // If error and response has not been sent, send error response
        if (!res.writableEnded) {
          return apiError(res, 'Middleware error', 500, {
            error: process.env.NODE_ENV !== 'production' ? error.message : 'Internal error'
          });
        }
      }
    };
    
    // Start executing middlewares
    return executeMiddleware(0);
  };
}