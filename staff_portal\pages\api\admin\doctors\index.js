import { withRole } from '../../../../lib/auth';
import { createDoctor } from '../../../../lib/firebase';

// Only admin users can manage doctors
export default withRole(async (req, res) => {
  // POST - Create new doctor
  if (req.method === 'POST') {
    try {
      const { hospitalId, doctorData } = req.body;
      
      // Validate required parameters
      if (!hospitalId || !doctorData) {
        return res.status(400).json({
          success: false,
          message: 'Hospital ID and doctor data are required'
        });
      }
      
      // Verify hospital ID matches the authenticated user's hospital
      if (hospitalId !== req.user.hospital_id) {
        return res.status(403).json({
          success: false,
          message: 'You are not authorized to create doctors for this hospital'
        });
      }
      
      // Validate required doctor fields
      if (!doctorData.name || !doctorData.specialty || !doctorData.phone) {
        return res.status(400).json({
          success: false,
          message: 'Name, specialty, and phone are required'
        });
      }
      
      // Prepare doctor data for creating in Firestore
      const doctorDataToCreate = {
        ...doctorData,
        hospital_id: hospitalId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      // Create doctor in Firestore
      const result = await createDoctor(doctorDataToCreate);
      
      if (result.success) {
        return res.status(201).json({
          success: true,
          message: 'Doctor created successfully',
          id: result.id
        });
      } else {
        return res.status(500).json({
          success: false,
          message: result.error || 'Failed to create doctor'
        });
      }
    } catch (error) {
      console.error('Create doctor error:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
  
  // Method not allowed
  return res.status(405).json({
    success: false,
    message: 'Method not allowed'
  });
}, ['admin']); // Only admin role can access this endpoint
