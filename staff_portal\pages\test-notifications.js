import React from 'react';
import Layout from '../components/Layout';
import { AvailabilityManagement } from '../components/AvailabilityManagement';

/**
 * Test page for notification system
 * This page can be used to test the error notification functionality
 */
export default function TestNotifications() {
  // Mock hospital ID for testing
  const mockHospitalId = 'test-hospital-123';
  
  return (
    <Layout title="Test Notifications">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Notification System Test
          </h1>
          <p className="text-gray-600">
            This page demonstrates the error notification system in the Availability Management component.
            Try changing the date or making changes to see notifications in action.
          </p>
        </div>
        
        <AvailabilityManagement 
          hospitalId={mockHospitalId}
          userRole="admin"
        />
      </div>
    </Layout>
  );
}
