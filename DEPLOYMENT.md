# Ascle AI Healthcare Management System - Deployment Guide

This document provides detailed instructions for deploying the Ascle AI Healthcare Management System in various environments.

## Table of Contents
- [Local Development Deployment](#local-development-deployment)
- [PostgreSQL Database Setup](#postgresql-database-setup)
- [Redis Setup](#redis-setup)
- [Firebase Configuration](#firebase-configuration)
- [OpenAI API Configuration](#openai-api-configuration)
- [Jambonz Configuration](#jambonz-configuration)
- [Multi-Hospital Setup](#multi-hospital-setup)
- [Production Deployment](#production-deployment)
- [Admin and Staff User Creation](#admin-and-staff-user-creation)
- [Testing Telephony Features](#testing-telephony-features)
- [Monitoring and Maintenance](#monitoring-and-maintenance)
- [Backup and Recovery](#backup-and-recovery)

## Local Development Deployment

### Prerequisites
- [Node.js 18+](https://nodejs.org/)
- [Python 3.11+](https://www.python.org/downloads/)
- [PostgreSQL 13+](https://www.postgresql.org/download/)
- [Redis 6+](https://redis.io/download)
- [Git](https://git-scm.com/downloads)

### 1. Clone the Repository
```bash
git clone https://github.com/yourusername/ascle-ai.git
cd ascle-ai
```

### 2. Voice Agent Setup
```bash
# Navigate to voice agent directory
cd voice_agent

# Create a virtual environment
python -m venv venv

# Activate the virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Create .env file
cp .env.example .env
# Edit .env with your configuration values
```

### 3. Staff Portal Setup
```bash
# Navigate to staff portal directory
cd ../staff_portal

# Install dependencies
npm install

# Create .env.local file
cp .env.example .env.local
# Edit .env.local with your configuration values

# Start development server
npm run dev
```

## PostgreSQL Database Setup

### 1. Install PostgreSQL
- **Windows**: Download and install from [PostgreSQL website](https://www.postgresql.org/download/windows/)
- **macOS**: Using Homebrew: `brew install postgresql`
- **Linux (Ubuntu)**: `sudo apt update && sudo apt install postgresql postgresql-contrib`

### 2. Create Database and User
```sql
-- Connect to PostgreSQL
psql -U postgres

-- Create database
CREATE DATABASE hospital_db;

-- Create user with password
CREATE USER hospital_user WITH PASSWORD 'your_secure_password';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE hospital_db TO hospital_user;

-- Connect to the new database
\c hospital_db

-- Enable extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
```

### 3. Initialize Database Schema

For Staff Portal:
```bash
cd staff_portal

# Create the database initialization script
cat > scripts/init-db.js << 'EOF'
const { query } = require('../lib/db');

async function initDatabase() {
  try {
    // Create staff_users table
    await query(`
      CREATE TABLE IF NOT EXISTS staff_users (
        id SERIAL PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        name VARCHAR(100) NOT NULL,
        role VARCHAR(50) NOT NULL,
        email VARCHAR(100),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    // Create other tables as defined in lib/db.js
    
    console.log('Database initialized successfully');
  } catch (error) {
    console.error('Failed to initialize database:', error);
  }
}

initDatabase();
EOF

# Run the initialization script
node scripts/init-db.js
```

For Voice Agent:
```bash
cd voice_agent

# Create the database initialization script
cat > init_db.py << 'EOF'
import asyncio
import os
from database import execute_query

async def init_database():
    # Define schema creation queries
    queries = [
        """
        CREATE TABLE IF NOT EXISTS hospitals (
            id VARCHAR(50) PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            location TEXT,
            emergency_number VARCHAR(20),
            settings JSONB,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        )
        """,
        """
        CREATE TABLE IF NOT EXISTS doctors (
            id SERIAL PRIMARY KEY,
            hospital_id VARCHAR(50) REFERENCES hospitals(id),
            name VARCHAR(100) NOT NULL,
            specialty VARCHAR(100) NOT NULL,
            schedule JSONB,
            availability JSONB,
            email VARCHAR(100),
            phone VARCHAR(20),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        )
        """,
        # Add other tables as needed
    ]
    
    # Execute each query
    for query in queries:
        await execute_query("default", query)
    
    print("Database initialized successfully")

if __name__ == "__main__":
    asyncio.run(init_database())
EOF

# Run the initialization script
python init_db.py
```

## Redis Setup

### 1. Install Redis
- **Windows**: Use [Redis for Windows](https://github.com/microsoftarchive/redis/releases) or WSL
- **macOS**: Using Homebrew: `brew install redis`
- **Linux (Ubuntu)**: `sudo apt update && sudo apt install redis-server`

### 2. Configure Redis
Edit the Redis configuration file (usually at `/etc/redis/redis.conf` on Linux/macOS):

```
# Set password (recommended)
requirepass your_secure_redis_password

# Bind to localhost only for development
bind 127.0.0.1

# Basic persistence settings
appendonly yes
appendfsync everysec
```

### 3. Start Redis
```bash
# On Linux/macOS with systemd
sudo systemctl start redis

# On macOS with Homebrew
brew services start redis

# On Windows
redis-server.exe
```

### 4. Test Redis Connection
```bash
# Connect using redis-cli
redis-cli

# If password is set
auth your_secure_redis_password

# Test with ping
ping
# Should return PONG
```

## Firebase Configuration

### 1. Create Firebase Project
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Add project"
3. Enter project name (e.g., "Ascle AI Hospital System")
4. Configure Google Analytics (optional)
5. Click "Create project"

### 2. Set Up Authentication
1. In Firebase Console, go to "Authentication" in the left sidebar
2. Click "Get started"
3. Select "Google" provider
4. Enable it and configure settings
5. Click "Save"

### 3. Add Web App to Your Project
1. Click the gear icon next to "Project Overview" and select "Project settings"
2. In the "Your apps" section, click the web icon (`</>`)
3. Register app with a nickname (e.g., "Staff Portal")
4. Copy the configuration object containing `apiKey`, `projectId`, and `appId`

### 4. Configure Authorized Domains
1. Go to "Authentication" > "Settings" tab
2. Under "Authorized domains", add your domains:
   - For local development: `localhost`
   - For production: Your actual domain(s)

### 5. Update Staff Portal Environment Variables
Add the Firebase configuration to your `.env.local` file:
```
FIREBASE_API_KEY=your_firebase_api_key
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_APP_ID=your_firebase_app_id
```

## OpenAI API Configuration

### 1. Create OpenAI Account
1. Go to [OpenAI](https://platform.openai.com/signup)
2. Create an account or sign in

### 2. Generate API Key
1. Go to [API Keys](https://platform.openai.com/api-keys)
2. Click "Create new secret key"
3. Name your key (e.g., "Ascle AI Voice Agent")
4. Copy and save the key securely

### 3. Update Voice Agent Environment Variables
Add the OpenAI API key to your `.env` file:
```
OPENAI_API_KEY=your_openai_api_key
```

### 4. Test OpenAI API
```python
from voice_agent.llm_fine_tuning import gpt4o_fine_tuner

# Validate API key
result = gpt4o_fine_tuner.validate_api_key()
print(result)
```

## Jambonz Configuration

Jambonz is used for production telephony services. Follow these steps to set up Jambonz:

### 1. Create Jambonz Account
1. Go to [Jambonz Cloud](https://jambonz.cloud/)
2. Sign up for an account
3. Choose a region closest to your users

### 2. Get API Credentials
1. Log in to your Jambonz account
2. Navigate to your account settings
3. Copy your API key and Account SID

### 3. Configure WebHook Application
1. Go to "Applications" in the Jambonz dashboard
2. Click "Create Application"
3. Select "WebHook" as the application type
4. Enter your voice agent's webhook URL:
   - For local testing with ngrok: `https://your-ngrok-url.ngrok.io/webhook`
   - For production: `https://your-domain.com/webhook`
5. Configure call status webhooks as needed

### 4. Provision Phone Numbers
1. Go to "Phone Numbers" in the Jambonz dashboard
2. Provision a new number or port an existing one
3. Assign the number to your WebHook application

### 5. Update Voice Agent Environment Variables
Add Jambonz configuration to your `.env` file:
```
JAMBONZ_API_KEY=your_jambonz_api_key
JAMBONZ_ACCOUNT_SID=your_jambonz_account_sid
JAMBONZ_API_URL=https://api.jambonz.cloud/v1
```

### 6. Configure SIP Trunks for Multiple Hospitals
Use the Jambonz service to create SIP trunks for each hospital:

```python
from voice_agent.jambonz_service import jambonz_service

# Create SIP trunk for a hospital
result = jambonz_service.create_sip_trunk(
    name="Hospital Name",
    service_provider="Provider Name",
    register_username="sip_username",
    register_password="sip_password",
    register_sip_realm="sip.example.com",
    register_from_user="hospital_id",
    register_from_domain="sip.example.com"
)

# Store the trunk ID in your hospital configuration
trunk_id = result["trunk_details"]["sid"]
print(f"SIP Trunk created with ID: {trunk_id}")
```

## Multi-Hospital Setup

### 1. Create Hospital Configuration File
Create a JSON configuration file `hospital_config.json` in the `voice_agent` directory:

```json
{
  "hospitals": [
    {
      "id": "hospital1",
      "name": "General Hospital",
      "location": "123 Main St, City",
      "emergency_number": "911",
      "database": {
        "host": "db.hospital1.com",
        "port": 5432,
        "database": "hospital1_db",
        "user": "hospital1_user",
        "password": "password1",
        "ssl": true,
        "ssh_tunnel": {
          "user": "ssh_user",
          "host": "ssh.hospital1.com",
          "port": 22,
          "private_key_path": "/path/to/private_key.pem",
          "remote_host": "localhost",
          "remote_port": 5432
        }
      },
      "jambonz": {
        "trunk_id": "tr_abc123",
        "phone_number": "+15551234567"
      }
    },
    {
      "id": "hospital2",
      "name": "City Medical Center",
      "location": "456 Oak Ave, Town",
      "emergency_number": "911",
      "database": {
        "host": "db.hospital2.com",
        "port": 5432,
        "database": "hospital2_db",
        "user": "hospital2_user",
        "password": "password2",
        "ssl": true
      },
      "jambonz": {
        "trunk_id": "tr_def456",
        "phone_number": "+15559876543"
      }
    }
  ]
}
```

### 2. Initialize Multi-Hospital Databases
Create an initialization script:

```python
# init_hospitals.py
import asyncio
import json
from voice_agent.multi_hospital_db import initialize_hospital_databases

async def init():
    # Initialize from configuration file
    result = await initialize_hospital_databases('hospital_config.json')
    print(f"Initialization result: {result}")

if __name__ == "__main__":
    asyncio.run(init())
```

Run the script:
```bash
python init_hospitals.py
```

### 3. Test Database Connections
Create a test script to verify database connections:

```python
# test_hospital_connections.py
import asyncio
from voice_agent.multi_hospital_db import hospital_db_manager

async def test_connections():
    # Get list of registered hospitals
    hospitals = await hospital_db_manager.list_hospitals()
    print(f"Registered hospitals: {hospitals}")
    
    # Test each connection
    for hospital_id in hospitals:
        result = await hospital_db_manager.test_connection(hospital_id)
        print(f"Hospital {hospital_id}: {result}")

if __name__ == "__main__":
    asyncio.run(test_connections())
```

Run the test script:
```bash
python test_hospital_connections.py
```

## Production Deployment

### Voice Agent (FastAPI Application)

#### 1. Server Requirements
- Ubuntu 20.04 LTS or newer
- Python 3.11+
- PostgreSQL 13+
- Redis 6+
- Nginx
- Supervisor or Systemd

#### 2. Deploy Voice Agent
```bash
# Clone repository
git clone https://github.com/yourusername/ascle-ai.git
cd ascle-ai/voice_agent

# Create virtual environment
python -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Create .env file with production settings
cp .env.example .env
# Edit .env with production values
```

#### 3. Set Up Gunicorn Service
Create a systemd service file:

```bash
sudo nano /etc/systemd/system/voice-agent.service
```

Add the following content:
```ini
[Unit]
Description=Ascle AI Voice Agent
After=network.target

[Service]
User=ubuntu
Group=ubuntu
WorkingDirectory=/path/to/ascle-ai/voice_agent
Environment="PATH=/path/to/ascle-ai/voice_agent/venv/bin"
EnvironmentFile=/path/to/ascle-ai/voice_agent/.env
ExecStart=/path/to/ascle-ai/voice_agent/venv/bin/gunicorn -k uvicorn.workers.UvicornWorker -w 4 -b 127.0.0.1:8000 main:app
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

Start the service:
```bash
sudo systemctl enable voice-agent
sudo systemctl start voice-agent
```

#### 4. Configure Nginx
```bash
sudo nano /etc/nginx/sites-available/voice-agent
```

Add the following content:
```nginx
server {
    listen 80;
    server_name voice-agent.yourdomain.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

Enable the site:
```bash
sudo ln -s /etc/nginx/sites-available/voice-agent /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

#### 5. Set Up SSL with Let's Encrypt
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d voice-agent.yourdomain.com
```

### Staff Portal (Next.js Application)

#### 1. Server Requirements
- Ubuntu 20.04 LTS or newer
- Node.js 18+
- PM2 or similar process manager
- Nginx

#### 2. Deploy Staff Portal
```bash
# Clone repository
git clone https://github.com/yourusername/ascle-ai.git
cd ascle-ai/staff_portal

# Install dependencies
npm install

# Create .env.local file with production settings
cp .env.example .env.local
# Edit .env.local with production values

# Build the application
npm run build
```

#### 3. Set Up PM2
```bash
# Install PM2 globally
npm install -g pm2

# Start the application with PM2
pm2 start npm --name "staff-portal" -- start

# Save PM2 configuration
pm2 save

# Set up PM2 to start on boot
pm2 startup
# Follow the instructions output by the above command
```

#### 4. Configure Nginx
```bash
sudo nano /etc/nginx/sites-available/staff-portal
```

Add the following content:
```nginx
server {
    listen 80;
    server_name staff-portal.yourdomain.com;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

Enable the site:
```bash
sudo ln -s /etc/nginx/sites-available/staff-portal /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

#### 5. Set Up SSL with Let's Encrypt
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d staff-portal.yourdomain.com
```

## Admin and Staff User Creation

### Create Admin User
Create a script to add an admin user to the database:

```javascript
// staff_portal/scripts/create-admin.js
const { query } = require('../lib/db');
const { hashPassword } = require('../lib/auth');
const yargs = require('yargs');

async function createAdmin() {
  const argv = yargs
    .option('username', {
      alias: 'u',
      description: 'Admin username',
      type: 'string',
      demandOption: true
    })
    .option('password', {
      alias: 'p',
      description: 'Admin password',
      type: 'string',
      demandOption: true
    })
    .option('name', {
      alias: 'n',
      description: 'Admin name',
      type: 'string',
      demandOption: true
    })
    .option('email', {
      alias: 'e',
      description: 'Admin email',
      type: 'string',
      demandOption: false
    })
    .help()
    .argv;

  try {
    // Hash the password
    const hashedPassword = await hashPassword(argv.password);
    
    // Check if user already exists
    const checkResult = await query(
      'SELECT id FROM staff_users WHERE username = $1',
      [argv.username]
    );
    
    if (checkResult.rows.length > 0) {
      console.error(`User with username '${argv.username}' already exists.`);
      process.exit(1);
    }
    
    // Insert admin user
    const result = await query(
      `INSERT INTO staff_users (username, password_hash, name, role, email) 
       VALUES ($1, $2, $3, $4, $5) RETURNING id`,
      [argv.username, hashedPassword, argv.name, 'admin', argv.email || null]
    );
    
    console.log(`Admin user created with ID: ${result.rows[0].id}`);
    process.exit(0);
  } catch (error) {
    console.error('Failed to create admin user:', error);
    process.exit(1);
  }
}

createAdmin();
```

Run the script:
```bash
cd staff_portal
node scripts/create-admin.js --username admin --password secure_password --name "Admin User" --email <EMAIL>
```

### Add Staff Members
Once logged in as an admin, you can add staff members through the staff portal interface:

1. Log in as admin
2. Navigate to "Staff Management"
3. Click "Add Staff Member"
4. Fill in the details:
   - Username
   - Password
   - Name
   - Role (doctor, receptionist, lab_technician, nurse)
   - Email (optional)
5. Click "Save"

## Testing Telephony Features

### 1. Twilio Testing (Development Only)
Ensure you have configured Twilio in your `.env` file:
```
ENABLE_TWILIO_TESTING=true
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number
```

Create a test script:
```python
# test_twilio.py
import os
from voice_agent.twilio_config import twilio_service

def test_sms():
    # Test sending an SMS
    result = twilio_service.send_sms(
        to_number="+**********",  # Replace with your phone number
        message="Hello from Ascle AI! This is a test message."
    )
    print(f"SMS result: {result}")

def test_call():
    # Generate TwiML for test call
    twiml = twilio_service.get_test_twiml("This is a test call from Ascle AI.")
    
    # Save TwiML to a file that can be served via URL
    with open("test_twiml.xml", "w") as f:
        f.write(twiml)
    
    # For this test, you would need to host this XML file at a public URL
    # or use a tool like ngrok to expose your local server
    
    # Make test call - replace with actual URL to your TwiML
    result = twilio_service.make_test_call(
        to_number="+**********",  # Replace with your phone number
        url="https://your-ngrok-url.ngrok.io/test_twiml.xml"
    )
    print(f"Call result: {result}")

if __name__ == "__main__":
    # Test Twilio configuration
    config_valid = twilio_service.validate_twilio_config()
    print(f"Twilio config valid: {config_valid}")
    
    if config_valid.get("valid", False):
        # Run tests
        test_sms()
        # test_call()  # Uncomment once you have a public URL for TwiML
    else:
        print("Twilio is not properly configured.")
```

Run the script:
```bash
python test_twilio.py
```

### 2. Jambonz Testing (Production)
Create a test script:
```python
# test_jambonz.py
import os
from voice_agent.jambonz_service import jambonz_service

def test_jambonz_configuration():
    # Validate credentials
    result = jambonz_service.validate_credentials()
    print(f"Jambonz credentials valid: {result}")
    
    if not result.get("valid", False):
        return
    
    # List active calls
    calls = jambonz_service.list_active_calls()
    print(f"Active calls: {calls}")
    
    # List SIP trunks
    trunks = jambonz_service.list_sip_trunks()
    print(f"SIP trunks: {trunks}")

if __name__ == "__main__":
    test_jambonz_configuration()
```

Run the script:
```bash
python test_jambonz.py
```

## Monitoring and Maintenance

### 1. Log Monitoring
Configure log rotation for both applications:

For Voice Agent:
```bash
sudo nano /etc/logrotate.d/voice-agent
```

Add:
```
/path/to/ascle-ai/voice_agent/logs/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 0640 ubuntu ubuntu
}
```

For Staff Portal:
```bash
sudo nano /etc/logrotate.d/staff-portal
```

Add:
```
/path/to/ascle-ai/staff_portal/logs/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 0640 ubuntu ubuntu
}
```

### 2. Database Maintenance
Set up regular database maintenance:

```bash
sudo nano /etc/cron.d/postgresql-maintenance
```

Add:
```
# Run VACUUM ANALYZE daily at 3:00 AM
0 3 * * * postgres /usr/bin/psql -d hospital_db -c "VACUUM ANALYZE;"

# Run full maintenance weekly on Sunday at 4:00 AM
0 4 * * 0 postgres /usr/bin/psql -d hospital_db -c "VACUUM FULL; ANALYZE;"
```

### 3. Performance Monitoring
Set up basic monitoring with Prometheus and Grafana, or use a service like New Relic or Datadog.

## Backup and Recovery

### 1. Database Backups
Set up automatic PostgreSQL backups:

```bash
sudo nano /etc/cron.d/pg-backups
```

Add:
```
# Daily backup at 2:00 AM
0 2 * * * postgres /usr/bin/pg_dump -Fc hospital_db > /path/to/backups/hospital_db_$(date +\%Y\%m\%d).dump

# Keep only the last 30 daily backups
0 3 * * * root find /path/to/backups/ -name "hospital_db_*.dump" -type f -mtime +30 -delete
```

### 2. Application Backups
Set up regular backups of application code and configuration:

```bash
sudo nano /etc/cron.d/app-backups
```

Add:
```
# Weekly backup of application files on Sunday at 1:00 AM
0 1 * * 0 root tar -czf /path/to/backups/ascle-ai-$(date +\%Y\%m\%d).tar.gz -C /path/to/ ascle-ai
```

### 3. Recovery Testing
Regularly test recovery procedures:

1. Create a test environment
2. Restore database from backup:
   ```bash
   pg_restore -d hospital_db_test /path/to/backups/hospital_db_YYYYMMDD.dump
   ```
3. Restore application code:
   ```bash
   tar -xzf /path/to/backups/ascle-ai-YYYYMMDD.tar.gz -C /path/to/test/
   ```
4. Test functionality

## Conclusion

This deployment guide should help you set up the Ascle AI Healthcare Management System in various environments. For additional help or customization, please contact <NAME_EMAIL>.

---

© 2025 Ascle AI. All rights reserved.