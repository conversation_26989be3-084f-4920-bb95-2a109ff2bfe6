import { withAuth } from '../../../lib/auth';
import { sendAppointmentReminder } from '../../../lib/db';

export default withAuth(async (req, res) => {
  // Only allow POST method
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  try {
    const { doctorId, method, message } = req.body;
    const hospitalId = req.user.hospital_id;

    if (!doctorId || !method) {
      return res.status(400).json({
        success: false,
        message: 'Doctor ID and reminder method are required'
      });
    }

    // Validate method
    if (!['sms', 'email', 'call'].includes(method)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid reminder method. Use sms, email, or call.'
      });
    }

    // Get all of today's appointments for this doctor
    const dayStart = new Date();
    dayStart.setHours(0, 0, 0, 0);
    const dayEnd = new Date();
    dayEnd.setHours(23, 59, 59, 999);

    // Query database for appointments
    const result = await sendAppointmentReminder(doctorId, method, message, hospitalId);

    if (result.success) {
      return res.status(200).json({
        success: true,
        message: 'Reminders sent successfully',
        data: result.data
      });
    } else {
      return res.status(500).json({
        success: false,
        message: result.error || 'Failed to send reminders'
      });
    }
  } catch (error) {
    console.error('Send reminders error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});