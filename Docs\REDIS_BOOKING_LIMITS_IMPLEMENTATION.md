# Redis-Based Booking Limit System Implementation

## Overview

This document describes the implementation of the Redis-based booking limit system for the voice agent. The system provides ultra-fast booking availability checks and automatic daily cache refresh to minimize Firebase queries and reduce latency.

## Architecture

### Key Components

1. **Redis Cache Manager** (`cache_manager.py`)
   - Booking limit caching methods
   - Atomic counter operations
   - Fast availability checking

2. **Booking Limit Scheduler** (`booking_limit_scheduler.py`)
   - Daily cache refresh at 12 PM
   - Firebase data synchronization
   - Next available date calculation

3. **Voice Agent Integration** (`main.py`)
   - Redis-first availability checking
   - Real-time counter updates
   - Fallback to Firebase/PostgreSQL

## Redis Key Structure

### Booking Limits
```
booking_limits:{hospital_id}:{doctor_id}:daily_limits
```
**Value**: JSON object with day-specific limits
```json
{
  "monday": 12,
  "tuesday": 12,
  "wednesday": 6,
  "thursday": 12,
  "friday": 12,
  "saturday": 0,
  "sunday": 0
}
```

### Booking Counters
```
booking_counter:{hospital_id}:{doctor_id}:{date}
```
**Value**: Integer counter (auto-expires at end of day)
**Example**: `booking_counter:456:doc_001:2025-01-15` → `5`

### Next Available Dates
```
booking_limits:{hospital_id}:{doctor_id}:next_available
```
**Value**: Date string in YYYY-MM-DD format
**Example**: `2025-01-20`

## Daily Refresh Process

### Schedule
- **Time**: 12:00 PM daily
- **Trigger**: Automatic via `BookingLimitScheduler`
- **Manual**: Available via API endpoint

### Process Flow
1. **Query Firebase** for all hospitals and doctors
2. **Extract booking limits** from doctor documents
3. **Calculate next available dates** based on current bookings
4. **Cache in Redis** with 24-hour TTL
5. **Log results** for monitoring

### Data Sources
- **Daily Limits**: `doctor.daily_booking_limits` from Firebase
- **Current Bookings**: Redis counters + PostgreSQL verification
- **Doctor Schedules**: `doctor.schedule` from Firebase

## Voice Agent Integration

### Booking Flow
1. **User selects doctor** → Voice agent checks Redis availability
2. **If available** → Proceed with time selection
3. **If not available** → Offer next available date from cache
4. **On successful booking** → Increment Redis counter + update Firebase

### Performance Benefits
- **Sub-millisecond** availability checks
- **No Firebase queries** during peak hours
- **Automatic fallback** if Redis unavailable
- **Real-time counter updates**

## API Endpoints

### Manual Refresh
```http
POST /api/booking-limits/refresh
Content-Type: application/json

{
  "hospital_id": "456"  // Optional: specific hospital
}
```

### Status Check
```http
GET /api/booking-limits/status/{hospital_id}/{doctor_id}?date=2025-01-15
```

**Response**:
```json
{
  "hospital_id": "456",
  "doctor_id": "doc_001",
  "date": "2025-01-15",
  "availability": {
    "is_available": false,
    "current_count": 12,
    "limit": 12,
    "next_available_date": "2025-01-20"
  },
  "timestamp": "2025-01-15T14:30:00"
}
```

## Error Handling

### Redis Unavailable
- **Fallback**: Query Firebase/PostgreSQL directly
- **Behavior**: System continues to work (slower)
- **Logging**: Errors logged for monitoring

### Firebase Unavailable
- **Fallback**: Use cached Redis data
- **Behavior**: May have stale limits (acceptable)
- **Recovery**: Auto-retry on next refresh cycle

### Counter Inconsistency
- **Detection**: Periodic verification against PostgreSQL
- **Correction**: Manual refresh API can resync
- **Prevention**: Atomic Redis operations

## Performance Metrics

### Expected Performance
- **Availability Check**: < 1ms (Redis lookup)
- **Counter Increment**: < 2ms (Redis atomic operation)
- **Daily Refresh**: < 30 seconds for 100 doctors
- **Memory Usage**: ~1KB per doctor per day

### Monitoring
- **Redis Hit Rate**: Should be > 95%
- **Refresh Success Rate**: Should be > 99%
- **Counter Accuracy**: Verified daily
- **Response Times**: Logged for analysis

## Configuration

### Environment Variables
```bash
REDIS_URL=redis://localhost:6379/0
BOOKING_REFRESH_TIME=12:00  # Optional: default 12:00 PM
```

### Firebase Structure
Doctor documents must include:
```json
{
  "daily_booking_limits": {
    "monday": 12,
    "tuesday": 12,
    "wednesday": 6,
    "thursday": 12,
    "friday": 12,
    "saturday": 0,
    "sunday": 0
  },
  "current_bookings": {},
  "next_available_dates": []
}
```

## Testing

### Test Script
Run comprehensive tests:
```bash
cd voice_agent
python test_redis_booking_limits.py
```

### Test Coverage
- ✅ Daily limits caching
- ✅ Counter operations
- ✅ Availability checking
- ✅ Limit reached scenarios
- ✅ Performance testing
- ✅ Error handling
- ✅ Voice agent integration

## Deployment Considerations

### Production Setup
1. **Redis Persistence**: Enable RDB snapshots
2. **Redis Clustering**: For high availability
3. **Monitoring**: Set up Redis metrics
4. **Backup**: Daily Redis data backup
5. **Scaling**: Horizontal Redis scaling if needed

### Security
- **Redis Auth**: Enable Redis authentication
- **Network**: Secure Redis network access
- **Encryption**: Use Redis TLS in production
- **Access Control**: Limit Redis command access

## Troubleshooting

### Common Issues
1. **Stale Counters**: Run manual refresh
2. **Missing Limits**: Check Firebase doctor data
3. **High Latency**: Verify Redis connection
4. **Inconsistent Data**: Compare Redis vs PostgreSQL

### Debug Commands
```bash
# Check Redis keys
redis-cli KEYS "booking_*"

# Get doctor limits
redis-cli GET "booking_limits:456:doc_001:daily_limits"

# Get booking counter
redis-cli GET "booking_counter:456:doc_001:2025-01-15"
```

## Future Enhancements

### Planned Features
- **Multi-day booking limits** (weekly/monthly)
- **Dynamic limit adjustment** based on demand
- **Predictive availability** using ML
- **Real-time notifications** for limit changes
- **Advanced analytics** dashboard

### Optimization Opportunities
- **Redis Lua scripts** for complex operations
- **Batch operations** for multiple doctors
- **Compression** for large datasets
- **Caching strategies** optimization
