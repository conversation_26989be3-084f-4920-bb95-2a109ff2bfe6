import { useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';

export default function LandingPage() {
  const router = useRouter();

  // Redirect to login page on component mount
  useEffect(() => {
    // Check if already authenticated
    const checkAuth = async () => {
      try {
        const res = await fetch('/api/auth/status');
        const data = await res.json();
        
        if (data.success) {
          // If already authenticated, redirect to dashboard
          router.push('/dashboard');
        } else {
          // Otherwise, redirect to login
          router.push('/login');
        }
      } catch (error) {
        // Error means not authenticated, redirect to login
        console.error('Auth check error:', error);
        router.push('/login');
      }
    };
    
    checkAuth();
  }, [router]);

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center items-center">
      <Head>
        <title>Voice Health Portal - Staff Access</title>
      </Head>
      
      <div className="text-center max-w-md px-4 py-8 bg-white rounded-lg shadow-sm border border-slate-200">
        <div className="mb-6 flex justify-center">
          <div className="h-16 w-16 rounded-full bg-teal-100 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-teal-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
          </div>
        </div>
        <h1 className="text-3xl font-bold text-slate-900 mb-4">Voice Health Portal</h1>
        <p className="text-md text-slate-600 mb-8">Staff Portal Access</p>
        
        <div className="flex justify-center space-x-4">
          <button 
            onClick={() => router.push('/login')}
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-colors duration-150"
          >
            Sign In
          </button>
        </div>
        
        <div className="mt-6 animate-pulse">
          <p className="text-sm text-slate-500 flex items-center justify-center">
            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-teal-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Redirecting to login page...
          </p>
        </div>
      </div>
    </div>
  );
}