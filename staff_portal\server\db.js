// Using ESM syntax for compatibility with modern JS
import pg from 'pg';
import Redis from 'ioredis';
import dotenv from 'dotenv';

// Configure environment variables
dotenv.config({ path: new URL('../.env', import.meta.url).pathname });

console.log('[DEBUG] DATABASE_URL:', process.env.DATABASE_URL);

const { Pool } = pg;

// Initialize PostgreSQL connection pool
let pool = null;
if (process.env.DATABASE_URL) {
  pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
    max: 20, // Adjust as needed
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000, // Short timeout for initial connection test
  });

  // Test the PostgreSQL connection on startup only if pool is initialized
  pool.connect((err, client, release) => {
    if (err) {
      console.error('[DB] Failed to connect to global PostgreSQL pool:', err.message);
      // Optionally, set pool to null or handle so app doesn't assume a working global pool
      // pool = null; 
    } else {
      console.log('[DB] Connected to global PostgreSQL pool successfully.');
      release();
    }
  });
} else {
  console.warn('[DB] Global DATABASE_URL not set. Global PostgreSQL pool not initialized.');
  // pool remains null, or you can assign a dummy object if needed for type consistency elsewhere
  // pool = { query: () => Promise.reject(new Error('Global pool not configured')) };
}

// Initialize Redis connection
let redis = null;
try {
  if (process.env.REDIS_URL) {
    redis = new Redis(process.env.REDIS_URL, { 
      connectTimeout: 10000, // 10 seconds
      maxRetriesPerRequest: 3 
    });
  } else {
    redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD || undefined, // Ensure password is not empty string if not set
      db: parseInt(process.env.REDIS_DB || '0'),
      connectTimeout: 10000,
      maxRetriesPerRequest: 3
    });
  }

  redis.on('connect', () => {
    console.log('[DB] Connected to Redis successfully.');
  });

  redis.on('error', (err) => {
    console.error('[DB] Redis connection error:', err.message);
    // Potentially set redis to null or a dummy object if critical for app startup
    // redis = null;
  });

} catch (error) {
  console.error('[DB] Failed to initialize Redis client:', error.message);
  // redis remains null
}

// Export using ESM syntax
export { pool, redis };