import React from 'react';

/**
 * Card component with consistent styling
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Card content
 * @param {string} props.className - Additional classes
 * @param {string} props.title - Card title
 * @param {React.ReactNode} props.action - Action component (button, link, etc.)
 */
const Card = ({ 
  children, 
  className = '', 
  title = null,
  action = null,
  ...rest 
}) => {
  return (
    <div 
      className={`bg-white rounded-lg shadow-sm border border-slate-200 overflow-hidden ${className}`}
      {...rest}
    >
      {title && (
        <div className="px-6 py-4 border-b border-slate-200 flex justify-between items-center">
          <h3 className="text-lg font-semibold text-slate-800">{title}</h3>
          {action && <div>{action}</div>}
        </div>
      )}
      <div className={title ? 'px-6 py-4' : 'p-6'}>
        {children}
      </div>
    </div>
  );
};

export default Card;
