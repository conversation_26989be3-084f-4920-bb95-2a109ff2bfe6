import Redis from 'ioredis';
import { logger } from './logger';

// Initialize Redis client for rate limiting
const redisClient = new Redis(process.env.REDIS_URL || 'redis://localhost:6379');

/**
 * Rate limiting service to prevent API abuse
 * Uses a sliding window algorithm with Redis
 */
class RateLimiter {
  constructor() {
    // Default settings for different rate limit tiers
    this.limits = {
      // Standard API endpoints
      standard: {
        window: 60, // 1 minute window
        max: 60,    // 60 requests per minute
      },
      
      // Authentication endpoints (more restricted)
      auth: {
        window: 60, // 1 minute window
        max: 10,    // 10 requests per minute
      },
      
      // Admin endpoints (slightly more restricted)
      admin: {
        window: 60, // 1 minute window
        max: 30,    // 30 requests per minute
      },
      
      // Public endpoints (less restricted)
      public: {
        window: 60,   // 1 minute window
        max: 120,     // 120 requests per minute
      }
    };
  }

  /**
   * Get appropriate rate limit tier based on request path
   * @param {string} path - Request path
   * @returns {string} Rate limit tier
   */
  getTierForPath(path) {
    // Authentication-related endpoints
    if (path.startsWith('/api/auth/')) {
      return 'auth';
    }
    
    // Admin endpoints
    if (path.startsWith('/api/admin/')) {
      return 'admin';
    }
    
    // Public endpoints
    if (path.startsWith('/api/public/')) {
      return 'public';
    }
    
    // Default to standard tier
    return 'standard';
  }

  /**
   * Check if a request should be rate limited
   * @param {string} identifier - Unique identifier (e.g., IP address, user ID)
   * @param {string} tier - Rate limit tier
   * @returns {Promise<Object>} Rate limit status
   */
  async check(identifier, tier = 'standard') {
    const { window, max } = this.limits[tier] || this.limits.standard;
    const key = `ratelimit:${tier}:${identifier}`;
    const now = Date.now();
    const windowStart = now - (window * 1000);
    
    try {
      // Add current timestamp to the sorted set
      await redisClient.zadd(key, now, `${now}`);
      
      // Remove entries outside the current window
      await redisClient.zremrangebyscore(key, 0, windowStart);
      
      // Set expiry on the key
      await redisClient.expire(key, window * 2);
      
      // Count requests in the current window
      const count = await redisClient.zcard(key);
      
      // Calculate when the rate limit will reset
      const resetTime = now + (window * 1000);
      
      // Check if rate limit is exceeded
      const limited = count > max;
      
      // If limited, log it
      if (limited) {
        logger.warn(`Rate limit exceeded for ${identifier} on tier ${tier}`, {
          count,
          max,
          identifier,
          tier
        });
      }
      
      return {
        limited,
        current: count,
        max,
        remaining: Math.max(0, max - count),
        resetTime,
        resetSeconds: Math.ceil((resetTime - now) / 1000)
      };
    } catch (error) {
      logger.error(`Rate limit check error for ${identifier}:`, error);
      
      // In case of an error, don't rate limit
      return {
        limited: false,
        current: 0,
        max,
        remaining: max,
        resetTime: now + (window * 1000),
        resetSeconds: window,
        error: true
      };
    }
  }

  /**
   * Block an identifier for a specified period due to abuse
   * @param {string} identifier - Unique identifier to block
   * @param {number} seconds - Duration to block in seconds
   * @returns {Promise<boolean>} Success or failure
   */
  async block(identifier, seconds = 3600) {
    const key = `ratelimit:blocked:${identifier}`;
    
    try {
      await redisClient.set(key, 'blocked');
      await redisClient.expire(key, seconds);
      
      logger.warn(`Blocked ${identifier} for ${seconds} seconds due to abuse`);
      return true;
    } catch (error) {
      logger.error(`Failed to block ${identifier}:`, error);
      return false;
    }
  }

  /**
   * Check if an identifier is blocked
   * @param {string} identifier - Unique identifier to check
   * @returns {Promise<boolean>} Whether the identifier is blocked
   */
  async isBlocked(identifier) {
    const key = `ratelimit:blocked:${identifier}`;
    
    try {
      const blocked = await redisClient.exists(key);
      return blocked === 1;
    } catch (error) {
      logger.error(`Failed to check if ${identifier} is blocked:`, error);
      return false;
    }
  }

  /**
   * Remove a block on an identifier
   * @param {string} identifier - Unique identifier to unblock
   * @returns {Promise<boolean>} Success or failure
   */
  async unblock(identifier) {
    const key = `ratelimit:blocked:${identifier}`;
    
    try {
      await redisClient.del(key);
      logger.info(`Unblocked ${identifier}`);
      return true;
    } catch (error) {
      logger.error(`Failed to unblock ${identifier}:`, error);
      return false;
    }
  }
}

// Create singleton instance
const rateLimiter = new RateLimiter();

/**
 * Rate limiting middleware for Next.js API routes
 * @param {Object} options - Configuration options
 * @returns {Function} Next.js API middleware
 */
export function rateLimiterMiddleware(options = {}) {
  return async (req, res, next) => {
    // Get client identifier (IP address or user ID if authenticated)
    const identifier = req.user?.id || req.headers['x-forwarded-for'] || req.connection.remoteAddress;
    
    // Check if client is blocked
    const blocked = await rateLimiter.isBlocked(identifier);
    
    if (blocked) {
      return res.status(429).json({
        success: false,
        message: 'Too many requests, please try again later',
        blocked: true
      });
    }
    
    // Determine appropriate tier based on request path
    const tier = options.tier || rateLimiter.getTierForPath(req.url);
    
    // Check rate limit
    const limitStatus = await rateLimiter.check(identifier, tier);
    
    // Set rate limit headers
    res.setHeader('X-RateLimit-Limit', limitStatus.max);
    res.setHeader('X-RateLimit-Remaining', limitStatus.remaining);
    res.setHeader('X-RateLimit-Reset', limitStatus.resetTime);
    
    // If rate limited, return 429 status
    if (limitStatus.limited) {
      return res.status(429).json({
        success: false,
        message: 'Too many requests, please try again later',
        retryAfter: limitStatus.resetSeconds
      });
    }
    
    // Continue to handler if not rate limited
    if (typeof next === 'function') {
      return next();
    }
    
    // For Next.js API routes that don't have a next function
    return true;
  };
}

// Higher-order function to apply rate limiting to a Next.js API handler
export function withRateLimit(handler, options = {}) {
  return async (req, res) => {
    // Apply rate limiting
    const middleware = rateLimiterMiddleware(options);
    const shouldContinue = await middleware(req, res);
    
    // If rate limiting middleware returned false, we should stop processing
    if (!shouldContinue) {
      return;
    }
    
    // Call the original handler
    return handler(req, res);
  };
}

export default rateLimiter;