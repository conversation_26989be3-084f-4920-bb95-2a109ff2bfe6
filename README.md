# Ascle AI Healthcare Management System

## Overview
Ascle AI is a comprehensive hospital management system consisting of three main components:

1. **MEGHA Voice Agent**: An AI-powered voice assistant for handling patient calls, appointment scheduling, and test booking via phone.
2. **WhatsApp Agent**: An AI-powered WhatsApp bot for handling patient inquiries, appointment scheduling, and test booking via WhatsApp.
3. **Staff Portal**: A web-based management portal for hospital staff to manage appointments, doctor schedules, test results, and administrative tasks.

This system is designed to be fully dynamic, with no hardcoded elements, and can be configured for multiple hospitals.

## Project Structure
```
.
├── voice_agent/             # Voice agent component (MEGHA)
│   ├── main.py              # Main FastAPI server
│   ├── models.py            # Data models
│   ├── telephony.py         # Call handling with Jambonz
│   ├── database.py          # Database operations
│   ├── nlp.py               # Natural language processing
│   ├── tts.py               # Text-to-speech processing
│   ├── cache_manager.py     # Redis cache manager
│   ├── call_context.py      # Call context management
│   ├── utils.py             # Utility functions
│   ├── init_db.py           # Database initialization script
│   └── requirements.txt     # Python dependencies
│
├── whatsapp_agent/          # WhatsApp agent component
│   ├── server.js            # Main Express server
│   ├── routes/              # Express routes
│   │   └── whatsapp.js      # WhatsApp webhook handler
│   ├── services/            # Business logic services
│   │   └── whatsapp.js      # WhatsApp message processing
│   ├── lib/                 # Utility libraries
│   │   ├── ai.js            # OpenAI integration
│   │   ├── database.js      # PostgreSQL operations
│   │   ├── firebase.js      # Firebase operations
│   │   ├── redis.js         # Redis operations
│   │   ├── hospitals.js     # Hospital configuration
│   │   ├── logger.js        # Logging utility
│   │   └── utils.js         # Utility functions
│   ├── .env                 # Environment variables
│   └── package.json         # Node.js dependencies
│
├── staff_portal/            # Staff portal component
│   ├── pages/               # Next.js pages
│   │   ├── index.js         # Login page
│   │   ├── dashboard.js     # Dashboard
│   │   ├── appointments/    # Appointment management
│   │   ├── settings/        # System settings
│   │   └── api/             # API routes
│   ├── components/          # React components
│   ├── lib/                 # Utility libraries
│   │   ├── auth.js          # Authentication
│   │   ├── firebase.js      # Firebase operations
│   │   └── db.js            # Database access
│   ├── scripts/             # Setup scripts
│   │   └── db_init.js       # Database initialization
│   ├── styles/              # CSS styles
│   ├── public/              # Static assets
│   ├── next.config.js       # Next.js configuration
│   └── package.json         # Node.js dependencies
│
├── data_init/               # Data initialization scripts
│   ├── init_data.py         # Script to populate initial data
│   └── requirements.txt     # Python dependencies
│
└── README.md                # This file
```

## System Requirements

### Voice Agent (MEGHA)
- Python 3.13+
- PostgreSQL 14+
- Redis 6+
- Jambonz account for production
- Twilio account for testing (optional)
- OpenAI API key

### WhatsApp Agent
- Node.js 18+
- PostgreSQL 14+
- Redis 6+
- Twilio WhatsApp Business API account
- OpenAI API key
- Firebase Admin SDK credentials

### Staff Portal
- Node.js 18+
- PostgreSQL 14+
- Firebase project (for authentication and data storage)

## 📦 Dependency Structure (Updated)

The system now uses a **consolidated dependency structure** with shared Redis implementation:

- **`shared/redis/requirements.txt`**: Contains all Redis, semantic processing, and IndicBERT dependencies
- **`voice_agent/requirements.txt`**: Contains only voice-agent-specific dependencies
- **`whatsapp_agent/package.json`**: Node.js dependencies (unchanged)
- **`staff_portal/package.json`**: Node.js dependencies (unchanged)

**⚠️ Important**: Install shared dependencies FIRST before installing individual component dependencies.

📖 **See [DEPENDENCY_CONSOLIDATION_GUIDE.md](DEPENDENCY_CONSOLIDATION_GUIDE.md) for detailed installation instructions.**

## Detailed Setup Instructions

### Prerequisites Installation

#### 1. PostgreSQL
```bash
# For Windows
# Download and install from https://www.postgresql.org/download/windows/
```

#### 2. Redis
```bash
# For Windows
# Download and install from https://github.com/microsoftarchive/redis/releases
```

#### 3. Node.js
```bash
# For Windows
# Download and install from https://nodejs.org/
```

#### 4. Python
```bash
# For Windows
# Download and install from https://www.python.org/downloads/
```

### Firebase Setup

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Create a new project
3. Enable Authentication with Email/Password and Google providers
4. Create a Firestore database
5. Generate a new private key for the Admin SDK:
   - Go to Project Settings > Service Accounts
   - Click "Generate new private key"
   - Save the JSON file securely
6. Add a web app to your Firebase project and note the configuration details

### Database Setup

#### 1. PostgreSQL Database Creation

```bash
# For Windows
# Log into PostgreSQL
sudo -u postgres psql

# Create a superuser for hospital databases
CREATE USER hospital_user WITH PASSWORD 'hospitaldb' SUPERUSER;

# Create the main database
CREATE DATABASE hospital_db;

# Grant privileges
GRANT ALL PRIVILEGES ON DATABASE hospital_db TO hospital_user;

# Exit PostgreSQL
\q
```

#### 2. Create Hospital-Specific Databases

For each hospital in your system, create a separate database:

```bash
# Log into PostgreSQL
sudo -u postgres psql

# Create hospital-specific databases
CREATE DATABASE hospital1_db;
CREATE DATABASE hospital2_db;
# Add more as needed

# Grant privileges
GRANT ALL PRIVILEGES ON DATABASE hospital1_db TO hospital_user;
GRANT ALL PRIVILEGES ON DATABASE hospital2_db TO hospital_user;
# Add more as needed

# Exit PostgreSQL
\q
```

### Environment Variables Setup

#### 1. Voice Agent (.env file in voice_agent directory)

```
# Server
PORT=8000
HOST=0.0.0.0
DEBUG=True
LOG_LEVEL=INFO

# Database
POSTGRES_USER=hospital_user
POSTGRES_PASSWORD=hospitaldb
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=hospital_db

# Redis
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# OpenAI
OPENAI_API_KEY=your_openai_api_key
GPT_MODEL=gpt-4o-mini

# Jambonz (Production)
JAMBONZ_API_KEY=your_jambonz_api_key
JAMBONZ_ACCOUNT_SID=your_jambonz_account_sid
JAMBONZ_API_URL=https://api.jambonz.cloud/v1

# Twilio (Testing)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number
```

#### 2. WhatsApp Agent (.env file in whatsapp_agent directory)

```
# Server
WHATSAPP_PORT=3001 # Default is 3002 in whatsapp_agent/README.md, using 3001 from root README.md
NODE_ENV=development
LOG_LEVEL=info

# Twilio WhatsApp
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_whatsapp_number

# OpenAI
OPENAI_API_KEY=your_openai_api_key
GPT_MODEL=gpt-4o-mini

# Firebase
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/firebase-credentials.json

# Redis
REDIS_URL=redis://localhost:6379
```

#### 3. Staff Portal (.env.local file in staff_portal directory)

```
# Server
PORT=3000
NODE_ENV=development

# Database
POSTGRES_USER=hospital_user
POSTGRES_PASSWORD=hospitaldb
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=hospital_db

# JWT
JWT_SECRET=your_secure_jwt_secret
JWT_EXPIRY=7d

# Firebase
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_firebase_auth_domain
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_firebase_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_firebase_storage_bucket
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_firebase_messaging_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_firebase_app_id
```

### Configuration Files

#### 1. Hospital Configuration (hospitals.json in project root)

Create a `hospitals.json` file in the project root with the following structure:

```json
{
  "hospitals": [
    {
      "id": "hospital1",
      "name": "City General Hospital",
      "logo": "/logos/hospital1.png",
      "address": "123 Main Street, City",
      "phone": "+**********",
      "email": "<EMAIL>",
      "website": "https://cityhospital.com",
      "database": {
        "host": "localhost",
        "port": 5432,
        "user": "hospital_user",
        "password": "hospitaldb",
        "database": "hospital1_db",
        "ssl": false
      },
      "settings": {
        "appointment_duration_minutes": 30,
        "working_hours": {
          "start": "09:00",
          "end": "17:00"
        },
        "languages": ["en", "hi"],
        "services": ["voice", "whatsapp"]
      },
      "webhooks": {
        "whatsapp": "/whatsapp/hospital1"
      }
    },
    {
      "id": "hospital2",
      "name": "Metro Medical Center",
      "logo": "/logos/hospital2.png",
      "address": "456 Park Avenue, Metro City",
      "phone": "+**********",
      "email": "<EMAIL>",
      "website": "https://metromedical.com",
      "database": {
        "host": "localhost",
        "port": 5432,
        "user": "hospital_user",
        "password": "hospitaldb",
        "database": "hospital2_db",
        "ssl": false
      },
      "settings": {
        "appointment_duration_minutes": 45,
        "working_hours": {
          "start": "08:00",
          "end": "18:00"
        },
        "languages": ["en", "hi", "bn"],
        "services": ["voice"]
      }
    }
  ]
}
```

## Data Initialization System (`data_init/`)

### Overview
The data initialization system (`data_init/`) populates the Ascle AI system with initial sample data. It handles data insertion into both Firebase (Firestore) and PostgreSQL databases. It loads data from JSON files for hospitals, doctors, tests, staff, appointments, and test bookings.

### Prerequisites
- Firebase project setup with a service account key.
- PostgreSQL database created for the project.
- Python packages: `firebase-admin`, `psycopg2`.

### Environment Setup
Set the following environment variables:
```bash
# For Firebase
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/your/firebase-service-account-key.json"

# For PostgreSQL (fallback connection, optional)
export DATABASE_URL="postgresql://username:password@localhost:5432/hospital_db"
```
The system supports multiple hospital-specific PostgreSQL databases. Each hospital's configuration in `hospitals.json` (referenced by `data_init/data/hospitals.json`) should contain its `db_postgres` connection string.

### Running the Script
Navigate to the `data_init` directory.
To initialize all data:
```bash
python init_data.py --all
```
To initialize specific data types:
```bash
python init_data.py --hospital
python init_data.py --doctors
python init_data.py --tests
python init_data.py --staff
python init_data.py --appointments
python init_data.py --test-bookings
```

### Data Structure
- **Hospitals**: Stored in Firebase (for voice agent) and PostgreSQL (for staff portal).
- **Doctors, Tests, Staff**: Primarily stored in Firebase with references in PostgreSQL.
- **Appointments, Test Bookings**: Primarily stored in PostgreSQL.

### Customizing Data
Modify JSON files in the `data_init/data/` directory.

### Important Notes
- The script checks for existing records to avoid duplicates.
- Passwords are stored as bcrypt hashes in Firebase.
- Sample data is for demonstration and should be replaced in production.

## Running the System Locally

### Step 1: Initialize the Databases

#### Voice Agent Database Initialization
```bash
cd voice_agent
python -m venv venv
venv\Scripts\activate  # On Windows
# source venv/bin/activate # On macOS/Linux
pip install -r requirements.txt
python init_db.py
```

#### Staff Portal Database Initialization
```bash
cd staff_portal
npm install
node scripts/db_init.js
```

### Step 2: Install Shared Dependencies (Required First)
```bash
# Install shared Redis and semantic processing dependencies
cd shared/redis
pip install -r requirements.txt
```

### Step 3: Initialize Firebase and PostgreSQL Data
This step uses the Data Initialization System described above.
```bash
cd ../../data_init
python -m venv venv
venv\Scripts\activate  # On Windows
# source venv/bin/activate # On macOS/Linux
pip install -r requirements.txt
# Ensure GOOGLE_APPLICATION_CREDENTIALS is set
python init_data.py --all
```
This script will:
1. Create hospital records in PostgreSQL.
2. Create staff records in PostgreSQL and Firebase.
3. Create doctor records in Firebase with specialties and booking limits.
4. Create test records in Firebase.
5. Set up initial settings for each hospital.

### Step 4: Start the Voice Agent
```bash
cd ../voice_agent
# Install voice-agent-specific dependencies
pip install -r requirements.txt
# Activate venv if not already active
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```
The voice agent will be available at http://localhost:8000.

### Step 5: Start the WhatsApp Agent
```bash
cd ../whatsapp_agent
npm install
npm start # Starts on port 3001 (or as configured in .env)
```
The WhatsApp agent will be available at http://localhost:3001 (or as configured).

### Step 6: Start the Staff Portal
```bash
cd ../staff_portal
# npm install (if not already done)
npm run dev
```
The staff portal will be available at http://localhost:3000.

## Testing the System

### Testing the Voice Agent

#### Option 1: Using Twilio for Testing
1. Install ngrok: `npm install -g ngrok`.
2. Expose your voice agent server: `ngrok http 8000`. Note the ngrok URL.
3. Configure your Twilio phone number's voice webhook to `YOUR_NGROK_URL/voice`.
4. Call your Twilio number.

#### Option 2: Using the Voice Agent API Directly
```bash
curl http://localhost:8000/health
curl -X POST http://localhost:8000/voice \
  -H "Content-Type: application/json" \
  -d '{
    "call_id": "test-call-123",
    "from": "+**********",
    "to": "+**********"
  }'
```

### Testing the WhatsApp Agent

#### Using Twilio WhatsApp Sandbox for Testing
1. Expose your WhatsApp agent server: `ngrok http 3001` (or your WhatsApp agent port). Note the ngrok URL.
2. Configure your Twilio WhatsApp Sandbox webhook to `YOUR_NGROK_URL/api/whatsapp/webhook` (for single webhook testing) or `YOUR_NGROK_URL/api/whatsapp/webhook/{hospitalId}` (for hospital-specific).
3. Join your Twilio WhatsApp Sandbox and send a message.

### Testing the Staff Portal
1. Open http://localhost:3000.
2. Log in with credentials from data initialization (e.g., `<EMAIL>` / `password123`).
3. Explore features: Dashboard, Appointments, Doctors, Tests, Settings.

## WhatsApp Agent Specifics

### Features
- Processes incoming WhatsApp messages via Twilio.
- Supports multiple hospitals with dedicated webhooks.
- Uses GPT-4o Mini for NLU.
- Extracts booking details, checks doctor availability (Redis), enforces booking limits.
- Stores chat history in hospital-specific PostgreSQL DBs.
- Multilingual support.
- Integrates with Staff Portal.

### Installation
Covered in general setup. Ensure `npm install` is run in `whatsapp_agent/`.

### Twilio WhatsApp Configuration

#### For Development/Testing (Single Webhook)
- In Twilio Console > Messaging > Settings > WhatsApp Sandbox.
- Set "When a message comes in" webhook URL to: `https://your-server-url.com/api/whatsapp/webhook`.
- Method: POST.

#### For Production (Hospital-Specific Webhooks)
- For each hospital WhatsApp number in Twilio Console > Phone Numbers > Manage > Active Numbers.
- Under Messaging Configuration, set webhook URL to: `https://your-server-url.com/api/whatsapp/webhook/{hospitalId}`.
- Replace `{hospitalId}` with the actual hospital ID.
- Method: POST.

### Integration with Staff Portal
- Appointments booked via WhatsApp appear in Staff Portal ("WhatsApp" source).
- Booking limits set in Staff Portal are enforced by both agents.
- Booking counts synchronized across PostgreSQL, Firebase, Redis.
- Admins can enable/disable WhatsApp service per hospital in Staff Portal.

## Advanced Configuration

### Multi-Hospital Setup
- Add hospital to `hospitals.json`.
- Create new PostgreSQL database for the hospital.
- Run `data_init` scripts for the new hospital.
- Configure Twilio/Jambonz numbers and WhatsApp webhooks.

### Appointment ID System
- Unified 6-digit ID: last 2 digits of phone + 4 digits from timestamp.
- Consistent across voice and WhatsApp bookings.

### Data Synchronization
- PostgreSQL: Appointments, patient records, staff info.
- Firebase: Doctor info, test details, booking limits.
- Redis: Real-time doctor availability, booking counts.
- New appointments update PostgreSQL, Firebase booking count, and Redis availability.

## Deployment

### Voice Agent Deployment
1. Cloud VM (AWS EC2, GCP Compute Engine).
2. Install Python, PostgreSQL, Redis.
3. Clone repo, setup venv, install dependencies.
4. Set production environment variables.
5. Run with Gunicorn: `gunicorn main:app -k uvicorn.workers.UvicornWorker -w 4 --bind 0.0.0.0:8000`.
6. Setup reverse proxy (Nginx/Apache) for HTTPS.
7. Configure Jambonz to point to your server.

### WhatsApp Agent Deployment
(Recommended: Digital Ocean App Platform)

#### Using Digital Ocean App Platform
1. Code in Git repo. Create `Dockerfile` in `whatsapp_agent/`:
   ```dockerfile
   FROM node:18-alpine
   WORKDIR /app
   COPY package*.json ./
   RUN npm install --production
   COPY . .
   EXPOSE 3001 # Or your configured port
   CMD ["node", "server.js"]
   ```
2. In Digital Ocean: Create App, select repo, configure plan, environment variables.
3. Setup managed PostgreSQL and Redis on Digital Ocean, link to App.
4. Update Twilio webhooks to `https://your-app-name.ondigitalocean.app/api/whatsapp/webhook/{hospitalId}`.
5. Secure Firebase credentials (env var or DO secrets).

#### Alternative Deployment Options (AWS Elastic Beanstalk, Heroku)
- **AWS Elastic Beanstalk**: Node.js platform, deploy code, set env vars, use RDS (PostgreSQL) & ElastiCache (Redis).
- **Heroku**: Create app, add PostgreSQL & Redis add-ons, deploy, set env vars.

### Staff Portal Deployment
1. Build: `npm run build` in `staff_portal/`.
2. Deploy: Vercel (`vercel --prod`), Netlify, or custom server (PM2 with `npm start`).
3. Set production environment variables.
4. Configure domain and SSL.

## Troubleshooting

### Common Issues and Solutions
- **Database Connection**: Check PostgreSQL status, credentials, user permissions (`ALTER USER hospital_user WITH SUPERUSER;`), firewalls.
- **Redis Connection**: Check Redis status, URL, password.
- **WhatsApp Webhook**: ngrok active (local), webhook URL in Twilio correct, agent logs, hospital ID in URL.
- **Voice Agent Call**: Agent logs, Twilio/Jambonz webhook, OpenAI key/credits, hospital config.

### WhatsApp Agent Troubleshooting
- Check logs: `whatsapp_agent/logs/whatsapp-agent.log`, `whatsapp-agent-error.log`.
- Ensure PostgreSQL/Redis accessible, Firebase key correct, Twilio webhooks set up.
- Verify booking count sync and hospital WhatsApp number configuration.

### Data Initialization Troubleshooting
- Check Firebase key permissions.
- Verify PostgreSQL connection strings in `hospitals.json` (used by `data_init`).
- Ensure hospital DBs exist with required tables.
- Script logs warnings for inaccessible hospital DBs and continues.

## Security Considerations

### Data Protection
- Strong, unique DB passwords. SSL for DB connections (production).
- Secure environment variables (no version control). HTTPS for all deployments.
- Staff Portal: Proper authN/authZ. Audit logs.
- Compliance with healthcare regulations (e.g., HIPAA).

### API Key Security
- Store in env vars. Use restricted keys. Rotate regularly. Monitor usage.

### WhatsApp Agent Security
- Chat history in hospital-specific PostgreSQL DBs (not Firebase).
- Patient data handled per privacy regulations. Encrypted data transmission.
- No medical advice, only booking assistance.
- Hospital-specific webhooks for message isolation.
- Redis/Firebase data partitioned by hospital ID. Secure booking count sync.

## Maintenance and Updates

### Regular Maintenance Tasks
- Daily database backups. Log rotation.
- Security updates for dependencies and system packages.
- Performance monitoring.

### System Updates
- Test in staging first. Use DB migrations for schema changes.
- Maintain backward compatibility. Document changes.

## Contributing
1. Fork the repository.
2. Create a feature branch.
3. Make your changes.
4. Submit a pull request with a clear description.

## License
This project is licensed under the MIT License.

## Contact
- Email: <EMAIL>
- Website: https://asclehealthcare.com
