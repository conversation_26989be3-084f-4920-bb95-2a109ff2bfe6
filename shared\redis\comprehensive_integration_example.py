"""
Comprehensive Integration Example for Shared Redis Implementation

Demonstrates all features working together:
- Automatic Firebase data synchronization
- Semantic caching with IndicBERT and multilingual support
- Concurrent processing with batching
- Cross-application compatibility
- Performance monitoring and optimization
"""

import asyncio
import logging
import time
from typing import Dict, Any, List

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def demonstrate_voice_agent_integration():
    """Demonstrate voice agent integration with enhanced features."""
    logger.info("=== Voice Agent Integration Demo ===")
    
    try:
        # Import enhanced cache manager
        from voice_agent.enhanced_cache_manager import get_enhanced_cache_manager
        cache_manager = get_enhanced_cache_manager()
        
        hospital_id = "demo_hospital_voice"
        
        # 1. Demonstrate semantic caching with IndicBERT
        logger.info("1. Testing IndicBERT semantic caching...")
        
        # Cache Hindi queries
        hindi_queries = [
            ("डॉक्टर शर्मा कब आएंगे?", "डॉ. शर्मा शाम 5 बजे आएंगे।"),
            ("शर्मा डॉक्टर का समय क्या है?", "डॉ. शर्मा का समय शाम 5 बजे है।"),
            ("ब्लड टेस्ट की कीमत क्या है?", "ब्लड टेस्ट की कीमत ₹500 है।")
        ]
        
        for query, response in hindi_queries:
            success = await cache_manager.cache_semantic_response_async(
                query, response, hospital_id, "general"
            )
            if success:
                logger.info(f"✓ Cached: {query[:30]}...")
        
        # Test semantic search
        search_query = "डॉक्टर शर्मा का टाइम"
        matches = await cache_manager.semantic_search_async(search_query, hospital_id, "general")
        
        if matches:
            logger.info(f"✓ Found {len(matches)} semantic matches for: {search_query}")
            for match in matches:
                logger.info(f"  - {match['response']} (similarity: {match.get('similarity', 0):.2f})")
        
        # 2. Demonstrate call context management
        logger.info("2. Testing enhanced call context management...")
        
        call_id = "demo_call_12345"
        context_data = {
            "hospital_id": hospital_id,
            "caller_number": "+91XXXXXXXXXX",
            "language": "hi",
            "state": "appointment_booking",
            "entities": {"doctor_name": "Dr. Sharma"},
            "conversation_history": []
        }
        
        # Save call context
        await cache_manager.save_call_context_async(call_id, context_data)
        logger.info(f"✓ Saved call context for {call_id}")
        
        # Update call context
        updates = {
            "state": "appointment_confirmed",
            "entities": {
                "doctor_name": "Dr. Sharma",
                "appointment_time": "5 PM",
                "date": "2024-01-15"
            }
        }
        await cache_manager.update_call_context_async(call_id, updates)
        logger.info("✓ Updated call context")
        
        # Load call context
        loaded_context = await cache_manager.load_call_context_async(call_id)
        if loaded_context:
            logger.info(f"✓ Loaded call context: {loaded_context['state']}")
        
        # 3. Demonstrate Firebase integration
        logger.info("3. Testing Firebase integration...")
        
        mock_firebase_data = {
            "doctors": {
                "doc1": {"name": "Dr. Sharma", "specialty": "Cardiology", "schedule": "9 AM - 5 PM"},
                "doc2": {"name": "Dr. Patel", "specialty": "Neurology", "schedule": "10 AM - 6 PM"}
            },
            "tests": {
                "test1": {"name": "Blood Test", "price": "₹500", "duration": "30 minutes"},
                "test2": {"name": "X-Ray", "price": "₹800", "duration": "15 minutes"}
            }
        }
        
        sync_result = await cache_manager.sync_hospital_from_firebase_async(hospital_id, mock_firebase_data)
        if sync_result["success"]:
            logger.info(f"✓ Firebase sync: {sync_result['cached_items']}/{sync_result['total_items']} items cached")
        
        # 4. Get performance statistics
        stats = await cache_manager.get_cache_stats_async()
        logger.info(f"✓ Cache operations: {stats['enhanced_cache_manager']['cache_operations']}")
        
        return True
        
    except Exception as e:
        logger.error(f"Voice agent integration demo failed: {e}")
        return False


async def demonstrate_whatsapp_agent_integration():
    """Demonstrate WhatsApp agent integration with multilingual support."""
    logger.info("=== WhatsApp Agent Integration Demo ===")
    
    try:
        # Import enhanced WhatsApp Redis
        import sys
        sys.path.append('whatsapp_agent')
        from whatsapp_agent.lib.redis import (
            initializeRedis, 
            formatDoctorAvailabilityMessage,
            searchDoctorQueries,
            batchUpdateDoctorAvailability
        )
        
        # Initialize Redis
        await initializeRedis()
        logger.info("✓ WhatsApp Redis initialized")
        
        hospital_id = "demo_hospital_whatsapp"
        
        # 1. Demonstrate multilingual doctor availability
        logger.info("1. Testing multilingual doctor availability...")
        
        # Update doctor availability
        doctor_updates = [
            {"doctorId": "dr_sharma", "status": "arrived"},
            {"doctorId": "dr_patel", "status": "delayed", "estimatedTime": "30 minutes"},
            {"doctorId": "dr_singh", "status": "unavailable"}
        ]
        
        batch_result = await batchUpdateDoctorAvailability(hospital_id, doctor_updates)
        logger.info(f"✓ Batch update: {batch_result['successful']} successful, {batch_result['failed']} failed")
        
        # 2. Test multilingual message formatting
        logger.info("2. Testing multilingual message formatting...")
        
        # English message
        en_message = await formatDoctorAvailabilityMessage(
            hospital_id, "dr_sharma", "Dr. Sharma", "en"
        )
        logger.info(f"✓ English: {en_message['message']}")
        
        # Hindi message
        hi_message = await formatDoctorAvailabilityMessage(
            hospital_id, "dr_sharma", "Dr. Sharma", "hi"
        )
        logger.info(f"✓ Hindi: {hi_message['message']}")
        
        # 3. Test semantic search across languages
        logger.info("3. Testing cross-language semantic search...")
        
        test_queries = [
            ("when will dr sharma come", "en"),
            ("dr sharma kab aayenge", "hi"),
            ("sharma doctor ka time", "hi")
        ]
        
        for query, language in test_queries:
            search_result = await searchDoctorQueries(query, hospital_id, language)
            if search_result["matches"]:
                logger.info(f"✓ Found matches for '{query}': {len(search_result['matches'])}")
            else:
                logger.info(f"- No matches for '{query}'")
        
        return True
        
    except Exception as e:
        logger.error(f"WhatsApp agent integration demo failed: {e}")
        return False


async def demonstrate_staff_portal_integration():
    """Demonstrate staff portal integration with enhanced management."""
    logger.info("=== Staff Portal Integration Demo ===")
    
    try:
        # Import staff portal Redis client
        import sys
        sys.path.append('staff_portal')
        from staff_portal.lib.redis_client import RedisClient
        
        redis_client = RedisClient()
        await redis_client.connect()
        logger.info("✓ Staff portal Redis connected")
        
        hospital_id = "demo_hospital_staff"
        
        # 1. Demonstrate enhanced doctor availability management
        logger.info("1. Testing enhanced doctor availability management...")
        
        # Update doctor availability from staff portal
        update_result = await redis_client.updateDoctorAvailability(
            hospital_id, 
            "dr_sharma", 
            "arrived", 
            None,
            {"staff_id": "staff_001", "reason": "Manual update"}
        )
        
        if update_result["success"]:
            logger.info(f"✓ Updated Dr. Sharma availability: {update_result['status']}")
        
        # Batch update multiple doctors
        doctor_updates = [
            {"doctorId": "dr_patel", "status": "delayed", "estimatedTime": "15 minutes"},
            {"doctorId": "dr_singh", "status": "unavailable"},
            {"doctorId": "dr_kumar", "status": "arrived"}
        ]
        
        batch_result = await redis_client.batchUpdateDoctorAvailability(
            hospital_id, 
            doctor_updates,
            {"staff_id": "staff_001", "batch_update": True}
        )
        
        logger.info(f"✓ Batch update: {batch_result['successful']} successful, {batch_result['failed']} failed")
        
        # 2. Get availability status
        logger.info("2. Testing availability retrieval...")
        
        availability = await redis_client.getDoctorAvailability(hospital_id, "dr_sharma")
        if availability:
            logger.info(f"✓ Dr. Sharma status: {availability['status']}")
        
        # 3. Get cache statistics
        stats = await redis_client.getCacheStats()
        if "error" not in stats:
            logger.info(f"✓ Cache operations: {stats.get('operations', 0)}")
        
        return True
        
    except Exception as e:
        logger.error(f"Staff portal integration demo failed: {e}")
        return False


async def demonstrate_cross_application_compatibility():
    """Demonstrate data sharing across applications."""
    logger.info("=== Cross-Application Compatibility Demo ===")
    
    try:
        # Get adapters for all applications
        from shared.redis.adapters.python_adapter import get_python_adapter
        shared_adapter = get_python_adapter()
        
        hospital_id = "demo_hospital_cross"
        
        # 1. Voice agent caches doctor info
        logger.info("1. Voice agent caching doctor info...")
        
        doctor_data = {
            "name": "Dr. Cross",
            "specialty": "General Medicine",
            "schedule": "9 AM - 5 PM",
            "availability": "available"
        }
        
        await shared_adapter.cache_doctor_info_async(
            "डॉक्टर क्रॉस की जानकारी", doctor_data, hospital_id
        )
        logger.info("✓ Voice agent cached doctor info")
        
        # 2. WhatsApp agent updates availability
        logger.info("2. WhatsApp agent updating availability...")
        
        whatsapp_adapter = shared_adapter  # Same shared adapter
        await whatsapp_adapter.set_async(
            f"availability:{hospital_id}:doctor:dr_cross:status",
            {"status": "delayed", "estimated_time": "20 minutes"},
            expiry=1800
        )
        logger.info("✓ WhatsApp agent updated availability")
        
        # 3. Staff portal reads the data
        logger.info("3. Staff portal reading shared data...")
        
        cached_doctor = await shared_adapter.get_cached_hospital_data_async(hospital_id, "doctors")
        availability_status = await shared_adapter.get_async(f"availability:{hospital_id}:doctor:dr_cross:status")
        
        if cached_doctor or availability_status:
            logger.info("✓ Staff portal successfully read shared data")
        
        # 4. Demonstrate semantic search across applications
        logger.info("4. Testing cross-application semantic search...")
        
        # Search for doctor info cached by voice agent
        matches = await shared_adapter.semantic_search_async(
            "doctor cross information", hospital_id, "doctors"
        )
        
        if matches:
            logger.info(f"✓ Found {len(matches)} cross-application semantic matches")
        
        return True
        
    except Exception as e:
        logger.error(f"Cross-application compatibility demo failed: {e}")
        return False


async def demonstrate_performance_monitoring():
    """Demonstrate performance monitoring and optimization."""
    logger.info("=== Performance Monitoring Demo ===")
    
    try:
        from shared.redis.cache.monitor import get_cache_monitor
        from shared.redis.cache.optimizer import get_cache_optimizer
        from shared.redis.config_manager import get_config_manager
        
        # 1. Get cache monitor
        monitor = get_cache_monitor()
        
        # Start monitoring
        monitor.start_monitoring(interval=5)  # 5 seconds for demo
        logger.info("✓ Started cache monitoring")
        
        # Generate some load
        from shared.redis.adapters.python_adapter import get_python_adapter
        adapter = get_python_adapter()
        
        # Simulate cache operations
        for i in range(50):
            await adapter.set_async(f"perf_test_{i}", f"value_{i}", expiry=60)
            await adapter.get_async(f"perf_test_{i}")
        
        # Wait a bit for monitoring
        await asyncio.sleep(6)
        
        # Get performance report
        report = monitor.get_performance_report()
        logger.info(f"✓ Performance report generated with {len(report.get('applications', {}))} applications")
        
        # Stop monitoring
        monitor.stop_monitoring()
        
        # 2. Test cache optimization
        optimizer = get_cache_optimizer()
        
        optimization_result = optimizer.optimize_ttl_settings("demo_hospital_perf")
        logger.info(f"✓ Cache optimization: {optimization_result.get('optimizations_applied', 0)} optimizations applied")
        
        # 3. Test configuration management
        config_manager = get_config_manager()
        
        validation = config_manager.validate_configuration()
        logger.info(f"✓ Configuration validation: {'Valid' if validation['valid'] else 'Invalid'}")
        
        # Clean up test data
        keys = await adapter.keys_async("perf_test_*")
        if keys:
            await adapter.delete_async(*keys)
        
        return True
        
    except Exception as e:
        logger.error(f"Performance monitoring demo failed: {e}")
        return False


async def main():
    """Run comprehensive integration demonstration."""
    logger.info("🚀 Starting Comprehensive Shared Redis Integration Demo")
    logger.info("=" * 60)
    
    demos = [
        ("Voice Agent Integration", demonstrate_voice_agent_integration),
        ("WhatsApp Agent Integration", demonstrate_whatsapp_agent_integration),
        ("Staff Portal Integration", demonstrate_staff_portal_integration),
        ("Cross-Application Compatibility", demonstrate_cross_application_compatibility),
        ("Performance Monitoring", demonstrate_performance_monitoring)
    ]
    
    passed = 0
    failed = 0
    
    for demo_name, demo_func in demos:
        try:
            logger.info(f"\n🔄 Running {demo_name}...")
            result = await demo_func()
            
            if result:
                passed += 1
                logger.info(f"✅ {demo_name} - SUCCESS")
            else:
                failed += 1
                logger.error(f"❌ {demo_name} - FAILED")
                
        except Exception as e:
            failed += 1
            logger.error(f"❌ {demo_name} - FAILED with exception: {e}")
    
    logger.info("\n" + "=" * 60)
    logger.info(f"📊 Demo Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        logger.info("🎉 All integration demos passed! Shared Redis implementation is working perfectly.")
    else:
        logger.warning(f"⚠️  {failed} demo(s) failed. Please check the implementation.")
    
    return failed == 0


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
