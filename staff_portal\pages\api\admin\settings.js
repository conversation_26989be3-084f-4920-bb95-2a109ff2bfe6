import { withRole } from '../../../lib/auth';
import { getHospitalSettings, updateHospitalSettings } from '../../../lib/firebase';

// Only admin users can access hospital settings
export default withRole(async (req, res) => {
  // GET - Fetch hospital settings
  if (req.method === 'GET') {
    try {
      // SECURITY: Use only authenticated user's hospital_id, ignore any client-provided hospital_id
      const hospitalId = req.user.hospital_id;

      if (!hospitalId) {
        return res.status(401).json({
          success: false,
          message: 'Hospital ID missing from authenticated user'
        });
      }
      
      // Get hospital settings from Firestore
      const result = await getHospitalSettings(hospitalId);
      
      if (result.success) {
        return res.status(200).json({
          success: true,
          data: result.data
        });
      } else {
        return res.status(404).json({
          success: false,
          message: 'Hospital settings not found'
        });
      }
    } catch (error) {
      console.error('Get hospital settings error:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
  
  // PUT - Update hospital settings
  if (req.method === 'PUT') {
    try {
      const { settings } = req.body;

      // SECURITY: Use only authenticated user's hospital_id, ignore any client-provided hospitalId
      const hospitalId = req.user.hospital_id;

      // Validate required parameters
      if (!hospitalId) {
        return res.status(401).json({
          success: false,
          message: 'Hospital ID missing from authenticated user'
        });
      }

      if (!settings) {
        return res.status(400).json({
          success: false,
          message: 'Settings are required'
        });
      }

      // Update hospital settings in Firestore
      const result = await updateHospitalSettings(hospitalId, settings);
      
      if (result.success) {
        return res.status(200).json({
          success: true,
          message: 'Hospital settings updated successfully'
        });
      } else {
        return res.status(500).json({
          success: false,
          message: result.error || 'Failed to update hospital settings'
        });
      }
    } catch (error) {
      console.error('Update hospital settings error:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
  
  // Method not allowed
  return res.status(405).json({
    success: false,
    message: 'Method not allowed'
  });
}, ['admin']); // Only admin role can access this endpoint