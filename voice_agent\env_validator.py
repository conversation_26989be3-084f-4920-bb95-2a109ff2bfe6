"""
Environment Variable Validator for Voice Agent
Ensures all required environment variables are set for production deployment
"""

import os
import re
import json
from typing import Dict, List, Optional, Any

class VoiceAgentEnvValidator:
    """
    Production-ready environment variable validator for Voice Agent.
    Validates all critical configuration before startup.
    """
    
    # Required environment variables with validation rules
    REQUIRED_ENV_VARS = {
        "INTERNAL_API_KEY": {
            "description": "Internal API key for secure communication with staff portal",
            "example": "your_secure_internal_api_key_here",
            "min_length": 32,
            "validation": lambda x: len(x) >= 32 and x != "your_secure_internal_api_key_here"
        },
        
        "JAMBONZ_WEBSOCKET_URL": {
            "description": "Jambonz WebSocket server URL for voice communication",
            "example": "wss://your-jambonz-server.com/ws",
            "validation": lambda x: x.startswith(("ws://", "wss://")) and "localhost" not in x if os.getenv("ENVIRONMENT") == "production" else True
        },
        
        "JAMBONZ_API_KEY": {
            "description": "Jambonz API key for authentication",
            "example": "your_jambonz_api_key",
            "min_length": 16,
            "validation": lambda x: len(x) >= 16
        },
        
        "JAMBONZ_ACCOUNT_SID": {
            "description": "Jambonz account SID",
            "example": "your_jambonz_account_sid",
            "min_length": 16,
            "validation": lambda x: len(x) >= 16
        },
        
        "REDIS_URL": {
            "description": "Redis connection URL for caching and communication",
            "example": "redis://localhost:6379",
            "validation": lambda x: x.startswith(("redis://", "rediss://"))
        },
        
        "FIREBASE_PROJECT_ID": {
            "description": "Firebase project ID",
            "example": "your_project_id",
            "validation": lambda x: len(x) > 0 and x != "your_project_id"
        },
        
        "GOOGLE_APPLICATION_CREDENTIALS": {
            "description": "Path to Firebase service account JSON file",
            "example": "path/to/your/firebase-credentials.json",
            "validation": lambda x: os.path.exists(x) if x else False
        }
    }
    
    # Optional environment variables with defaults
    OPTIONAL_ENV_VARS = {
        "PORT": "8000",
        "HOST": "0.0.0.0",
        "ENVIRONMENT": "development",
        "DEBUG": "False",
        "LOG_LEVEL": "INFO",
        "DEFAULT_LANGUAGE": "hi",
        "DEFAULT_REGION": "IN",
        "PHONE_VALIDATION_REGION": "IN",
        "STRICT_PHONE_VALIDATION": "true",
        "WEBSOCKET_TIMEOUT": "300",
        "MAX_WEBSOCKET_CONNECTIONS": "1000",
        "RATE_LIMIT_PER_MINUTE": "60"
    }
    
    @classmethod
    def validate_environment(cls) -> Dict[str, Any]:
        """
        Validate all environment variables.
        
        Returns:
            Dictionary with validation results
        """
        errors = []
        warnings = []
        missing = []
        
        # Check required variables
        for var_name, config in cls.REQUIRED_ENV_VARS.items():
            value = os.getenv(var_name)
            
            if not value:
                missing.append({
                    "name": var_name,
                    "description": config["description"],
                    "example": config["example"]
                })
            else:
                # Validate the value
                if "validation" in config:
                    try:
                        if not config["validation"](value):
                            errors.append({
                                "name": var_name,
                                "error": f"Invalid value for {var_name}",
                                "description": config["description"]
                            })
                    except Exception as e:
                        errors.append({
                            "name": var_name,
                            "error": f"Validation error: {str(e)}",
                            "description": config["description"]
                        })
                
                # Check minimum length if specified
                if "min_length" in config and len(value) < config["min_length"]:
                    errors.append({
                        "name": var_name,
                        "error": f"Must be at least {config['min_length']} characters",
                        "current_length": len(value)
                    })
        
        # Production-specific validations
        if os.getenv("ENVIRONMENT") == "production":
            cls._validate_production_settings(errors, warnings)
        
        # Validate JSON configurations
        cls._validate_json_configs(errors)
        
        return {
            "success": len(errors) == 0 and len(missing) == 0,
            "errors": errors,
            "warnings": warnings,
            "missing": missing,
            "environment": os.getenv("ENVIRONMENT", "development")
        }
    
    @classmethod
    def _validate_production_settings(cls, errors: List[Dict], warnings: List[Dict]):
        """Validate production-specific settings"""
        
        # Check for localhost URLs in production
        jambonz_url = os.getenv("JAMBONZ_WEBSOCKET_URL", "")
        if "localhost" in jambonz_url:
            errors.append({
                "name": "JAMBONZ_WEBSOCKET_URL",
                "error": "Cannot use localhost URL in production environment",
                "current": jambonz_url
            })
        
        # Check for weak secrets
        jwt_secret = os.getenv("JWT_SECRET", "")
        if jwt_secret and len(jwt_secret) < 32:
            warnings.append({
                "name": "JWT_SECRET",
                "warning": "JWT_SECRET should be at least 32 characters in production"
            })
        
        # Check debug mode
        if os.getenv("DEBUG", "").lower() == "true":
            warnings.append({
                "name": "DEBUG",
                "warning": "DEBUG mode should be disabled in production"
            })
        
        # Check CORS origins
        cors_origins = os.getenv("CORS_ORIGINS", "")
        if "*" in cors_origins:
            errors.append({
                "name": "CORS_ORIGINS",
                "error": "CORS_ORIGINS should not use wildcard (*) in production"
            })
    
    @classmethod
    def _validate_json_configs(cls, errors: List[Dict]):
        """Validate JSON configuration variables"""
        
        # Validate hospital database configuration
        hospital_db_config = os.getenv("HOSPITAL_DB_CONFIG", "")
        if hospital_db_config:
            try:
                json.loads(hospital_db_config)
            except json.JSONDecodeError:
                errors.append({
                    "name": "HOSPITAL_DB_CONFIG",
                    "error": "Invalid JSON format for hospital database configuration"
                })
    
    @classmethod
    def print_validation_results(cls, result: Dict[str, Any]):
        """Print validation results to console"""
        
        print("\n🔍 Voice Agent Environment Validation")
        print("======================================")
        print(f"Environment: {result['environment']}")
        
        if result["success"]:
            print("✅ All required environment variables are properly configured!")
        else:
            print("❌ Environment validation failed!")
        
        # Print missing variables
        if result["missing"]:
            print("\n📋 Missing Required Variables:")
            for item in result["missing"]:
                print(f"  ❌ {item['name']}")
                print(f"     Description: {item['description']}")
                print(f"     Example: {item['example']}")
        
        # Print errors
        if result["errors"]:
            print("\n🚨 Configuration Errors:")
            for item in result["errors"]:
                print(f"  ❌ {item['name']}: {item['error']}")
                if "current" in item:
                    print(f"     Current: {item['current']}")
        
        # Print warnings
        if result["warnings"]:
            print("\n⚠️  Warnings:")
            for item in result["warnings"]:
                print(f"  ⚠️  {item['name']}: {item['warning']}")
        
        if not result["success"]:
            print("\n📖 Please check .env.example for configuration guidance")
            print("🔧 Fix the issues above before deploying to production")
        
        print("======================================\n")
    
    @classmethod
    def validate_and_exit_on_error(cls):
        """Validate environment and exit if critical errors found"""
        
        result = cls.validate_environment()
        cls.print_validation_results(result)
        
        if not result["success"]:
            print("❌ Environment validation failed. Exiting...")
            exit(1)
        
        return result

# Convenience function for easy importing
def validate_environment():
    """Validate voice agent environment variables"""
    return VoiceAgentEnvValidator.validate_environment()

def validate_and_exit_on_error():
    """Validate environment and exit if errors found"""
    return VoiceAgentEnvValidator.validate_and_exit_on_error()

# Export for easy importing
__all__ = ['VoiceAgentEnvValidator', 'validate_environment', 'validate_and_exit_on_error']
