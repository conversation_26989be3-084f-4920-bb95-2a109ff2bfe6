import React from 'react';

/**
 * Button component with consistent styling
 * @param {Object} props - Component props
 * @param {string} props.variant - Button variant (primary, secondary, accent, outline, danger)
 * @param {string} props.size - Button size (default, small)
 * @param {boolean} props.isLoading - Loading state
 * @param {boolean} props.disabled - Disabled state
 * @param {React.ReactNode} props.children - Button content
 * @param {Function} props.onClick - Click handler
 * @param {string} props.className - Additional classes
 */
const Button = ({ 
  variant = 'primary', 
  size = 'default', 
  isLoading = false, 
  disabled = false, 
  children, 
  onClick, 
  className = '',
  ...rest 
}) => {
  // Base button styles
  let buttonClasses = '';
  
  // Variant styles
  switch (variant) {
    case 'primary':
      buttonClasses = 'bg-teal-600 hover:bg-teal-700 text-white focus:ring-teal-500';
      break;
    case 'secondary':
      buttonClasses = 'bg-slate-600 hover:bg-slate-700 text-white focus:ring-slate-500';
      break;
    case 'accent':
      buttonClasses = 'bg-amber-500 hover:bg-amber-600 text-white focus:ring-amber-500';
      break;
    case 'outline':
      buttonClasses = 'border border-slate-300 bg-white text-slate-700 hover:bg-slate-50 focus:ring-teal-500';
      break;
    case 'danger':
      buttonClasses = 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500';
      break;
    default:
      buttonClasses = 'bg-teal-600 hover:bg-teal-700 text-white focus:ring-teal-500';
  }
  
  // Size styles
  const sizeClasses = size === 'small' ? 'px-3 py-1 text-sm' : 'px-4 py-2';
  
  // Disabled and loading styles
  const stateClasses = (disabled || isLoading) ? 'opacity-70 cursor-not-allowed' : 'focus:outline-none focus:ring-2 focus:ring-offset-2';
  
  return (
    <button
      onClick={onClick}
      disabled={disabled || isLoading}
      className={`inline-flex items-center justify-center font-medium rounded-md shadow-sm ${buttonClasses} ${sizeClasses} ${stateClasses} ${className}`}
      {...rest}
    >
      {isLoading ? (
        <>
          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-current" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          {children}
        </>
      ) : children}
    </button>
  );
};

export default Button;
