import { getStaffByEmail } from '../../../lib/firebase';
import { createToken, setTokenCookie, comparePassword } from '../../../lib/auth';

export default async function handler(req, res) {
  // Only allow POST method
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false, 
      message: 'Method not allowed' 
    });
  }

  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({ 
        success: false, 
        message: 'Email and password are required' 
      });
    }

    // Get staff data from Firestore by email
    const staffResult = await getStaffByEmail(email);

    if (!staffResult.success) {
      return res.status(401).json({ 
        success: false, 
        message: 'Invalid email or password' 
      });
    }

    const staffData = staffResult.data;
    
    // Get stored password hash
    const storedHash = staffData.credentials?.password_hash;
    
    if (!storedHash) {
      return res.status(401).json({ 
        success: false, 
        message: 'Invalid credentials' 
      });
    }
    
    // Compare the provided password with the stored hash using bcrypt
    const passwordMatch = await comparePassword(password, storedHash);
    
    if (!passwordMatch) {
      return res.status(401).json({ 
        success: false, 
        message: 'Invalid email or password' 
      });
    }

    // Create JWT with user data
    const token = createToken({
      id: staffData.id,
      name: staffData.name,
      role: staffData.role,
      hospital_id: staffData.hospital_id,
      email: staffData.email
    });

    // Set JWT in HTTP-only cookie
    setTokenCookie(res, token);

    // Return success with user data (excluding sensitive info)
    return res.status(200).json({
      success: true,
      user: {
        id: staffData.id,
        name: staffData.name,
        role: staffData.role,
        email: staffData.email,
        hospital_id: staffData.hospital_id
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    });
  }
}