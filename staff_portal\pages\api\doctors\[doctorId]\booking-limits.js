import { withRole } from '../../../../lib/auth';
import { 
  updateDoctorBookingLimits, 
  getDoctorBookingLimits 
} from '../../../../lib/firebase';
import redisClient from '../../../../lib/redis_client';
import { logger } from '../../../../lib/logger';

/**
 * API endpoint for managing specific doctor's booking limits
 * Accessible by admin and receptionist roles
 */
export default withRole(async (req, res) => {
  const { doctorId } = req.query;
  const hospitalId = req.user?.hospital_id;
  
  if (!hospitalId) {
    return res.status(401).json({ 
      success: false, 
      message: 'Hospital ID missing from user context' 
    });
  }
  
  if (!doctorId) {
    return res.status(400).json({ 
      success: false, 
      message: 'Doctor ID is required' 
    });
  }

  try {
    switch (req.method) {
      case 'GET':
        return await handleGetDoctorBookingLimits(req, res, hospitalId, doctorId);
      case 'PUT':
        return await handleUpdateDoctorBookingLimits(req, res, hospitalId, doctorId);
      default:
        res.setHeader('Allow', ['GET', 'PUT']);
        return res.status(405).json({ 
          success: false, 
          message: `Method ${req.method} Not Allowed` 
        });
    }
  } catch (error) {
    logger.error('[API_DOCTOR_BOOKING_LIMITS] Unexpected error:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
}, ['admin', 'receptionist']); // Allow both admin and receptionist roles

/**
 * Handle GET request - fetch specific doctor's booking limits with Redis status
 */
async function handleGetDoctorBookingLimits(req, res, hospitalId, doctorId) {
  try {
    logger.info(`[API_DOCTOR_BOOKING_LIMITS] Fetching limits for doctor ${doctorId} in hospital ${hospitalId}`);
    
    // Get doctor's booking limits from Firebase
    const result = await getDoctorBookingLimits(hospitalId, doctorId);
    
    if (!result.success) {
      return res.status(404).json(result);
    }
    
    // Enhance with Redis data
    try {
      // Get current booking status from Redis/Voice Agent
      const today = new Date().toISOString().split('T')[0];
      const statusResult = await redisClient.getBookingAvailabilityStatus(hospitalId, doctorId, today);
      
      if (statusResult.success) {
        result.data.currentStatus = statusResult.data.availability;
        result.data.lastChecked = new Date().toISOString();
      }
      
      // Get Redis cached limits for comparison
      const cachedLimits = await redisClient.getDoctorDailyLimits(hospitalId, doctorId);
      result.data.redisCachedLimits = cachedLimits;
      
      // Check if Firebase and Redis are in sync
      const firebaseLimits = result.data.dailyLimits;
      const isInSync = cachedLimits && 
        JSON.stringify(firebaseLimits) === JSON.stringify(cachedLimits);
      
      result.data.syncStatus = {
        isInSync,
        firebaseLimits,
        redisCachedLimits: cachedLimits,
        lastSyncCheck: new Date().toISOString()
      };
      
    } catch (redisError) {
      logger.warn('[API_DOCTOR_BOOKING_LIMITS] Redis data fetch failed:', redisError);
      result.data.redisError = redisError.message;
      result.data.syncStatus = { isInSync: false, error: redisError.message };
    }
    
    return res.status(200).json(result);
    
  } catch (error) {
    logger.error('[API_DOCTOR_BOOKING_LIMITS] Error in GET handler:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to fetch doctor booking limits',
      error: error.message 
    });
  }
}

/**
 * Handle PUT request - update specific doctor's booking limits
 */
async function handleUpdateDoctorBookingLimits(req, res, hospitalId, doctorId) {
  try {
    const { dailyLimits, forceSync = false } = req.body;
    
    if (!dailyLimits) {
      return res.status(400).json({ 
        success: false, 
        message: 'Daily limits are required' 
      });
    }
    
    logger.info(`[API_DOCTOR_BOOKING_LIMITS] Updating limits for doctor ${doctorId} in hospital ${hospitalId}`);
    
    // Get current limits for comparison
    const currentLimitsResult = await getDoctorBookingLimits(hospitalId, doctorId);
    const oldLimits = currentLimitsResult.success ? currentLimitsResult.data.dailyLimits : null;
    const doctorName = currentLimitsResult.success ? currentLimitsResult.data.doctorName : 'Unknown Doctor';
    
    // Update limits in Firebase
    const updateResult = await updateDoctorBookingLimits(hospitalId, doctorId, dailyLimits);
    
    if (!updateResult.success) {
      return res.status(400).json(updateResult);
    }
    
    // Trigger Redis sync with voice agent
    let syncStatus = { status: 'pending', message: 'Initiating sync...' };
    
    try {
      logger.info(`[API_DOCTOR_BOOKING_LIMITS] Triggering Redis sync for doctor ${doctorId}`);
      
   // If forceSync is true, refresh the entire hospital, otherwise just this hospital
    // Always refresh the entire hospital's booking limits
      const syncResult = await redisClient.triggerBookingLimitRefresh(hospitalId);
      
      if (syncResult.success) {
        syncStatus = { 
          status: 'success', 
          message: 'Voice agent cache updated successfully',
          details: syncResult.data,
          syncedAt: new Date().toISOString()
        };
        logger.info('[API_DOCTOR_BOOKING_LIMITS] Redis sync completed successfully');
        
        // Verify sync by checking cached data
        setTimeout(async () => {
          try {
            const cachedLimits = await redisClient.getDoctorDailyLimits(hospitalId, doctorId);
            const isInSync = cachedLimits && 
              JSON.stringify(dailyLimits) === JSON.stringify(cachedLimits);
            
            if (!isInSync) {
              logger.warn('[API_DOCTOR_BOOKING_LIMITS] Sync verification failed - data mismatch');
            } else {
              logger.info('[API_DOCTOR_BOOKING_LIMITS] Sync verification successful');
            }
          } catch (verifyError) {
            logger.warn('[API_DOCTOR_BOOKING_LIMITS] Sync verification error:', verifyError);
          }
        }, 2000); // Check after 2 seconds
        
      } else {
        syncStatus = { 
          status: 'error', 
          message: syncResult.error || 'Failed to sync with voice agent',
          details: syncResult,
          failedAt: new Date().toISOString()
        };
        logger.warn('[API_DOCTOR_BOOKING_LIMITS] Redis sync failed:', syncResult.error);
      }
    } catch (syncError) {
      syncStatus = { 
        status: 'error', 
        message: 'Redis sync error: ' + syncError.message,
        error: syncError.message,
        failedAt: new Date().toISOString()
      };
      logger.error('[API_DOCTOR_BOOKING_LIMITS] Redis sync error:', syncError);
    }
    
    return res.status(200).json({ 
      success: true, 
      message: 'Booking limits updated successfully',
      data: {
        doctorId,
        doctorName,
        oldLimits,
        newLimits: dailyLimits,
        syncStatus,
        updatedAt: new Date().toISOString(),
        updatedBy: req.user?.email || 'Unknown User'
      }
    });
    
  } catch (error) {
    logger.error('[API_DOCTOR_BOOKING_LIMITS] Error in PUT handler:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to update doctor booking limits',
      error: error.message 
    });
  }
}
