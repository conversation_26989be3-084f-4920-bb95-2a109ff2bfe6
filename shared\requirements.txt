# Requirements for shared LLM service
# Core LLM functionality
openai>=1.0.0  # GPT-4.1-nano (gpt-4o-mini) for function calling

# Web framework for LLM API server
fastapi>=0.104.0
uvicorn>=0.24.0
pydantic>=2.0.0

# Database and storage
firebase-admin>=6.2.0  # Firebase integration
redis>=5.0.0  # Semantic caching
asyncpg>=0.29.0  # Async PostgreSQL
psycopg2-binary>=2.9.0  # PostgreSQL sync client

# Utilities
python-dotenv>=1.0.0  # Environment variable loading
requests>=2.31.0  # HTTP client for external API calls
