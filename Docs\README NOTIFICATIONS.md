# Notification System

This directory contains a comprehensive notification system for the Staff Portal application, providing user-facing error notifications and success messages.

## Components

### 1. `useNotifications` Hook (`../hooks/useNotifications.js`)
A custom React hook that provides centralized notification management.

**Features:**
- Add/remove notifications
- Auto-dismiss with configurable duration
- Convenience methods for different notification types
- Type-safe notification management

**Usage:**
```javascript
import { useNotifications } from '../hooks/useNotifications';

function MyComponent() {
  const { showError, showSuccess, showWarning, showInfo } = useNotifications();
  
  const handleError = () => {
    showError('Error Title', 'Error message details');
  };
  
  const handleSuccess = () => {
    showSuccess('Success!', 'Operation completed successfully');
  };
}
```

### 2. `ErrorToast` Component (`ErrorToast.js`)
Enhanced toast component supporting multiple notification types.

**Features:**
- Support for success, error, warning, and info notifications
- Auto-dismiss with configurable duration
- Click-to-dismiss functionality
- Proper accessibility attributes
- Smooth animations

**Props:**
- `type`: 'success' | 'error' | 'warning' | 'info'
- `title`: Notification title
- `message`: Notification message
- `duration`: Auto-dismiss duration (0 = no auto-dismiss)
- `dismissible`: Whether the notification can be dismissed
- `onClose`: Callback when notification is closed
- `onClick`: Callback when notification is clicked

### 3. `NotificationContainer` Component (`NotificationContainer.js`)
Container component that manages multiple notifications in a stack.

**Features:**
- Stacks notifications at bottom-right of screen
- Proper z-index management
- Smooth stacking animations

### 4. Legacy Components
- `Toast.js`: Original toast component for OCR notifications
- `NotificationCenter.js`: WhatsApp OCR notification center
- `PrescriptionViewer.js`: OCR prescription viewer

## Integration Example

The `AvailabilityManagement` component demonstrates full integration:

```javascript
import { useNotifications } from '../hooks/useNotifications';
import { NotificationContainer } from './notifications/NotificationContainer';

export function AvailabilityManagement({ hospitalId }) {
  const { notifications, removeNotification, showError, showSuccess } = useNotifications();
  
  const handleError = (error) => {
    showError('Operation Failed', error.message);
  };
  
  const handleSuccess = () => {
    showSuccess('Success', 'Changes saved successfully');
  };
  
  return (
    <>
      {/* Your component content */}
      
      <NotificationContainer 
        notifications={notifications} 
        onRemove={removeNotification} 
      />
    </>
  );
}
```

## Notification Types

### Success Notifications
- Green color scheme
- CheckCircle icon
- Used for successful operations
- Default duration: 5 seconds

### Error Notifications
- Red color scheme
- AlertCircle icon
- Used for errors and failures
- Default duration: 8 seconds (longer for errors)

### Warning Notifications
- Yellow color scheme
- AlertTriangle icon
- Used for warnings and partial failures
- Default duration: 5 seconds

### Info Notifications
- Blue color scheme
- Info icon
- Used for informational messages
- Default duration: 5 seconds

## Styling

Notifications use Tailwind CSS classes and custom animations defined in `styles/globals.css`:

- `animate-slide-in-right`: Slide in from right animation
- `animate-slide-out-right`: Slide out to right animation

## Best Practices

1. **Error Handling**: Always provide user-facing error messages instead of just console logging
2. **Message Clarity**: Use clear, actionable error messages
3. **Retry Options**: For network errors, consider providing retry functionality
4. **Duration**: Use longer durations for error messages, shorter for success messages
5. **Accessibility**: All notifications include proper ARIA attributes

## Testing

Use the test page at `/test-notifications` to verify the notification system functionality.
