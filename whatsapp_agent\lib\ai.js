/**
 * AI module for WhatsApp Agent
 *
 * This module has been updated to use the unified LLM service with function calling
 * instead of the previous fine-tuning approach. It now integrates with the shared
 * LLM service for better consistency and functionality.
 */

import { logger } from './logger.js';
import { whatsappLLMIntegration } from './llm_integration.js';

// Deprecated: OpenAI client is now handled by the unified LLM service
logger.info('AI module updated to use unified LLM service with function calling');

/**
 * Process a WhatsApp message using the unified LLM service with function calling
 * @param {Object} message - Message object with text and context
 * @param {Object} hospitalContext - Hospital-specific context
 * @returns {Promise<Object>} - Processed message with extracted details and response
 */
export const processMessage = async (message, hospitalContext) => {
  try {
    logger.info('Processing WhatsApp message with unified LLM service');

    // Use the new LLM integration
    const result = await whatsappLLMIntegration.processMessage(message, hospitalContext);

    logger.info('WhatsApp message processed successfully with LLM integration');
    return result;

  } catch (error) {
    logger.error('Error processing message with unified LLM service:', error);

    // Fallback to basic response
    return {
      originalText: message.text,
      response: "I'm sorry, I'm experiencing technical difficulties. Please try again later or contact the hospital directly.",
      bookingDetails: null,
      intent: 'error',
      language: message.language || 'en',
      functionCalls: []
    };
  }
};

// Legacy functions kept for backward compatibility
// These are now handled by the unified LLM service

/**
 * Legacy function - now handled by LLM integration
 * @deprecated Use whatsappLLMIntegration.processMessage instead
 */
function createSystemPrompt() {
  logger.warn('createSystemPrompt is deprecated. System prompts are now handled by the unified LLM service.');
  return '';
}

/**
 * Legacy function - now handled by LLM integration
 * @deprecated Chat history formatting is now handled by the unified LLM service
 */
function formatChatHistory() {
  logger.warn('formatChatHistory is deprecated. Chat history formatting is now handled by the unified LLM service.');
  return [];
}

// Export for backward compatibility
export default {
  processMessage
};
