"""
Language utility functions for Voice Health Portal.

This module provides shared language detection and processing utilities
optimized for Indian languages and multi-hospital environments.
Designed for high-concurrency voice applications with thread-safe operations.
"""

import logging
import re
import asyncio
import time
import unicodedata
from typing import Dict, Any, List, Optional, Tuple, Set, Union
from functools import lru_cache
from concurrent.futures import ThreadPoolExecutor
import threading
from collections import OrderedDict

# Configure logging
logger = logging.getLogger(__name__)

# Thread-safe LRU cache for language detection results with optimized eviction
class ThreadSafeLRUCache:
    """
    Thread-safe LRU cache with optimized eviction to avoid performance bottlenecks.
    Uses OrderedDict for guaranteed LRU behavior and deferred eviction.
    """

    def __init__(self, max_size: int = 10000, eviction_batch_size: int = 2000):
        self.max_size = max_size
        self.eviction_batch_size = eviction_batch_size
        self._cache = OrderedDict()
        self._lock = threading.RLock()
        self._eviction_needed = False
        self._last_eviction_check = time.time()
        self._eviction_check_interval = 60  # Check every 60 seconds

    def get(self, key: int) -> Optional[bool]:
        """Get value from cache and mark as recently used."""
        with self._lock:
            if key in self._cache:
                # Move to end (most recently used)
                value = self._cache.pop(key)
                self._cache[key] = value
                return value
            return None

    def put(self, key: int, value: bool) -> None:
        """Put value in cache with optimized eviction."""
        with self._lock:
            if key in self._cache:
                # Update existing key and move to end
                self._cache.pop(key)
                self._cache[key] = value
            else:
                # Add new key
                self._cache[key] = value

                # Check if eviction is needed (non-blocking check)
                if len(self._cache) > self.max_size:
                    self._eviction_needed = True

        # Perform eviction outside the lock if needed
        self._maybe_evict()

    def _maybe_evict(self) -> None:
        """Perform deferred eviction to avoid holding lock during eviction."""
        current_time = time.time()

        # Only check for eviction periodically or when explicitly needed
        if (self._eviction_needed or
            (current_time - self._last_eviction_check) > self._eviction_check_interval):

            self._last_eviction_check = current_time
            self._perform_eviction()

    def _perform_eviction(self) -> None:
        """Perform actual eviction with minimal lock time."""
        items_to_remove = []

        with self._lock:
            if len(self._cache) > self.max_size:
                # Remove oldest items (from beginning of OrderedDict)
                items_to_remove = list(self._cache.keys())[:self.eviction_batch_size]

                for key in items_to_remove:
                    self._cache.pop(key, None)

                self._eviction_needed = False
                logger.debug(f"Evicted {len(items_to_remove)} items from language cache")
            else:
                self._eviction_needed = False

    def clear(self) -> None:
        """Clear all cached items."""
        with self._lock:
            self._cache.clear()
            self._eviction_needed = False

    def size(self) -> int:
        """Get current cache size."""
        return len(self._cache)

# Initialize optimized cache with shared Redis configuration
def _initialize_language_cache():
    """Initialize language cache with shared Redis configuration."""
    try:
        from shared.redis.cache.monitor import CacheOptimizationConfig
        config = CacheOptimizationConfig()
        return ThreadSafeLRUCache(
            max_size=config.voice_agent_batch_size * 100,  # Scale based on batch size
            eviction_batch_size=config.voice_agent_batch_size * 20
        )
    except ImportError:
        # Fallback to default configuration
        logger.warning("Shared cache config not available, using defaults")
        return ThreadSafeLRUCache(max_size=10000, eviction_batch_size=2000)

# Global optimized cache instance
_language_cache = _initialize_language_cache()

# Unicode ranges for Indian scripts (optimized for fast lookup)
INDIAN_SCRIPT_RANGES = {
    'devanagari': (0x0900, 0x097F),  # Hindi, Marathi, Sanskrit
    'bengali': (0x0980, 0x09FF),     # Bengali, Assamese
    'gurmukhi': (0x0A00, 0x0A7F),    # Punjabi
    'gujarati': (0x0A80, 0x0AFF),    # Gujarati
    'oriya': (0x0B00, 0x0B7F),       # Oriya (Odia)
    'tamil': (0x0B80, 0x0BFF),       # Tamil
    'telugu': (0x0C00, 0x0C7F),      # Telugu
    'kannada': (0x0C80, 0x0CFF),     # Kannada
    'malayalam': (0x0D00, 0x0D7F),   # Malayalam
}

# Script to language mapping for IndicBERT supported languages
SCRIPT_TO_LANGUAGE = {
    'devanagari': 'hi',  # Default to Hindi for Devanagari
    'bengali': 'bn',
    'gurmukhi': 'pa',
    'gujarati': 'gu',
    'oriya': 'or',
    'tamil': 'ta',
    'telugu': 'te',
    'kannada': 'kn',
    'malayalam': 'ml',
}

# Supported Indian languages with IndicBERT
SUPPORTED_INDIC_LANGUAGES = {
    'as': 'Assamese',
    'bn': 'Bengali',
    'en': 'English',
    'gu': 'Gujarati',
    'hi': 'Hindi',
    'kn': 'Kannada',
    'ml': 'Malayalam',
    'mr': 'Marathi',
    'or': 'Oriya',
    'pa': 'Punjabi',
    'ta': 'Tamil',
    'te': 'Telugu'
}


def contains_indian_script(text: str) -> bool:
    """
    Check if text contains Indian language script characters.
    Optimized for high-performance voice applications with LRU cache.

    Args:
        text: Input text to check

    Returns:
        True if text contains Indian script characters, False otherwise
    """
    if not text:
        return False

    # Fast path: check optimized LRU cache first
    cache_key = hash(text)
    cached_result = _language_cache.get(cache_key)
    if cached_result is not None:
        return cached_result

    # Check for Indian script characters using optimized ranges
    for char in text:
        code = ord(char)
        for start, end in INDIAN_SCRIPT_RANGES.values():
            if start <= code <= end:
                # Cache the positive result using optimized cache
                _language_cache.put(cache_key, True)
                return True

    # Cache negative result using optimized cache
    _language_cache.put(cache_key, False)
    return False


def detect_script_type(text: str) -> Optional[str]:
    """
    Detect the primary script type in the text.
    Returns the script name with highest character count.
    
    Args:
        text: Input text to analyze
        
    Returns:
        Script name or None if no clear winner
    """
    if not text:
        return None
    
    script_counts = {script: 0 for script in INDIAN_SCRIPT_RANGES.keys()}
    script_counts['latin'] = 0  # For English
    
    # Count characters by script
    for char in text:
        code = ord(char)

        # Check Indian scripts first
        found_indian = False
        for script, (start, end) in INDIAN_SCRIPT_RANGES.items():
            if start <= code <= end:
                script_counts[script] += 1
                found_indian = True
                break

        # Check Latin characters using Unicode categories for comprehensive detection
        # This includes extended Latin characters like accented letters (á, é, ñ, etc.)
        if not found_indian:
            # Use Unicode category to detect all Latin script characters
            if unicodedata.category(char).startswith('L') and code < 0x0900:
                script_counts['latin'] += 1
    
    # Find script with highest count
    max_script = max(script_counts.items(), key=lambda x: x[1])
    script_name, count = max_script
    
    # Only return if we have a clear winner with sufficient characters
    total_chars = sum(script_counts.values())
    if count > 0 and total_chars > 0 and (count / total_chars) > 0.5:
        return script_name
    
    return None


def detect_language_by_script(text: str) -> Optional[str]:
    """
    Detect language based on script/character set.
    Fast first-pass for Indian language detection.
    
    Args:
        text: Input text
        
    Returns:
        Language code or None
    """
    script = detect_script_type(text)
    if script:
        if script == 'latin':
            return 'en'
        return SCRIPT_TO_LANGUAGE.get(script)
    return None


@lru_cache(maxsize=5000)
def _check_indian_script_cached(text: str) -> bool:
    """
    LRU cached version of Indian script detection.
    Alternative high-performance implementation using functools.lru_cache.

    Args:
        text: Input text to check

    Returns:
        True if text contains Indian script characters, False otherwise
    """
    if not text:
        return False

    # Check for Indian script characters using optimized ranges
    for char in text:
        code = ord(char)
        for start, end in INDIAN_SCRIPT_RANGES.values():
            if start <= code <= end:
                return True

    return False


@lru_cache(maxsize=1000)
def get_script_confidence(text: str) -> Dict[str, float]:
    """
    Get confidence scores for each script in the text.
    Cached for performance with LRU eviction.
    
    Args:
        text: Input text
        
    Returns:
        Dictionary mapping script names to confidence scores (0.0-1.0)
    """
    if not text:
        return {}
    
    script_counts = {script: 0 for script in INDIAN_SCRIPT_RANGES.keys()}
    script_counts['latin'] = 0
    total_chars = 0
    
    # Count characters by script
    for char in text:
        code = ord(char)
        total_chars += 1

        # Check Indian scripts first
        found_indian = False
        for script, (start, end) in INDIAN_SCRIPT_RANGES.items():
            if start <= code <= end:
                script_counts[script] += 1
                found_indian = True
                break

        # Check Latin characters using Unicode categories for comprehensive detection
        # This includes extended Latin characters like accented letters (á, é, ñ, etc.)
        if not found_indian:
            # Use Unicode category to detect all Latin script characters
            if unicodedata.category(char).startswith('L') and code < 0x0900:
                script_counts['latin'] += 1
    
    # Calculate confidence scores
    if total_chars == 0:
        return {}
    
    return {script: count / total_chars for script, count in script_counts.items() if count > 0}


def is_mixed_script(text: str, threshold: float = 0.3) -> bool:
    """
    Check if text contains mixed scripts (multiple languages).
    
    Args:
        text: Input text
        threshold: Minimum proportion for a script to be considered significant
        
    Returns:
        True if text contains multiple significant scripts
    """
    confidence_scores = get_script_confidence(text)
    significant_scripts = [script for script, score in confidence_scores.items() if score >= threshold]
    return len(significant_scripts) > 1


async def detect_language_async(text: str, hospital_id: str = None) -> Dict[str, Any]:
    """
    Asynchronous language detection with hospital-specific optimizations.
    
    Args:
        text: Input text
        hospital_id: Hospital identifier for context-aware detection
        
    Returns:
        Dictionary with detection results
    """
    try:
        # Run detection in thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        with ThreadPoolExecutor(max_workers=2) as executor:
            # Run script detection and confidence calculation concurrently
            script_task = loop.run_in_executor(executor, detect_script_type, text)
            confidence_task = loop.run_in_executor(executor, get_script_confidence, text)
            
            script, confidence_scores = await asyncio.gather(script_task, confidence_task)
        
        # Determine primary language
        primary_language = None
        if script:
            if script == 'latin':
                primary_language = 'en'
            else:
                primary_language = SCRIPT_TO_LANGUAGE.get(script)
        
        return {
            'primary_language': primary_language,
            'script': script,
            'confidence_scores': confidence_scores,
            'has_indian_script': contains_indian_script(text),
            'is_mixed_script': is_mixed_script(text),
            'hospital_id': hospital_id
        }
        
    except Exception as e:
        logger.error(f"Error in async language detection: {e}")
        return {
            'primary_language': None,
            'script': None,
            'confidence_scores': {},
            'has_indian_script': False,
            'is_mixed_script': False,
            'error': str(e)
        }


def normalize_text_for_language(text: str, language: str) -> str:
    """
    Normalize text for specific language processing.
    Handles language-specific text cleaning and preparation.
    
    Args:
        text: Input text
        language: Target language code
        
    Returns:
        Normalized text
    """
    if not text:
        return ""
    
    # Basic normalization
    normalized = text.strip()
    
    # Language-specific normalization
    if language in ['hi', 'mr']:  # Devanagari scripts
        # Remove common Devanagari punctuation and normalize
        normalized = re.sub(r'[।॥]', '.', normalized)
    elif language == 'ta':  # Tamil
        # Tamil-specific normalization
        # Note: Preserving virama (்) as it's essential for correct Tamil pronunciation and meaning
        # The virama is a critical combining character that affects semantic content
        pass
    elif language == 'te':  # Telugu
        # Telugu-specific normalization
        pass  # Add Telugu-specific rules as needed
    
    return normalized


def get_supported_languages() -> List[str]:
    """
    Get list of supported language codes.
    
    Returns:
        List of supported language codes
    """
    return list(SUPPORTED_INDIC_LANGUAGES.keys())


def get_language_name(language_code: str) -> str:
    """
    Get human-readable language name from code.
    
    Args:
        language_code: Language code (e.g., 'hi', 'en')
        
    Returns:
        Language name or 'Unknown' if not found
    """
    return SUPPORTED_INDIC_LANGUAGES.get(language_code, 'Unknown')


def get_language_cache_stats() -> Dict[str, Any]:
    """
    Get language cache statistics for monitoring and optimization.

    Returns:
        Dictionary with cache statistics
    """
    stats = {
        'custom_cache_size': _language_cache.size(),
        'custom_cache_max_size': _language_cache.max_size,
        'lru_cache_info': {}
    }

    # Get LRU cache statistics
    try:
        script_confidence_info = get_script_confidence.cache_info()
        stats['lru_cache_info']['get_script_confidence'] = {
            'hits': script_confidence_info.hits,
            'misses': script_confidence_info.misses,
            'maxsize': script_confidence_info.maxsize,
            'currsize': script_confidence_info.currsize,
            'hit_rate': script_confidence_info.hits / (script_confidence_info.hits + script_confidence_info.misses) if (script_confidence_info.hits + script_confidence_info.misses) > 0 else 0.0
        }

        indian_script_info = _check_indian_script_cached.cache_info()
        stats['lru_cache_info']['_check_indian_script_cached'] = {
            'hits': indian_script_info.hits,
            'misses': indian_script_info.misses,
            'maxsize': indian_script_info.maxsize,
            'currsize': indian_script_info.currsize,
            'hit_rate': indian_script_info.hits / (indian_script_info.hits + indian_script_info.misses) if (indian_script_info.hits + indian_script_info.misses) > 0 else 0.0
        }
    except Exception as e:
        logger.warning(f"Error getting LRU cache stats: {e}")
        stats['lru_cache_info']['error'] = str(e)

    return stats


def clear_language_cache():
    """
    Clear the language detection cache.
    Useful for testing or memory management.
    """
    global _language_cache
    _language_cache.clear()

    # Clear LRU caches as well
    get_script_confidence.cache_clear()
    _check_indian_script_cached.cache_clear()

    logger.info("Cleared all language detection caches")


def contains_indian_script_lru(text: str) -> bool:
    """
    Alternative implementation using pure LRU cache for comparison.
    This can be used for A/B testing performance differences.

    Args:
        text: Input text to check

    Returns:
        True if text contains Indian script characters, False otherwise
    """
    return _check_indian_script_cached(text)


# Hospital-specific language preferences (can be extended)
HOSPITAL_LANGUAGE_PREFERENCES = {
    # This can be populated from database or configuration
    # Format: hospital_id -> [preferred_languages_in_order]
}


def get_hospital_preferred_languages(hospital_id: str) -> List[str]:
    """
    Get preferred languages for a specific hospital.
    
    Args:
        hospital_id: Hospital identifier
        
    Returns:
        List of preferred language codes in order of preference
    """
    return HOSPITAL_LANGUAGE_PREFERENCES.get(hospital_id, ['hi', 'en'])  # Default to Hindi and English


def set_hospital_language_preferences(hospital_id: str, languages: List[str]):
    """
    Set language preferences for a hospital.
    
    Args:
        hospital_id: Hospital identifier
        languages: List of language codes in order of preference
    """
    HOSPITAL_LANGUAGE_PREFERENCES[hospital_id] = languages
