import { Clock } from 'react-feather';
import DoctorBookingLimits from '../DoctorBookingLimits';
import AvailabilityManagement from '../AvailabilityManagement';

export default function BookingLimits({ user }) {
  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">Booking Limits Management</h2>
            <p className="text-sm text-gray-600 mt-1">
              Manage daily appointment limits for doctors. Changes are automatically synced with the voice agent system.
            </p>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <Clock className="w-4 h-4" />
            <span>Real-time sync enabled</span>
          </div>
        </div>

        {/* Role-based access notice */}
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">Access Information</h3>
              <div className="mt-2 text-sm text-blue-700">
                <p>
                  {user?.role === 'admin'
                    ? 'As an admin, you can modify booking limits for all doctors in your hospital.'
                    : 'As a receptionist, you can modify booking limits to help manage daily appointments efficiently.'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Booking Limits Component */}
        <DoctorBookingLimits
          hospitalId={user?.hospital_id}
          userRole={user?.role}
        />
      </div>

      {/* Availability Management Component */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">Daily Availability Management</h2>
            <p className="text-sm text-gray-600 mt-1">
              Control which doctors and tests are available on specific dates. Changes are automatically synced with the voice agent system.
            </p>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <Clock className="w-4 h-4" />
            <span>Real-time sync enabled</span>
          </div>
        </div>

        {/* Role-based access notice */}
        <div className="bg-green-50 border border-green-200 rounded-md p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-800">Availability Control</h3>
              <div className="mt-2 text-sm text-green-700">
                <p>
                  {user?.role === 'admin'
                    ? 'As an admin, you can control the daily availability of all doctors and tests in your hospital.'
                    : 'As a receptionist, you can manage daily availability to help patients get accurate information during voice calls.'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Availability Management Component */}
        <AvailabilityManagement
          hospitalId={user?.hospital_id}
          userRole={user?.role}
        />
      </div>
    </div>
  );
}
