# This file has been deprecated and replaced with the unified LLM service
# All LLM functionality has been moved to shared/llm_service.py
# This file is kept for backward compatibility but should not be used

import logging

logger = logging.getLogger("llm_cost_optimizer_deprecated")
logger.warning("llm_cost_optimizer.py is deprecated. Use shared/llm_service.py instead.")

# Placeholder functions for backward compatibility
def get_usage_stats():
    """Deprecated: Use shared.llm_service.get_usage_stats() instead"""
    logger.warning("get_usage_stats() is deprecated. Use shared.llm_service.get_usage_stats() instead.")
    return {"deprecated": True}

def reset_usage_stats():
    """Deprecated: Use shared.llm_service.reset_usage_stats() instead"""
    logger.warning("reset_usage_stats() is deprecated. Use shared.llm_service.reset_usage_stats() instead.")
    pass