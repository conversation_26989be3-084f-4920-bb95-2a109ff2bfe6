"""
Unified LLM Service for Voice Health Portal

This module provides a unified interface for LLM operations using GPT-4.1 nano
with function calling instead of fine-tuning. It's designed to be used by both
the voice_agent and whatsapp_agent applications.

Features:
- Async OpenAI client for better performance
- Function calling for hospital-specific operations
- Multilingual support (Hindi, Bengali, English)
- Redis caching for cost optimization
- Production-ready error handling
"""

import os
import json
import asyncio
import logging
from typing import Dict, Any, List, Optional, Union, Callable
from datetime import datetime
import hashlib
from dotenv import load_dotenv
from pathlib import Path

# Load environment variables from .env file in shared directory
shared_dir = Path(__file__).parent
env_path = shared_dir / '.env'
load_dotenv(dotenv_path=env_path)

try:
    from openai import AsyncOpenAI
except ImportError:
    AsyncOpenAI = None

# Configure logging
logger = logging.getLogger(__name__)

class LLMService:
    """
    Unified LLM service using GPT-4.1 nano with function calling.
    Replaces the previous fine-tuning approach with a more flexible function calling system.
    """
    
    def __init__(self):
        """Initialize the LLM service with AsyncOpenAI client."""
        self.client = None
        self.model = "gpt-4o-mini"  # GPT-4.1-nano (using gpt-4o-mini as the actual model name)
        self.functions = {}
        self.usage_stats = {
            "total_calls": 0,
            "cached_calls": 0,
            "total_tokens": 0,
            "estimated_cost": 0.0
        }
        
        # Initialize OpenAI client
        self._initialize_client()
        
        # Register default functions
        self._register_default_functions()
    
    def _initialize_client(self):
        """Initialize AsyncOpenAI client."""
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            logger.error("OPENAI_API_KEY environment variable not set")
            return
        
        if AsyncOpenAI is None:
            logger.error("AsyncOpenAI not available. Please install openai>=1.0.0")
            return
        
        try:
            self.client = AsyncOpenAI(api_key=api_key)
            logger.info("AsyncOpenAI client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize AsyncOpenAI client: {e}")
    
    def _register_default_functions(self):
        """Register default functions that can be called by the LLM."""
        # Hospital information functions
        self.register_function(
            name="get_hospital_info",
            description="Get basic information about a hospital",
            parameters={
                "type": "object",
                "properties": {
                    "hospital_id": {"type": "string", "description": "Hospital ID"}
                },
                "required": ["hospital_id"]
            }
        )
        
        # Doctor-related functions
        self.register_function(
            name="get_available_doctors",
            description="Get list of available doctors for a specific date",
            parameters={
                "type": "object",
                "properties": {
                    "hospital_id": {"type": "string", "description": "Hospital ID"},
                    "date": {"type": "string", "description": "Date in YYYY-MM-DD format"}
                },
                "required": ["hospital_id"]
            }
        )
        
        self.register_function(
            name="get_doctor_schedule",
            description="Get schedule and availability for a specific doctor",
            parameters={
                "type": "object",
                "properties": {
                    "hospital_id": {"type": "string", "description": "Hospital ID"},
                    "doctor_id": {"type": "string", "description": "Doctor ID"},
                    "date": {"type": "string", "description": "Date in YYYY-MM-DD format"}
                },
                "required": ["hospital_id", "doctor_id"]
            }
        )
        
        # Test-related functions
        self.register_function(
            name="get_available_tests",
            description="Get list of available medical tests",
            parameters={
                "type": "object",
                "properties": {
                    "hospital_id": {"type": "string", "description": "Hospital ID"},
                    "date": {"type": "string", "description": "Date in YYYY-MM-DD format"}
                },
                "required": ["hospital_id"]
            }
        )
        
        # Booking functions
        self.register_function(
            name="book_appointment",
            description="Book an appointment with a doctor",
            parameters={
                "type": "object",
                "properties": {
                    "hospital_id": {"type": "string", "description": "Hospital ID"},
                    "patient_name": {"type": "string", "description": "Patient name"},
                    "phone": {"type": "string", "description": "Patient phone number"},
                    "doctor_id": {"type": "string", "description": "Doctor ID"},
                    "date": {"type": "string", "description": "Appointment date"},
                    "time": {"type": "string", "description": "Appointment time"}
                },
                "required": ["hospital_id", "patient_name", "phone", "doctor_id", "date", "time"]
            }
        )
        
        self.register_function(
            name="book_test",
            description="Book a medical test",
            parameters={
                "type": "object",
                "properties": {
                    "hospital_id": {"type": "string", "description": "Hospital ID"},
                    "patient_name": {"type": "string", "description": "Patient name"},
                    "phone": {"type": "string", "description": "Patient phone number"},
                    "test_id": {"type": "string", "description": "Test ID"},
                    "date": {"type": "string", "description": "Test date"},
                    "time": {"type": "string", "description": "Test time"}
                },
                "required": ["hospital_id", "patient_name", "phone", "test_id", "date", "time"]
            }
        )
    
    def register_function(self, name: str, description: str, parameters: Dict[str, Any], 
                         handler: Optional[Callable] = None):
        """
        Register a function that can be called by the LLM.
        
        Args:
            name: Function name
            description: Function description for the LLM
            parameters: JSON schema for function parameters
            handler: Optional function handler (if None, will look for handler in function_handlers)
        """
        self.functions[name] = {
            "name": name,
            "description": description,
            "parameters": parameters,
            "handler": handler
        }
        logger.info(f"Registered function: {name}")
    
    def set_function_handlers(self, handlers: Dict[str, Callable]):
        """
        Set function handlers for the registered functions.

        Args:
            handlers: Dictionary mapping function names to handler functions
        """
        for name, handler in handlers.items():
            if name in self.functions:
                self.functions[name]["handler"] = handler
                logger.info(f"Set handler for function: {name}")
            else:
                logger.warning(f"Function {name} not registered, cannot set handler")

    async def process_message(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a message using the LLM with function calling capabilities.

        Args:
            message: User message to process
            context: Context information (hospital_id, language, user_info, etc.)

        Returns:
            Dict containing response and any function call results
        """
        if not self.client:
            raise Exception("OpenAI client not initialized")

        try:
            # Extract context information
            hospital_id = context.get("hospital_id")
            language = context.get("language", "en")
            user_info = context.get("user_info", {})
            chat_history = context.get("chat_history", [])

            # Create system prompt
            system_prompt = self._create_system_prompt(hospital_id, language, context)

            # Prepare messages
            messages = [{"role": "system", "content": system_prompt}]

            # Add chat history if available
            if chat_history:
                messages.extend(self._format_chat_history(chat_history))

            # Add current message
            messages.append({"role": "user", "content": message})

            # Prepare function definitions for OpenAI (exclude handler from serialization)
            tools = []
            for func_name, func_data in self.functions.items():
                tool_def = {
                    "type": "function",
                    "function": {
                        "name": func_data["name"],
                        "description": func_data["description"],
                        "parameters": func_data["parameters"]
                    }
                }
                tools.append(tool_def)

            # Call OpenAI API
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                tools=tools if tools else None,
                tool_choice="auto" if tools else None,
                temperature=0.7,
                max_tokens=1000
            )

            # Update usage stats
            self._update_usage_stats(response.usage)

            # Process response
            result = await self._process_response(response, context)

            return result

        except Exception as e:
            logger.error(f"Error processing message: {e}")
            return {
                "success": False,
                "error": str(e),
                "response": "I apologize, but I'm experiencing technical difficulties. Please try again later."
            }

    def _create_system_prompt(self, hospital_id: str, language: str, context: Dict[str, Any]) -> str:
        """Create system prompt based on context."""
        hospital_name = context.get("hospital_name", "the hospital")

        # Base prompt in English
        base_prompt = f"""You are MEGHA, an AI assistant for {hospital_name}. Your role is to help patients with:

1. Scheduling doctor appointments
2. Booking medical tests
3. Providing hospital information
4. Answering general queries about services

IMPORTANT GUIDELINES:
- Always be helpful, professional, and empathetic
- Use the available functions to get real-time information
- Do NOT provide medical advice or diagnoses
- Respond in {self._get_language_name(language)} language
- If you need to book something, always collect: patient name, phone number, preferred date/time
- Confirm all booking details before finalizing

Available functions allow you to:
- Get hospital information and services
- Check doctor availability and schedules
- Get available medical tests
- Book appointments and tests
- Check booking limits and availability

Always use functions to get current information rather than making assumptions."""

        # Add language-specific instructions
        if language == "hi":
            base_prompt += "\n\nPlease respond in Hindi (हिंदी) and be culturally appropriate for Indian patients."
        elif language == "bn":
            base_prompt += "\n\nPlease respond in Bengali (বাংলা) and be culturally appropriate for Bengali-speaking patients."

        return base_prompt

    def _get_language_name(self, language_code: str) -> str:
        """Get language name from code."""
        language_names = {
            "en": "English",
            "hi": "Hindi",
            "bn": "Bengali"
        }
        return language_names.get(language_code, "English")

    def _format_chat_history(self, chat_history: List[Dict[str, Any]]) -> List[Dict[str, str]]:
        """Format chat history for OpenAI messages."""
        formatted = []
        for msg in chat_history[-10:]:  # Keep last 10 messages for context
            role = "user" if msg.get("direction") == "inbound" else "assistant"
            content = msg.get("message_text", msg.get("text", ""))
            if content:
                formatted.append({"role": role, "content": content})
        return formatted

    async def _process_response(self, response, context: Dict[str, Any]) -> Dict[str, Any]:
        """Process OpenAI response and handle function calls."""
        message = response.choices[0].message
        result = {
            "success": True,
            "response": message.content or "",
            "function_calls": [],
            "function_results": {}
        }

        # Handle function calls if present
        if message.tool_calls:
            for tool_call in message.tool_calls:
                if tool_call.type == "function":
                    function_name = tool_call.function.name
                    function_args = json.loads(tool_call.function.arguments)

                    # Execute function if handler is available
                    if function_name in self.functions:
                        handler = self.functions[function_name].get("handler")
                        if handler:
                            try:
                                # Call the function handler
                                if asyncio.iscoroutinefunction(handler):
                                    function_result = await handler(**function_args)
                                else:
                                    function_result = handler(**function_args)

                                result["function_calls"].append({
                                    "name": function_name,
                                    "arguments": function_args,
                                    "result": function_result
                                })
                                result["function_results"][function_name] = function_result

                            except Exception as e:
                                logger.error(f"Error executing function {function_name}: {e}")
                                result["function_calls"].append({
                                    "name": function_name,
                                    "arguments": function_args,
                                    "error": str(e)
                                })
                        else:
                            logger.warning(f"No handler found for function: {function_name}")
                    else:
                        logger.warning(f"Unknown function called: {function_name}")

        return result

    def _update_usage_stats(self, usage):
        """Update usage statistics."""
        if usage:
            self.usage_stats["total_calls"] += 1
            self.usage_stats["total_tokens"] += usage.total_tokens
            # Estimate cost (GPT-4o-mini/4.1-nano pricing: $0.000150 per 1K input tokens, $0.000600 per 1K output tokens)
            input_cost = (usage.prompt_tokens / 1000) * 0.000150
            output_cost = (usage.completion_tokens / 1000) * 0.000600
            self.usage_stats["estimated_cost"] += input_cost + output_cost

    def get_usage_stats(self) -> Dict[str, Any]:
        """Get current usage statistics."""
        return {
            "total_calls": self.usage_stats["total_calls"],
            "cached_calls": self.usage_stats["cached_calls"],
            "total_tokens": self.usage_stats["total_tokens"],
            "estimated_cost": f"${self.usage_stats['estimated_cost']:.4f}",
            "cache_hit_rate": f"{(self.usage_stats['cached_calls'] / max(1, self.usage_stats['total_calls'])) * 100:.2f}%"
        }

    def reset_usage_stats(self):
        """Reset usage statistics."""
        self.usage_stats = {
            "total_calls": 0,
            "cached_calls": 0,
            "total_tokens": 0,
            "estimated_cost": 0.0
        }
        logger.info("Usage statistics reset")

# Global instance
llm_service = LLMService()

# Convenience functions for backward compatibility and easy access
async def process_message(message: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """Process a message using the global LLM service instance."""
    return await llm_service.process_message(message, context)

def register_function(name: str, description: str, parameters: Dict[str, Any], handler: Optional[Callable] = None):
    """Register a function with the global LLM service instance."""
    llm_service.register_function(name, description, parameters, handler)

def set_function_handlers(handlers: Dict[str, Callable]):
    """Set function handlers for the global LLM service instance."""
    llm_service.set_function_handlers(handlers)

def get_usage_stats() -> Dict[str, Any]:
    """Get usage statistics from the global LLM service instance."""
    return llm_service.get_usage_stats()

def reset_usage_stats():
    """Reset usage statistics for the global LLM service instance."""
    llm_service.reset_usage_stats()
