import os
import logging
import j<PERSON>
from typing import Dict, Any, Optional

import requests
from fastapi import HTT<PERSON>Exception
from .language_config import language_config, get_primary_language

# Module-local logger – configuration stays with the host app
logger = logging.getLogger(__name__)

# Get Jambonz API credentials from environment variables
JAMBONZ_ACCOUNT_SID = os.environ.get("JAMBONZ_ACCOUNT_SID")
JAMBONZ_API_KEY = os.environ.get("JAMBONZ_API_KEY")
JAMBONZ_REST_API_BASE_URL = os.environ.get("JAMBONZ_REST_API_BASE_URL", "https://api.jambonz.org/v1")

def setup_jambonz():
    """
    Verify Jambonz credentials and setup necessary resources
    """
    if not all([JAMBONZ_ACCOUNT_SID, JAMBONZ_API_KEY, JAMBONZ_REST_API_BASE_URL]):
        logger.warning("Jambonz credentials not fully configured")
        return False
    
    try:
        # Test connection with a simple GET request
        response = requests.get(
            f"{JAMBONZ_REST_API_BASE_URL}/Applications",
            auth=(JAMBONZ_ACCOUNT_SID, JAMBONZ_API_KEY)
        )
        response.raise_for_status()
        logger.info("Jambonz connection successful")
        return True
    except Exception as e:
        logger.error(f"Error testing Jambonz connection: {e}")
        return False

async def handle_call(payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Process incoming call from Jambonz and return appropriate JAMBONZ response
    """
    try:
        # Extract required information from the payload
        call_id = payload.get('call_id')
        from_number = payload.get('from')
        to_number = payload.get('to')
        
        if not all([call_id, from_number, to_number]):
            raise ValueError("Missing required call parameters")
        
        # Welcome message in Hindi (primary language) with fallback
        primary_lang = get_primary_language()
        welcome_message = language_config.get_welcome_message(
            primary_lang,
            "अस्पताल",  # Hospital in Hindi
            ""  # No language options here, will be added in main flow
        )

        response = {
            "verb": "say",
            "text": welcome_message
        }
        
        return response
    except Exception as e:
        logger.error(f"Error handling call: {e}")
        raise HTTPException(status_code=500, detail=f"Error handling call: {str(e)}")

def send_sip_message(sip_trunk_config: Dict[str, Any], to_number: str, message: str) -> bool:
    """
    Send SIP message using the hospital's SIP trunk configuration
    """
    provider = sip_trunk_config.get('provider', '').lower()
    sip_endpoint = sip_trunk_config.get('sip_endpoint')
    auth_token = sip_trunk_config.get('auth_token')
    
    if not all([provider, sip_endpoint, auth_token]):
        logger.error("Incomplete SIP trunk configuration")
        return False
    
    try:
        # Implementation depends on the provider
        if provider == 'jio':
            # Example for Jio SIP trunk
            payload = {
                "from": sip_endpoint,
                "to": to_number,
                "body": message
            }
            
            headers = {
                "Authorization": f"Bearer {auth_token}",
                "Content-Type": "application/json"
            }
            
            response = requests.post(
                f"https://api.{provider}.com/sms/send",
                headers=headers,
                json=payload
            )
            
            if response.status_code == 200:
                logger.info(f"SIP message sent to {to_number}")
                return True
            else:
                logger.error(f"Failed to send SIP message: {response.text}")
                return False
        
        # Add support for other providers as needed
        else:
            logger.error(f"Unsupported SIP provider: {provider}")
            return False
    
    except Exception as e:
        logger.error(f"Error sending SIP message: {e}")
        return False

def make_outbound_call(sip_trunk_config: Dict[str, Any], from_number: str, to_number: str, webhook_url: str) -> Optional[str]:
    """
    Make an outbound call using Jambonz API
    Returns the call_id if successful, None otherwise
    """
    if not all([JAMBONZ_ACCOUNT_SID, JAMBONZ_API_KEY, JAMBONZ_REST_API_BASE_URL]):
        logger.error("Jambonz credentials not configured")
        return None
    
    try:
        # Prepare payload for outbound call
        payload = {
            "application_sid": os.environ.get("JAMBONZ_APPLICATION_SID"),
            "from": from_number,
            "to": to_number,
            "call_hook": webhook_url,
            "call_status_hook": f"{webhook_url}/status"
        }
        
        # Make API request
        response = requests.post(
            f"{JAMBONZ_REST_API_BASE_URL}/Calls",
            auth=(JAMBONZ_ACCOUNT_SID, JAMBONZ_API_KEY),
            json=payload
        )
        
        if response.status_code == 201:
            logger.info(f"Outbound call initiated to {to_number}")
            return response.json().get('sid')
        else:
            logger.error(f"Failed to initiate outbound call: {response.text}")
            return None
    
    except Exception as e:
        logger.error(f"Error making outbound call: {e}")
        return None

def send_sms(phone_number: str, message: str) -> bool:
    """
    Send SMS notification to patient
    This is a simplified implementation that would need to be replaced with an actual SMS gateway integration
    NOTE: This function is likely superseded by `dispatch_sms` for the main Jambonz flow.
    """
    try:
        # In a real implementation, integrate with an SMS gateway service
        # For now, just log the message
        logger.info(f"SMS notification would be sent to {phone_number}: {message}")
        return True
    except Exception as e:
        logger.error(f"Error sending SMS notification: {e}")
        return False

def format_phone_number(phone_number: str) -> str:
    """
    Format phone number to E.164 format for consistent processing
    """
    # Remove any non-digit characters
    digits_only = ''.join(filter(str.isdigit, phone_number))
    
    # Ensure country code
    if len(digits_only) == 10:  # Assuming 10-digit US/India number without country code
        return f"+91{digits_only}"  # Add India country code
    elif len(digits_only) > 10 and not digits_only.startswith('+'):
        return f"+{digits_only}"
    
    return phone_number

def extract_hospital_id_from_did(did: str) -> Optional[str]:
    '''
    Extracts the 10-digit hospital ID from the DID (phone number).
    Assumes the hospital ID is the last 10 digits of the numeric part of the DID.
    '''
    logger.info(f"Attempting to extract hospital ID from DID: {did} in telephony.py")

    if not did:
        logger.warning("DID is None or empty, cannot extract hospital ID.")
        return None

    # Clean the DID to get only numeric characters
    cleaned_did = ''.join(filter(str.isdigit, did))
    logger.info(f"Cleaned DID for hospital ID extraction: {cleaned_did}")

    # Check if the cleaned DID has at least 10 digits
    if len(cleaned_did) >= 10:
        # Take the last 10 digits as the hospital_id
        hospital_id = cleaned_did[-10:]
        logger.info(f"Extracted 10-digit hospital ID: {hospital_id} from DID: {did} (cleaned: {cleaned_did})")
        return hospital_id
    else:
        logger.warning(
            f"Cleaned DID '{cleaned_did}' (from original DID '{did}') is too short "
            f"to extract a 10-digit hospital ID. A minimum of 10 digits is required."
        )
        return None

def dispatch_sms(to_number: str, from_number: str, message: str, hospital_id: Optional[str] = None) -> Dict[str, Any]:
    """
    Dispatch SMS via carrier-specific mechanism.
    This function is a placeholder for the actual SMS sending logic,
    which would typically involve integrating with a specific SMS gateway or carrier API.
    """
    logger.info(
        f"dispatch_sms called with to_number: {to_number}, from_number: {from_number}, "
        f"message_length: {len(message)}, hospital_id: {hospital_id}"
    )
    logger.warning("This is a placeholder function for dispatch_sms. No actual SMS is sent.")
    
    # Here, you would implement logic to:
    # 1. Look up hospital-specific SIP trunk or SMS gateway configuration using hospital_id.
    # 2. Format the request according to the carrier's API.
    # 3. Make the API call to send the SMS.
    # 4. Handle the response from the carrier.

    # For now, returning a simulated success.
    return {
        "success": True,
        "status": "SIMULATED_DISPATCH_SUCCESS",
        "details": "SMS dispatch placeholder: Real carrier integration needed."
    }
