import { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { User, Calendar, Activity, FileText, LogOut, Home, Menu, X, Search, Settings, Bell } from 'react-feather';
import Header from './ui/Header';

export default function Layout({ children, title = 'Staff Portal', user = null, notificationCount = 0 }) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const router = useRouter();
  const [currentUser, setCurrentUser] = useState(user);
  
  // Fetch user if not provided
  useEffect(() => {
    if (!user) {
      const checkAuth = async () => {
        try {
          const res = await fetch('/api/auth/status');
          const data = await res.json();
          
          if (data.success) {
            setCurrentUser(data.user);
          }
        } catch (error) {
          console.error('Auth check error:', error);
        }
      };
      
      checkAuth();
    }
  }, [user]);
  
  const handleLogout = async () => {
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
      });
      router.push('/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };
  
  const handleSearch = (searchTerm) => {
    router.push(`/search?term=${encodeURIComponent(searchTerm)}`);
  };
  
  const menuItems = [
    { name: 'Dashboard', href: '/dashboard', icon: Home },
    { name: 'Appointments', href: '/appointments', icon: Calendar },
    { name: 'Search', href: '/search', icon: Search },
  ];
  
  // Add admin link if user has admin role
  if (currentUser && currentUser.role === 'admin') {
    menuItems.push({ name: 'Admin', href: '/admin', icon: Settings });
  }

  return (
    <div className="min-h-screen bg-slate-50 font-sans">
      <Head>
        <title>{title} | Voice Health Portal</title>
        <meta name="description" content="Voice Health Portal Staff Interface" />
        <link rel="icon" href="/favicon.ico" />
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet" />
      </Head>

      {/* Header */}
      <Header 
        user={currentUser} 
        notificationCount={notificationCount} 
        onLogout={handleLogout} 
        onSearch={handleSearch}
      />

      <div className="flex min-h-screen pt-16">
        {/* Mobile sidebar backdrop */}
        {sidebarOpen && (
          <div 
            className="fixed inset-0 z-20 bg-slate-900 bg-opacity-50 lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        {/* Sidebar */}
        <div className={`fixed top-16 left-0 z-30 h-[calc(100vh-4rem)] w-64 bg-white shadow-md transform ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'} transition-transform duration-300 ease-in-out lg:translate-x-0`}>
          <div className="p-6 border-b border-slate-200">
            <h2 className="text-lg font-semibold text-slate-800">Staff Portal</h2>
            {currentUser && (
              <div className="mt-2 text-sm text-slate-500">
                {currentUser.name} ({currentUser.role})
              </div>
            )}
            
            {/* Close button - mobile only */}
            <button 
              className="absolute top-5 right-5 text-slate-500 lg:hidden"
              onClick={() => setSidebarOpen(false)}
            >
              <X size={20} />
            </button>
          </div>
          
          <nav className="p-4">
            <ul className="space-y-1">
              {menuItems.map((item) => {
                const Icon = item.icon;
                const isActive = router.pathname === item.href;
                
                return (
                  <li key={item.name}>
                    <Link href={item.href}
                          className={`flex items-center px-4 py-3 rounded-md transition ${isActive ? 'bg-teal-50 text-teal-700 font-medium' : 'text-slate-600 hover:bg-slate-50 hover:text-slate-900'}`}
                          onClick={() => setSidebarOpen(false)}>
                      <Icon size={18} className="mr-3" />
                      <span>{item.name}</span>
                    </Link>
                  </li>
                );
              })}
              
              <li className="pt-6">
                <button 
                  onClick={handleLogout}
                  className="flex items-center w-full px-4 py-3 text-slate-600 rounded-md hover:bg-slate-50 hover:text-slate-900 transition"
                >
                  <LogOut size={18} className="mr-3" />
                  <span>Logout</span>
                </button>
              </li>
            </ul>
          </nav>
        </div>

        {/* Main content */}
        <div className="lg:ml-64 w-full min-h-screen flex flex-col">
          {/* Mobile header */}
          <div className="lg:hidden fixed top-16 left-0 right-0 z-10 bg-white shadow-sm border-b border-slate-200 px-4 py-2 flex items-center">
            <button 
              className="text-slate-500 mr-4"
              onClick={() => setSidebarOpen(!sidebarOpen)}
            >
              <Menu size={24} />
            </button>
            <h1 className="text-lg font-semibold text-slate-800">{title}</h1>
          </div>
          
          {/* Content area */}
          <main className="flex-grow p-4 sm:p-6 md:p-8 lg:p-10 mt-12 lg:mt-0">
            {children}
          </main>
          
          {/* Footer */}
          <footer className="bg-white p-4 border-t border-slate-200 text-center text-slate-500 text-sm">
            {new Date().getFullYear()} Voice Health Portal. All rights reserved.
          </footer>
        </div>
      </div>
    </div>
  );
}