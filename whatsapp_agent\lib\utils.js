/**
 * Utility functions for WhatsApp Agent
 */

import crypto from 'crypto';
import { logger } from './logger.js';

/**
 * Generate a 6-digit appointment ID based on phone number and timestamp
 * Format: last 2 digits of phone + 4 digits from timestamp
 * 
 * @param {string} phoneNumber - Customer's phone number
 * @returns {string} - 6-digit appointment ID
 */
export const generateAppointmentId = (phoneNumber) => {
  try {
    // Extract last 2 digits of phone number
    const phoneDigits = phoneNumber.replace(/\D/g, '');
    const lastTwoDigits = phoneDigits.slice(-2).padStart(2, '0');
    
    // Get current timestamp and extract 4 digits
    const timestamp = new Date().getTime();
    const timestampStr = timestamp.toString();
    const timeDigits = timestampStr.slice(-6, -2);
    
    // Combine to form 6-digit ID
    const appointmentId = `${lastTwoDigits}${timeDigits}`;
    
    logger.info(`Generated appointment ID: ${appointmentId} for phone: ${phoneNumber}`);
    return appointmentId;
  } catch (error) {
    logger.error(`Error generating appointment ID: ${error.message}`);
    // Fallback to random 6-digit number if there's an error
    return Math.floor(100000 + Math.random() * 900000).toString();
  }
};

/**
 * Generate a hash of a string
 * 
 * @param {string} text - Text to hash
 * @returns {string} - Hashed text
 */
export const generateHash = (text) => {
  return crypto.createHash('md5').update(text).digest('hex');
};

/**
 * Format a phone number for display
 * 
 * @param {string} phoneNumber - Phone number to format
 * @returns {string} - Formatted phone number
 */
export const formatPhoneNumber = (phoneNumber) => {
  // Remove all non-digit characters
  const digits = phoneNumber.replace(/\D/g, '');
  
  // Format based on length
  if (digits.length === 10) {
    return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`;
  } else if (digits.length > 10) {
    // Assume international number
    return `+${digits.slice(0, digits.length - 10)} (${digits.slice(-10, -7)}) ${digits.slice(-7, -4)}-${digits.slice(-4)}`;
  }
  
  // Return as is if we can't format it
  return phoneNumber;
};

/**
 * Parse date string to Date object
 * 
 * @param {string} dateStr - Date string (YYYY-MM-DD)
 * @param {string} timeStr - Time string (HH:MM)
 * @returns {Date} - Date object
 */
export const parseDateTime = (dateStr, timeStr) => {
  try {
    return new Date(`${dateStr}T${timeStr}`);
  } catch (error) {
    logger.error(`Error parsing date/time: ${error.message}`);
    return new Date();
  }
};

export default {
  generateAppointmentId,
  generateHash,
  formatPhoneNumber,
  parseDateTime
};
