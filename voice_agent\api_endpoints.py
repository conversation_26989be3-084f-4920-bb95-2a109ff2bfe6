"""
API Endpoints for Voice Agent Configuration Management

This module provides REST API endpoints for the voice agent to receive
configuration updates from the staff portal and manage cached data.
"""

import logging
import asyncio
import re
from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, Request
from pydantic import BaseModel, field_validator
import json

from .appointment_scheduler import appointment_scheduler
from .database import get_cached_data, set_cached_data, delete_cached_data
from shared.redis.migration_helper import get_shared_redis_manager

logger = logging.getLogger(__name__)

# Initialize shared Redis manager
redis_manager = get_shared_redis_manager()

# Create API router
router = APIRouter(prefix="/api", tags=["configuration"])

# Request models
class ConfigRefreshRequest(BaseModel):
    hospital_id: Optional[str] = None

    @field_validator('hospital_id')
    @classmethod
    def validate_hospital_id(cls, v):
        if v is not None:
            # Validate hospital ID format (alphanumeric with underscores and hyphens)
            if not re.match(r'^[a-zA-Z0-9_-]+$', v):
                raise ValueError('Invalid hospital ID format. Only alphanumeric characters, underscores, and hyphens are allowed.')
            # Ensure reasonable length limits for security
            if len(v) < 1 or len(v) > 50:
                raise ValueError('Hospital ID must be between 1 and 50 characters long.')
        return v

class AvailabilityRefreshRequest(BaseModel):
    hospital_id: str
    date: Optional[str] = None

    @field_validator('hospital_id')
    @classmethod
    def validate_hospital_id(cls, v):
        # Validate hospital ID format (alphanumeric with underscores and hyphens)
        if not re.match(r'^[a-zA-Z0-9_-]+$', v):
            raise ValueError('Invalid hospital ID format. Only alphanumeric characters, underscores, and hyphens are allowed.')
        # Ensure reasonable length limits for security
        if len(v) < 1 or len(v) > 50:
            raise ValueError('Hospital ID must be between 1 and 50 characters long.')
        return v

    @field_validator('date')
    @classmethod
    def validate_date(cls, v):
        if v is not None:
            # Validate date format (YYYY-MM-DD)
            try:
                datetime.strptime(v, '%Y-%m-%d')
            except ValueError:
                raise ValueError('Date must be in YYYY-MM-DD format')
        return v

class PerformanceMetricsResponse(BaseModel):
    success: bool
    data: Dict[str, Any]

# Authentication dependency (simple internal API key check)
async def verify_internal_api_key(request: Request):
    """Verify internal API key for staff portal communication."""
    import os
    
    expected_key = os.getenv('INTERNAL_API_KEY')
    if not expected_key:
        # If no API key is configured, allow access (development mode)
        return True
    
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        raise HTTPException(status_code=401, detail="Missing or invalid authorization header")
    
    provided_key = auth_header.replace('Bearer ', '')
    if provided_key != expected_key:
        raise HTTPException(status_code=401, detail="Invalid API key")
    
    return True

@router.post("/scheduling/config/refresh")
async def refresh_scheduling_config(
    request: ConfigRefreshRequest,
    _: bool = Depends(verify_internal_api_key)
):
    """
    Refresh scheduling configuration cache for a specific hospital or all hospitals.
    Called by staff portal when configuration is updated.
    """
    try:
        logger.info(f"Received scheduling config refresh request for hospital: {request.hospital_id}")
        
        if request.hospital_id:
            # Refresh specific hospital configuration
            cache_key = f"hospital_config:{request.hospital_id}"
            await delete_cached_data(cache_key)
            
            # Force refresh by fetching new configuration
            config = await appointment_scheduler._get_hospital_scheduling_config(request.hospital_id)
            
            logger.info(f"Refreshed scheduling config for hospital {request.hospital_id}")
            return {
                "success": True,
                "message": f"Scheduling configuration refreshed for hospital {request.hospital_id}",
                "hospital_id": request.hospital_id,
                "config": config
            }
        else:
            # Refresh all hospital configurations
            # Clear all hospital config cache entries using batched operations
            total_deleted = 0  # Initialize variable at proper scope
            try:
                total_deleted = await redis_manager.batched_scan_delete_async("hospital_config:*")
                logger.info(f"Cleared {total_deleted} hospital config cache entries using optimized batched operations")

            except Exception as cache_error:
                logger.warning(f"Error clearing hospital config cache: {cache_error}")
                total_deleted = 0

            logger.info("Refreshed scheduling config for all hospitals")
            return {
                "success": True,
                "message": "Scheduling configuration refreshed for all hospitals",
                "cleared_entries": total_deleted
            }
            
    except Exception as e:
        logger.error(f"Error refreshing scheduling configuration: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to refresh configuration: {str(e)}")

@router.post("/availability/refresh")
async def refresh_availability_data(
    request: AvailabilityRefreshRequest,
    _: bool = Depends(verify_internal_api_key)
):
    """
    Refresh availability data cache for a specific hospital and date.
    Called by staff portal when availability is updated.
    """
    try:
        logger.info(f"Received availability refresh request for hospital: {request.hospital_id}, date: {request.date}")
        
        # Clear relevant cache entries
        cache_patterns = [
            f"time_slots:{request.hospital_id}:*",
            f"test_slots:{request.hospital_id}:*",
            f"doctor_data:{request.hospital_id}:*",
            f"test_data:{request.hospital_id}:*"
        ]
        
        if request.date:
            # Clear specific date entries
            cache_patterns.extend([
                f"time_slots:{request.hospital_id}:*:{request.date}",
                f"test_slots:{request.hospital_id}:*:{request.date}"
            ])
        
        cleared_count = 0
        try:
            # Use batched scan delete for each pattern
            for pattern in cache_patterns:
                pattern_deleted = await redis_manager.batched_scan_delete_async(pattern)
                cleared_count += pattern_deleted
                if pattern_deleted > 0:
                    logger.debug(f"Cleared {pattern_deleted} cache entries for pattern: {pattern}")

        except Exception as cache_error:
            logger.warning(f"Error clearing availability cache: {cache_error}")
        
        logger.info(f"Refreshed availability data for hospital {request.hospital_id}, cleared {cleared_count} cache entries")
        return {
            "success": True,
            "message": f"Availability data refreshed for hospital {request.hospital_id}",
            "hospital_id": request.hospital_id,
            "date": request.date,
            "cleared_entries": cleared_count
        }
        
    except Exception as e:
        logger.error(f"Error refreshing availability data: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to refresh availability data: {str(e)}")

@router.get("/scheduling/performance-metrics")
async def get_performance_metrics(_: bool = Depends(verify_internal_api_key)):
    """
    Get performance metrics for the appointment scheduling system.
    Used by staff portal for monitoring and diagnostics.
    """
    try:
        metrics = appointment_scheduler.get_performance_metrics()
        
        return {
            "success": True,
            "data": metrics,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting performance metrics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get performance metrics: {str(e)}")

@router.post("/scheduling/performance-metrics/reset")
async def reset_performance_metrics(_: bool = Depends(verify_internal_api_key)):
    """
    Reset performance metrics for the appointment scheduling system.
    Used by staff portal for maintenance and testing.
    """
    try:
        appointment_scheduler.reset_performance_metrics()
        
        logger.info("Performance metrics reset successfully")
        return {
            "success": True,
            "message": "Performance metrics reset successfully",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error resetting performance metrics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to reset performance metrics: {str(e)}")

@router.get("/health")
async def health_check():
    """
    Health check endpoint for monitoring the voice agent API.
    """
    try:
        # Check if appointment scheduler is working
        metrics = appointment_scheduler.get_performance_metrics()
        
        return {
            "success": True,
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "appointment_scheduler": "operational",
            "total_requests": metrics.get('total_requests', 0)
        }
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "success": False,
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }
