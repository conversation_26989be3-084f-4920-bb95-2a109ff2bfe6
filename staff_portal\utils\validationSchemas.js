/**
 * Validation schemas for different entity types
 */

// Common validation utilities
export const validators = {
  email: (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },
  
  phone: (phone) => {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
    return phoneRegex.test(cleanPhone) && cleanPhone.length >= 10;
  },
  
  password: (password) => {
    return {
      minLength: password.length >= 8,
      hasUppercase: /[A-Z]/.test(password),
      hasLowercase: /[a-z]/.test(password),
      hasNumber: /[0-9]/.test(password),
      hasSpecialChar: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)
    };
  },
  
  required: (value) => {
    return value !== null && value !== undefined && value.toString().trim() !== '';
  },
  
  minLength: (value, min) => {
    return value && value.toString().length >= min;
  },
  
  maxLength: (value, max) => {
    return !value || value.toString().length <= max;
  }
};

// Staff validation schema
export const staffValidationSchema = {
  name: {
    required: true,
    minLength: 2,
    maxLength: 100,
    validate: (value) => {
      const errors = [];
      if (!validators.required(value)) {
        errors.push('Name is required');
      } else {
        const trimmed = value.trim();
        if (!validators.minLength(trimmed, 2)) {
          errors.push('Name must be at least 2 characters long');
        }
        if (!validators.maxLength(trimmed, 100)) {
          errors.push('Name must be less than 100 characters');
        }
      }
      return errors;
    }
  },
  
  email: {
    required: true,
    validate: (value) => {
      const errors = [];
      if (!validators.required(value)) {
        errors.push('Email is required');
      } else if (!validators.email(value.trim())) {
        errors.push('Please enter a valid email address');
      }
      return errors;
    }
  },
  
  role: {
    required: true,
    allowedValues: ['admin', 'doctor', 'receptionist', 'nurse', 'lab_technician'],
    validate: (value) => {
      const errors = [];
      if (!validators.required(value)) {
        errors.push('Role is required');
      } else if (!staffValidationSchema.role.allowedValues.includes(value)) {
        errors.push('Invalid role selected');
      }
      return errors;
    }
  },
  
  password: {
    required: true,
    validate: (value) => {
      const errors = [];
      if (!validators.required(value)) {
        errors.push('Password is required');
      } else {
        const checks = validators.password(value);
        if (!checks.minLength) {
          errors.push('Password must be at least 8 characters long');
        }
        if (!checks.hasUppercase) {
          errors.push('Password must contain at least one uppercase letter');
        }
        if (!checks.hasLowercase) {
          errors.push('Password must contain at least one lowercase letter');
        }
        if (!checks.hasNumber) {
          errors.push('Password must contain at least one number');
        }
        if (!checks.hasSpecialChar) {
          errors.push('Password must contain at least one special character');
        }
      }
      return errors;
    }
  }
};

// Doctor validation schema
export const doctorValidationSchema = {
  name: {
    required: true,
    minLength: 2,
    maxLength: 100,
    validate: (value) => {
      const errors = [];
      if (!validators.required(value)) {
        errors.push('Doctor name is required');
      } else {
        const trimmed = value.trim();
        if (!validators.minLength(trimmed, 2)) {
          errors.push('Doctor name must be at least 2 characters long');
        }
        if (!validators.maxLength(trimmed, 100)) {
          errors.push('Doctor name must be less than 100 characters');
        }
      }
      return errors;
    }
  },
  
  specialty: {
    required: true,
    minLength: 2,
    validate: (value) => {
      const errors = [];
      if (!validators.required(value)) {
        errors.push('Specialty is required');
      } else if (!validators.minLength(value.trim(), 2)) {
        errors.push('Specialty must be at least 2 characters long');
      }
      return errors;
    }
  },
  
  phone: {
    required: true,
    validate: (value) => {
      const errors = [];
      if (!validators.required(value)) {
        errors.push('Phone number is required');
      } else if (!validators.phone(value.trim())) {
        errors.push('Please enter a valid phone number (at least 10 digits)');
      }
      return errors;
    }
  },
  
  email: {
    required: false,
    validate: (value) => {
      const errors = [];
      if (value && value.trim() && !validators.email(value.trim())) {
        errors.push('Please enter a valid email address');
      }
      return errors;
    }
  },
  
  availableDays: {
    required: true,
    validate: (value) => {
      const errors = [];
      if (!value || !Array.isArray(value) || value.length === 0) {
        errors.push('At least one available day must be selected');
      }
      return errors;
    }
  }
};

// Generic validation function
export const validateEntity = (data, schema) => {
  const errors = [];
  
  for (const [field, rules] of Object.entries(schema)) {
    const value = data[field];
    const fieldErrors = rules.validate(value);
    errors.push(...fieldErrors);
  }
  
  return errors;
};

// Validation for API responses
export const apiResponseSchema = {
  success: {
    required: true,
    validate: (value) => {
      if (typeof value !== 'boolean') {
        return ['Response must include a boolean success field'];
      }
      return [];
    }
  },
  
  message: {
    required: false,
    validate: (value) => {
      if (value !== undefined && typeof value !== 'string') {
        return ['Message must be a string'];
      }
      return [];
    }
  }
};

// Sanitization utilities
export const sanitizers = {
  trim: (value) => value && typeof value === 'string' ? value.trim() : value,
  
  email: (value) => value && typeof value === 'string' ? value.trim().toLowerCase() : value,
  
  phone: (value) => {
    if (!value || typeof value !== 'string') return value;
    return value.replace(/[\s\-\(\)]/g, '');
  },
  
  name: (value) => {
    if (!value || typeof value !== 'string') return value;
    return value.trim().replace(/\s+/g, ' ');
  }
};

// Sanitize entity data
export const sanitizeEntity = (data, entityType) => {
  const sanitized = { ...data };
  
  if (entityType === 'staff' || entityType === 'doctor') {
    if (sanitized.name) sanitized.name = sanitizers.name(sanitized.name);
    if (sanitized.email) sanitized.email = sanitizers.email(sanitized.email);
    if (sanitized.phone) sanitized.phone = sanitizers.phone(sanitized.phone);
    if (sanitized.specialty) sanitized.specialty = sanitizers.trim(sanitized.specialty);
  }
  
  return sanitized;
};
