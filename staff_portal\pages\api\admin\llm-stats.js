import { withRole } from '../../../lib/auth';
import { withRateLimit } from '../../../lib/rate_limiter';
import { logger } from '../../../lib/logger';
import Redis from 'ioredis';

// Initialize Redis client
const redisClient = new Redis(process.env.REDIS_URL || 'redis://localhost:6379');

/**
 * API endpoint for retrieving LLM usage statistics from Redis
 * Provides data for visualization and cost management
 */
async function handler(req, res) {
  try {
    // Only allow GET requests
    if (req.method !== 'GET') {
      return res.status(405).json({
        success: false,
        message: 'Method not allowed'
      });
    }

    // Get date range parameters from query
    const { period = 'day', hospitalId } = req.query;

    // Validate hospital_id access
    if (hospitalId && hospitalId !== req.user.hospital_id && req.user.role !== 'super_admin') {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to access data for this hospital'
      });
    }

    // Determine the hospital ID to use
    const targetHospitalId = hospitalId || req.user.hospital_id;
    
    // Determine Redis key pattern based on period
    let keyPattern;
    
    switch (period) {
      case 'day':
        keyPattern = `llm:stats:${targetHospitalId}:${getDateString(0)}:*`;
        break;
      case 'week':
        // Get stats for the past 7 days
        keyPattern = `llm:stats:${targetHospitalId}:${getDateRangePattern(7)}:*`;
        break;
      case 'month':
        // Get stats for the past 30 days
        keyPattern = `llm:stats:${targetHospitalId}:${getDateRangePattern(30)}:*`;
        break;
      default:
        keyPattern = `llm:stats:${targetHospitalId}:${getDateString(0)}:*`;
    }
    
    // Fetch keys matching the pattern using production-safe SCAN
    const keys = [];
    const stream = redisClient.scanStream({
      match: keyPattern,
      count: 100
    });

    await new Promise((resolve, reject) => {
      stream.on('data', (resultKeys) => {
        keys.push(...resultKeys);
      });
      stream.on('end', resolve);
      stream.on('error', reject);
    });
    
    if (!keys || keys.length === 0) {
      return res.status(200).json({
        success: true,
        message: 'No LLM usage data found for the specified period',
        data: {
          totalCalls: 0,
          cachedCalls: 0,
          totalTokens: 0,
          estimatedCost: 0,
          cacheHitRate: 0,
          modelUsage: {},
          dailyUsage: [],
          costBreakdown: {}
        }
      });
    }
    
    // Fetch all values
    const pipeline = redisClient.pipeline();
    keys.forEach(key => {
      pipeline.get(key);
    });
    
    const results = await pipeline.exec();
    
    // Process the results
    const stats = {
      totalCalls: 0,
      cachedCalls: 0,
      totalTokens: 0,
      estimatedCost: 0,
      modelUsage: {},
      dailyUsage: [],
      costBreakdown: {}
    };
    
    // Parse and aggregate stats
    results.forEach(([err, result], index) => {
      if (err) {
        logger.error(`Error fetching LLM stats: ${err}`);
        return;
      }
      
      try {
        const data = JSON.parse(result);
        const key = keys[index];
        const keyParts = key.split(':');
        const date = keyParts[3] || 'unknown';
        const model = keyParts[4] || 'unknown';
        
        // Add to total stats
        stats.totalCalls += data.calls || 0;
        stats.cachedCalls += data.cached_calls || 0;
        stats.totalTokens += (data.input_tokens || 0) + (data.output_tokens || 0);
        stats.estimatedCost += data.cost || 0;
        
        // Track model usage
        if (!stats.modelUsage[model]) {
          stats.modelUsage[model] = 0;
        }
        stats.modelUsage[model] += data.calls || 0;
        
        // Track daily usage
        const dailyEntry = stats.dailyUsage.find(entry => entry.date === date);
        if (dailyEntry) {
          dailyEntry.calls += data.calls || 0;
          dailyEntry.cost += data.cost || 0;
        } else {
          stats.dailyUsage.push({
            date,
            calls: data.calls || 0,
            cost: data.cost || 0
          });
        }
        
        // Track cost breakdown
        if (!stats.costBreakdown[model]) {
          stats.costBreakdown[model] = 0;
        }
        stats.costBreakdown[model] += data.cost || 0;
      } catch (e) {
        logger.error(`Error parsing LLM stats: ${e}`);
      }
    });
    
    // Calculate cache hit rate
    stats.cacheHitRate = stats.totalCalls > 0 
      ? (stats.cachedCalls / stats.totalCalls) * 100 
      : 0;
    
    // Sort daily usage by date
    stats.dailyUsage.sort((a, b) => new Date(a.date) - new Date(b.date));
    
    // Return the aggregated stats
    return res.status(200).json({
      success: true,
      data: stats
    });
    
  } catch (error) {
    logger.error('LLM stats API error:', error);
    
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
}

/**
 * Get date string in YYYY-MM-DD format
 * @param {number} daysAgo - Number of days ago
 * @returns {string} Date string
 */
function getDateString(daysAgo) {
  const date = new Date();
  date.setDate(date.getDate() - daysAgo);
  return date.toISOString().split('T')[0];
}

/**
 * Get Redis key pattern for a date range
 * @param {number} days - Number of days in the range
 * @returns {string} Redis key pattern
 */
function getDateRangePattern(days) {
  // This is a simplified approach - Redis doesn't directly support date ranges in key patterns
  // For better performance in production, we would use a different data structure
  const dates = [];
  for (let i = 0; i < days; i++) {
    dates.push(getDateString(i));
  }
  
  if (dates.length === 1) {
    return dates[0];
  }
  
  // Return a pattern that would match any of these dates
  // This is inefficient for large ranges but works for our purpose
  return `{${dates.join(',')}}`;
}

// Apply rate limiting
const rateLimitedHandler = withRateLimit(handler, { tier: 'admin' });

// Only allow access to users with admin role
export default withRole(rateLimitedHandler, ['admin', 'super_admin']);