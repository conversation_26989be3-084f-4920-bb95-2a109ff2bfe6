"""
Multilingual Semantic Cache for WhatsApp Agent

Provides semantic caching functionality for English and regional languages
written in English script for the WhatsApp agent system.
"""

import json
import logging
import time
from typing import Dict, Any, List, Optional, Tuple
import numpy as np
from ..base_operations import RedisOperations
from ..config import get_redis_config
from .embedding_manager import get_embedding_manager

logger = logging.getLogger(__name__)


class MultilingualCache:
    """
    Semantic cache using multilingual embeddings for WhatsApp agent.
    Supports English and regional languages written in English script.
    """
    
    def __init__(self, redis_ops: Optional[RedisOperations] = None):
        """
        Initialize multilingual semantic cache.
        
        Args:
            redis_ops: Optional Redis operations instance
        """
        self.redis_ops = redis_ops or RedisOperations()
        self.config = get_redis_config()
        self.embedding_manager = get_embedding_manager()
        
        # Cache configuration
        self.similarity_threshold = self.config.semantic_similarity_threshold
        self.cache_ttl = self.config.semantic_cache_ttl
        
        # Statistics
        self._stats = {
            "cache_requests": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "embeddings_stored": 0,
            "similarity_searches": 0,
            "total_time": 0.0
        }
    
    def _get_cache_key(self, hospital_id: str, category: str, query_hash: str) -> str:
        """Generate cache key for semantic entry."""
        return f"semantic:multi:{hospital_id}:{category}:{query_hash}"
    
    def _get_embedding_key(self, hospital_id: str, category: str, query_hash: str) -> str:
        """Generate key for storing embeddings."""
        return f"embedding:multi:{hospital_id}:{category}:{query_hash}"
    
    def _get_index_key(self, hospital_id: str, category: str) -> str:
        """Generate key for semantic index."""
        return f"index:multi:{hospital_id}:{category}"
    
    def _serialize_embedding(self, embedding: np.ndarray) -> str:
        """Serialize embedding for Redis storage."""
        return json.dumps(embedding.tolist())
    
    def _deserialize_embedding(self, data: str) -> np.ndarray:
        """Deserialize embedding from Redis."""
        return np.array(json.loads(data))
    
    def _detect_language_script(self, text: str) -> str:
        """
        Detect if text is in English or regional language (in English script).
        
        Args:
            text: Input text
            
        Returns:
            Language script type ('english', 'regional_english', 'mixed')
        """
        # Simple heuristic: check for common English words vs transliterated patterns
        english_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        words = text.lower().split()
        
        english_count = sum(1 for word in words if word in english_words)
        total_words = len(words)
        
        if total_words == 0:
            return 'english'
        
        english_ratio = english_count / total_words
        
        if english_ratio > 0.3:
            return 'english'
        elif english_ratio > 0.1:
            return 'mixed'
        else:
            return 'regional_english'
    
    async def cache_response_async(self, query: str, response: str, hospital_id: str, 
                                 category: str = "general", language_hint: Optional[str] = None,
                                 metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Cache response with multilingual embedding for semantic matching.
        
        Args:
            query: User query text
            response: Response to cache
            hospital_id: Hospital identifier
            category: Response category
            language_hint: Optional language hint ('en', 'hi', 'ta', etc.)
            metadata: Optional metadata
            
        Returns:
            bool: True if cached successfully, False otherwise
        """
        start_time = time.time()
        
        try:
            # Generate embedding
            embedding = self.embedding_manager.generate_multilingual_embedding(query)
            if embedding is None:
                logger.warning(f"Failed to generate embedding for query: {query}")
                return False
            
            # Detect language script
            script_type = self._detect_language_script(query)
            
            # Create cache entry
            query_hash = str(hash(query))
            cache_key = self._get_cache_key(hospital_id, category, query_hash)
            embedding_key = self._get_embedding_key(hospital_id, category, query_hash)
            index_key = self._get_index_key(hospital_id, category)
            
            cache_data = {
                "query": query,
                "response": response,
                "hospital_id": hospital_id,
                "category": category,
                "language_hint": language_hint,
                "script_type": script_type,
                "metadata": metadata or {},
                "timestamp": time.time(),
                "query_hash": query_hash
            }
            
            # Store cache entry, embedding, and update index
            success1 = await self.redis_ops.set_async(cache_key, cache_data, ttl=self.cache_ttl)
            success2 = await self.redis_ops.set_async(embedding_key, self._serialize_embedding(embedding), ttl=self.cache_ttl)
            
            # Add to index for similarity search
            index_data = await self.redis_ops.get_async(index_key, as_json=True) or []
            index_data.append({
                "query_hash": query_hash,
                "query": query[:100],  # Store truncated query for debugging
                "script_type": script_type,
                "language_hint": language_hint,
                "timestamp": time.time()
            })
            
            # Limit index size
            if len(index_data) > 1000:
                index_data = index_data[-800:]  # Keep most recent 800 entries
            
            success3 = await self.redis_ops.set_async(index_key, index_data, ttl=self.cache_ttl)
            
            if success1 and success2 and success3:
                self._stats["embeddings_stored"] += 1
                logger.info(f"Cached multilingual response for hospital {hospital_id}, category {category}, script: {script_type}")
                return True
            else:
                logger.error("Failed to store some cache components")
                return False
            
        except Exception as e:
            logger.error(f"Error caching multilingual response: {e}")
            return False
        finally:
            self._stats["total_time"] += time.time() - start_time
    
    async def search_similar_async(self, query: str, hospital_id: str, 
                                 category: str = "general", language_hint: Optional[str] = None,
                                 limit: int = 5) -> List[Dict[str, Any]]:
        """
        Search for semantically similar cached responses.
        
        Args:
            query: User query text
            hospital_id: Hospital identifier
            category: Response category
            language_hint: Optional language hint for better matching
            limit: Maximum number of results
            
        Returns:
            List of similar responses with similarity scores
        """
        start_time = time.time()
        self._stats["cache_requests"] += 1
        self._stats["similarity_searches"] += 1
        
        try:
            # Generate query embedding
            query_embedding = self.embedding_manager.generate_multilingual_embedding(query)
            if query_embedding is None:
                self._stats["cache_misses"] += 1
                return []
            
            # Detect query script type
            query_script = self._detect_language_script(query)
            
            # Get index of cached entries
            index_key = self._get_index_key(hospital_id, category)
            index_data = await self.redis_ops.get_async(index_key, as_json=True)
            
            if not index_data:
                self._stats["cache_misses"] += 1
                return []
            
            # Filter candidates by language/script preference
            filtered_entries = []
            for entry in index_data:
                # Prefer same script type or language hint
                if language_hint and entry.get("language_hint") == language_hint:
                    filtered_entries.append(entry)
                elif entry.get("script_type") == query_script:
                    filtered_entries.append(entry)
                else:
                    # Include all entries but with lower priority
                    filtered_entries.append(entry)
            
            # Load embeddings and calculate similarities
            candidates = []
            for entry in filtered_entries:
                query_hash = entry["query_hash"]
                embedding_key = self._get_embedding_key(hospital_id, category, query_hash)
                
                embedding_data = await self.redis_ops.get_async(embedding_key, as_json=False)
                if embedding_data:
                    try:
                        embedding = self._deserialize_embedding(embedding_data)
                        candidates.append((query_hash, embedding))
                    except Exception as e:
                        logger.warning(f"Failed to deserialize embedding for {query_hash}: {e}")
            
            if not candidates:
                self._stats["cache_misses"] += 1
                return []
            
            # Find most similar
            similar_entries = self.embedding_manager.find_most_similar(
                query_embedding, candidates, self.similarity_threshold
            )
            
            if not similar_entries:
                self._stats["cache_misses"] += 1
                return []
            
            # Retrieve cached responses
            results = []
            for query_hash, similarity in similar_entries[:limit]:
                cache_key = self._get_cache_key(hospital_id, category, query_hash)
                cache_data = await self.redis_ops.get_async(cache_key, as_json=True)
                
                if cache_data:
                    cache_data["similarity"] = similarity
                    results.append(cache_data)
            
            if results:
                self._stats["cache_hits"] += 1
                logger.info(f"Found {len(results)} similar multilingual responses for query in hospital {hospital_id}")
            else:
                self._stats["cache_misses"] += 1
            
            return results
            
        except Exception as e:
            logger.error(f"Error searching similar multilingual responses: {e}")
            self._stats["cache_misses"] += 1
            return []
        finally:
            self._stats["total_time"] += time.time() - start_time
    
    async def get_best_match_async(self, query: str, hospital_id: str, 
                                 category: str = "general", language_hint: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Get the best matching cached response.
        
        Args:
            query: User query text
            hospital_id: Hospital identifier
            category: Response category
            language_hint: Optional language hint
            
        Returns:
            Best matching response or None if no good match found
        """
        similar_responses = await self.search_similar_async(query, hospital_id, category, language_hint, limit=1)
        
        if similar_responses and similar_responses[0]["similarity"] >= self.similarity_threshold:
            return similar_responses[0]
        
        return None
    
    async def cache_doctor_availability_async(self, hospital_id: str, doctor_id: str, 
                                            status: str, estimated_time: Optional[str] = None) -> bool:
        """
        Cache doctor availability information for WhatsApp queries.
        
        Args:
            hospital_id: Hospital identifier
            doctor_id: Doctor identifier
            status: Availability status ('arrived', 'delayed', 'unavailable')
            estimated_time: Optional estimated arrival time
            
        Returns:
            bool: True if cached successfully
        """
        try:
            # Create availability queries in multiple languages/scripts
            queries = [
                f"when will doctor {doctor_id} arrive",
                f"doctor {doctor_id} availability",
                f"is doctor {doctor_id} available",
                f"doctor {doctor_id} ka time kya hai",  # Hindi in English script
                f"doctor {doctor_id} kab aayenge",     # Hindi in English script
            ]
            
            # Create response based on status
            if status == "arrived":
                response = f"Dr. {doctor_id} has arrived at the hospital."
            elif status == "delayed" and estimated_time:
                response = f"Dr. {doctor_id} is delayed and will arrive in {estimated_time}."
            elif estimated_time:
                response = f"Dr. {doctor_id} will arrive in {estimated_time}."
            else:
                response = f"Dr. {doctor_id} is currently unavailable."
            
            # Cache all query variations
            success_count = 0
            for query in queries:
                success = await self.cache_response_async(
                    query, response, hospital_id, "doctor_availability",
                    metadata={"doctor_id": doctor_id, "status": status, "estimated_time": estimated_time}
                )
                if success:
                    success_count += 1
            
            return success_count > 0
            
        except Exception as e:
            logger.error(f"Error caching doctor availability: {e}")
            return False
    
    async def clear_hospital_cache_async(self, hospital_id: str, category: Optional[str] = None) -> int:
        """
        Clear cached data for a hospital.
        
        Args:
            hospital_id: Hospital identifier
            category: Optional category filter
            
        Returns:
            Number of entries cleared
        """
        try:
            if category:
                patterns = [
                    f"semantic:multi:{hospital_id}:{category}:*",
                    f"embedding:multi:{hospital_id}:{category}:*",
                    f"index:multi:{hospital_id}:{category}"
                ]
            else:
                patterns = [
                    f"semantic:multi:{hospital_id}:*",
                    f"embedding:multi:{hospital_id}:*",
                    f"index:multi:{hospital_id}:*"
                ]
            
            total_deleted = 0
            for pattern in patterns:
                keys = await self.redis_ops.keys_async(pattern)
                if keys:
                    deleted = await self.redis_ops.delete_async(*keys)
                    total_deleted += deleted
            
            logger.info(f"Cleared {total_deleted} multilingual cache entries for hospital {hospital_id}")
            return total_deleted
            
        except Exception as e:
            logger.error(f"Error clearing hospital cache: {e}")
            return 0
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics.
        
        Returns:
            Dict with cache statistics
        """
        stats = self._stats.copy()
        
        if stats["cache_requests"] > 0:
            stats["hit_rate"] = stats["cache_hits"] / stats["cache_requests"]
            stats["average_time_ms"] = (stats["total_time"] / stats["cache_requests"]) * 1000
        else:
            stats["hit_rate"] = 0
            stats["average_time_ms"] = 0
        
        stats["embedding_stats"] = self.embedding_manager.get_stats()
        stats["similarity_threshold"] = self.similarity_threshold
        stats["cache_ttl"] = self.cache_ttl
        
        return stats
    
    def reset_stats(self):
        """Reset cache statistics."""
        self._stats = {
            "cache_requests": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "embeddings_stored": 0,
            "similarity_searches": 0,
            "total_time": 0.0
        }
