/**
 * Example Integration for WhatsApp Agent with Shared Redis Implementation
 * 
 * This file shows how to integrate the whatsapp_agent with the new shared Redis implementation.
 * It provides examples of how to replace existing Redis usage with the enhanced shared implementation.
 */

import { getNodeJSAdapter } from './adapters/nodejs_adapter.js';
import { logger } from '../../whatsapp_agent/lib/logger.js';

// Example 1: Simple replacement of existing Redis usage
// OLD CODE (in whatsapp_agent/lib/redis.js):
// import { getRedisClient } from './redis.js';
// const client = getRedisClient();
// await client.set('key', 'value');

// NEW CODE:
const redisAdapter = getNodeJSAdapter();
await redisAdapter.initialize();
await redisAdapter.set('key', 'value');

// Example 2: Enhanced doctor availability caching
async function exampleDoctorAvailabilityCaching() {
  logger.info('=== Doctor Availability Caching Example ===');
  
  const hospitalId = 'hospital_123';
  const doctorId = 'dr_sharma';
  
  // Cache doctor availability with multiple language variations
  await redisAdapter.cacheDoctorAvailability(
    hospitalId, 
    doctorId, 
    'delayed', 
    '15 minutes'
  );
  
  // Get availability status
  const availability = await redisAdapter.getDoctorAvailability(hospitalId, doctorId);
  logger.info('Doctor availability:', availability);
  
  // Search for similar queries in multiple languages
  const queries = [
    'when will dr sharma come',
    'sharma doctor ka time',
    'doctor sharma kab aayenge'
  ];
  
  for (const query of queries) {
    const results = await redisAdapter.searchSimilarResponses(
      query, hospitalId, 'doctor_availability', 3
    );
    logger.info(`Query: "${query}" - Found ${results.length} matches`);
    
    if (results.length > 0) {
      logger.info(`Best match: ${results[0].response} (similarity: ${results[0].similarity})`);
    }
  }
}

// Example 3: Multilingual message caching
async function exampleMultilingualCaching() {
  logger.info('=== Multilingual Message Caching Example ===');
  
  const hospitalId = 'hospital_123';
  
  // Cache responses in different languages/scripts
  const messageVariations = [
    {
      query: 'what are visiting hours',
      response: 'Visiting hours are 10 AM to 8 PM daily.',
      language: 'en'
    },
    {
      query: 'visiting hours kya hai',
      response: 'Visiting hours 10 AM se 8 PM tak hai daily.',
      language: 'hi'
    },
    {
      query: 'hospital ka time kya hai',
      response: 'Hospital ka visiting time 10 baje se 8 baje tak hai.',
      language: 'hi'
    }
  ];
  
  // Cache all variations
  for (const msg of messageVariations) {
    const cacheKey = `semantic:multi:${hospitalId}:visiting_hours:${redisAdapter._hashString(msg.query)}`;
    const cacheData = {
      query: msg.query,
      response: msg.response,
      hospital_id: hospitalId,
      category: 'visiting_hours',
      language_hint: msg.language,
      timestamp: Date.now()
    };
    
    await redisAdapter.set(cacheKey, cacheData, redisAdapter.config.semantic_cache_ttl);
    logger.info(`Cached: ${msg.query} -> ${msg.response}`);
  }
  
  // Test semantic search
  const testQueries = [
    'visiting time kya hai',
    'when can I visit',
    'hospital timing'
  ];
  
  for (const testQuery of testQueries) {
    const matches = await redisAdapter.searchSimilarResponses(
      testQuery, hospitalId, 'visiting_hours', 2
    );
    
    logger.info(`\nQuery: "${testQuery}"`);
    matches.forEach((match, index) => {
      logger.info(`  ${index + 1}. ${match.response} (similarity: ${match.similarity.toFixed(2)})`);
    });
  }
}

// Example 4: Integration with existing WhatsApp message handling
class WhatsAppRedisIntegration {
  constructor() {
    this.redisAdapter = getNodeJSAdapter();
    this.initialized = false;
  }
  
  async initialize() {
    if (!this.initialized) {
      await this.redisAdapter.initialize();
      this.initialized = true;
      logger.info('WhatsApp Redis integration initialized');
    }
  }
  
  /**
   * Process incoming WhatsApp message with semantic caching
   */
  async processMessage(message, hospitalContext) {
    await this.initialize();
    
    const { text, from } = message;
    const { hospitalId } = hospitalContext;
    
    try {
      // First, try to find a semantic match
      const semanticMatches = await this.redisAdapter.searchSimilarResponses(
        text, hospitalId, 'general', 1
      );
      
      if (semanticMatches.length > 0 && semanticMatches[0].similarity > 0.7) {
        logger.info(`Found semantic match for: "${text}"`);
        return {
          response: semanticMatches[0].response,
          source: 'semantic_cache',
          similarity: semanticMatches[0].similarity
        };
      }
      
      // If no semantic match, check for doctor availability queries
      if (this._isDoctorAvailabilityQuery(text)) {
        return await this._handleDoctorAvailabilityQuery(text, hospitalId);
      }
      
      // If no cached response, process with LLM and cache result
      const llmResponse = await this._processWithLLM(text, hospitalContext);
      
      // Cache the response for future semantic matching
      await this._cacheResponse(text, llmResponse, hospitalId);
      
      return {
        response: llmResponse,
        source: 'llm_processed',
        cached: true
      };
      
    } catch (error) {
      logger.error('Error processing message:', error);
      return {
        response: 'Sorry, I encountered an error. Please try again.',
        source: 'error',
        error: error.message
      };
    }
  }
  
  /**
   * Handle doctor availability queries
   */
  async _handleDoctorAvailabilityQuery(text, hospitalId) {
    // Extract doctor name from query (simplified)
    const doctorMatch = text.match(/doctor?\s+(\w+)/i);
    const doctorId = doctorMatch ? doctorMatch[1].toLowerCase() : null;
    
    if (doctorId) {
      const availability = await this.redisAdapter.getDoctorAvailability(hospitalId, doctorId);
      
      if (availability) {
        let response;
        if (availability.status === 'arrived') {
          response = `Dr. ${doctorId} has arrived at the hospital.`;
        } else if (availability.status === 'delayed' && availability.estimated_time) {
          response = `Dr. ${doctorId} is delayed and will arrive in ${availability.estimated_time}.`;
        } else {
          response = `Dr. ${doctorId} is currently unavailable.`;
        }
        
        return {
          response,
          source: 'availability_cache',
          doctor_id: doctorId,
          status: availability.status
        };
      }
    }
    
    return null;
  }
  
  /**
   * Check if message is asking about doctor availability
   */
  _isDoctorAvailabilityQuery(text) {
    const availabilityKeywords = [
      'when', 'time', 'arrive', 'available', 'kab', 'samay', 'aayenge'
    ];
    const doctorKeywords = ['doctor', 'dr', 'doc'];
    
    const textLower = text.toLowerCase();
    const hasAvailabilityKeyword = availabilityKeywords.some(keyword => 
      textLower.includes(keyword)
    );
    const hasDoctorKeyword = doctorKeywords.some(keyword => 
      textLower.includes(keyword)
    );
    
    return hasAvailabilityKeyword && hasDoctorKeyword;
  }
  
  /**
   * Process message with LLM (placeholder)
   */
  async _processWithLLM(text, hospitalContext) {
    // This would integrate with your existing LLM processing
    // For now, return a placeholder response
    return `Thank you for your message: "${text}". Our team will assist you shortly.`;
  }
  
  /**
   * Cache response for future semantic matching
   */
  async _cacheResponse(query, response, hospitalId, category = 'general') {
    const cacheKey = `semantic:multi:${hospitalId}:${category}:${this.redisAdapter._hashString(query)}`;
    const cacheData = {
      query,
      response,
      hospital_id: hospitalId,
      category,
      timestamp: Date.now()
    };
    
    await this.redisAdapter.set(cacheKey, cacheData, this.redisAdapter.config.semantic_cache_ttl);
    logger.info(`Cached response for query: "${query}"`);
  }
  
  /**
   * Update doctor availability status
   */
  async updateDoctorAvailability(hospitalId, doctorId, status, estimatedTime = null) {
    await this.initialize();
    
    const success = await this.redisAdapter.cacheDoctorAvailability(
      hospitalId, doctorId, status, estimatedTime
    );
    
    if (success) {
      logger.info(`Updated availability for Dr. ${doctorId}: ${status}`);
    }
    
    return success;
  }
  
  /**
   * Get cache statistics
   */
  async getCacheStats() {
    await this.initialize();
    return this.redisAdapter.getStats();
  }
  
  /**
   * Clear cache for hospital
   */
  async clearHospitalCache(hospitalId, category = null) {
    await this.initialize();
    
    const pattern = category 
      ? `semantic:multi:${hospitalId}:${category}:*`
      : `semantic:multi:${hospitalId}:*`;
    
    return await this.redisAdapter.clearCache(pattern);
  }
}

// Example 5: Staff portal integration for updating doctor status
async function exampleStaffPortalIntegration() {
  logger.info('=== Staff Portal Integration Example ===');
  
  const integration = new WhatsAppRedisIntegration();
  await integration.initialize();
  
  const hospitalId = 'hospital_123';
  
  // Simulate staff updating doctor availability
  const doctorUpdates = [
    { doctorId: 'dr_sharma', status: 'arrived' },
    { doctorId: 'dr_patel', status: 'delayed', estimatedTime: '30 minutes' },
    { doctorId: 'dr_singh', status: 'unavailable' }
  ];
  
  for (const update of doctorUpdates) {
    await integration.updateDoctorAvailability(
      hospitalId, 
      update.doctorId, 
      update.status, 
      update.estimatedTime
    );
  }
  
  // Test queries after updates
  const testMessages = [
    { text: 'when will dr sharma come', from: '+91XXXXXXXXXX' },
    { text: 'dr patel ka time kya hai', from: '+91XXXXXXXXXX' },
    { text: 'is dr singh available', from: '+91XXXXXXXXXX' }
  ];
  
  for (const message of testMessages) {
    const result = await integration.processMessage(message, { hospitalId });
    logger.info(`Query: "${message.text}"`);
    logger.info(`Response: ${result.response} (source: ${result.source})`);
    logger.info('---');
  }
}

// Example 6: Performance monitoring
async function examplePerformanceMonitoring() {
  logger.info('=== Performance Monitoring Example ===');
  
  const integration = new WhatsAppRedisIntegration();
  await integration.initialize();
  
  // Get current statistics
  const stats = await integration.getCacheStats();
  logger.info('Cache Statistics:', {
    operations: stats.operations,
    hit_rate: stats.hit_rate,
    average_time: stats.average_time_ms,
    error_rate: stats.error_rate,
    is_connected: stats.is_connected
  });
  
  // Test connection health
  const isHealthy = await redisAdapter.testConnection();
  logger.info(`Redis connection healthy: ${isHealthy}`);
}

// Main example function
async function runExamples() {
  try {
    logger.info('Starting WhatsApp Agent Redis Integration Examples...');
    
    // Initialize the adapter
    await redisAdapter.initialize();
    
    // Run examples
    await exampleDoctorAvailabilityCaching();
    await exampleMultilingualCaching();
    await exampleStaffPortalIntegration();
    await examplePerformanceMonitoring();
    
    logger.info('All examples completed successfully!');
    
  } catch (error) {
    logger.error('Error running examples:', error);
  } finally {
    // Clean up
    await redisAdapter.close();
  }
}

// Export for use in other modules
export {
  WhatsAppRedisIntegration,
  exampleDoctorAvailabilityCaching,
  exampleMultilingualCaching,
  exampleStaffPortalIntegration,
  examplePerformanceMonitoring
};

// Run examples if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runExamples();
}
