{"version": "2.0", "description": "Enhanced query patterns for shared Redis voice agent", "last_updated": "2024-01-15", "doctor_patterns": {"name_patterns": ["(?i)dr\\.?\\s*{name}", "(?i)doctor\\s+{name}", "(?i){name}\\s+doctor", "(?i)डॉक्टर\\s*{name}", "(?i)डॉ\\.?\\s*{name}", "(?i)ডাক্তার\\s*{name}", "(?i)ডা\\.?\\s*{name}"], "specialty_patterns": ["(?i){specialty}\\s+doctor", "(?i){specialty}\\s+specialist", "(?i)doctor\\s+{specialty}", "(?i){specialty}\\s+डॉक्टर", "(?i){specialty}\\s+विशेषज्ञ", "(?i){specialty}\\s+ডাক্তার", "(?i){specialty}\\s+বিশেষজ্ঞ"], "availability_patterns": ["(?i)when\\s+(is|does)\\s+.*available", "(?i)what\\s+time\\s+.*come", "(?i).*timings?", "(?i).*schedule", "(?i)कब\\s+.*आते\\s+हैं", "(?i).*का\\s+समय", "(?i)कখন\\s+.*আসেন", "(?i).*এর\\s+সময়"]}, "test_patterns": {"price_patterns": ["(?i)what\\s+(is\\s+)?the\\s+price\\s+of\\s+{test}", "(?i)how\\s+much\\s+(does\\s+)?{test}\\s+cost", "(?i){test}\\s+price", "(?i){test}\\s+cost", "(?i){test}\\s+की\\s+कीमत", "(?i){test}\\s+का\\s+खर्च", "(?i){test}\\s+এর\\s+দাম", "(?i){test}\\s+এর\\s+খরচ"], "general_test_patterns": ["(?i)blood\\s+test\\s+price", "(?i)test\\s+costs?", "(?i)examination\\s+fees?", "(?i)lab\\s+test\\s+charges?", "(?i)खून\\s+की\\s+जांच\\s+की\\s+कीमत", "(?i)टेस्ट\\s+की\\s+लागत", "(?i)রক্ত\\s+পরীক্ষার\\s+দাম", "(?i)টেস্টের\\s+খরচ"]}, "common_patterns": {"greeting": {"english": ["(?i)hello", "(?i)hi", "(?i)good\\s+(morning|afternoon|evening)", "(?i)hey\\s+there"], "hindi": ["(?i)नमस्ते", "(?i)नमस्कार", "(?i)सुप्रभात", "(?i)शुभ\\s+(दोपहर|संध्या)"], "bengali": ["(?i)নমস্কার", "(?i)আসসালামু\\s+আলাইকুম", "(?i)সুপ্রভাত", "(?i)শুভ\\s+(দুপুর|সন্ধ্যা)"]}, "help": {"english": ["(?i)help\\s+me", "(?i)i\\s+need\\s+help", "(?i)can\\s+you\\s+help", "(?i)what\\s+can\\s+you\\s+do", "(?i)assist\\s+me"], "hindi": ["(?i)मेरी\\s+मदद\\s+करें", "(?i)मुझे\\s+सहायता\\s+चाहिए", "(?i)क्या\\s+आप\\s+मदद\\s+कर\\s+सकते\\s+हैं", "(?i)आप\\s+क्या\\s+कर\\s+सकते\\s+हैं"], "bengali": ["(?i)আ<PERSON>াক<PERSON>\\s+সাহায্য\\s+করুন", "(?i)আম<PERSON>র\\s+সাহায্য\\s+দরকার", "(?i)আপনি\\s+কি\\s+সাহায্য\\s+করতে\\s+পারেন", "(?i)আপনি\\s+কী\\s+করতে\\s+পারেন"]}, "appointment": {"english": ["(?i)book\\s+appointment", "(?i)schedule\\s+appointment", "(?i)make\\s+appointment", "(?i)appointment\\s+booking", "(?i)fix\\s+appointment"], "hindi": ["(?i)अपॉइंटमेंट\\s+बुक\\s+करें", "(?i)अपॉइंटमेंट\\s+का\\s+समय\\s+दें", "(?i)मुलाकात\\s+का\\s+समय", "(?i)समय\\s+निर्धारित\\s+करें"], "bengali": ["(?i)অ্যাপয়েন্টমেন্ট\\s+বুক\\s+করুন", "(?i)অ্যাপয়েন্টমেন্টের\\s+সময়\\s+দিন", "(?i)সাক্ষাতের\\s+সময়", "(?i)সময়\\s+নির্ধারণ\\s+করুন"]}, "hospital_info": {"english": ["(?i)hospital\\s+timings?", "(?i)visiting\\s+hours?", "(?i)hospital\\s+address", "(?i)contact\\s+number", "(?i)emergency\\s+contact"], "hindi": ["(?i)अस्पताल\\s+का\\s+समय", "(?i)मिलने\\s+का\\s+समय", "(?i)अस्पताल\\s+का\\s+पता", "(?i)संपर्क\\s+नंबर", "(?i)आपातकालीन\\s+संपर्क"], "bengali": ["(?i)হাসপাতালের\\s+সময়", "(?i)দেখার\\s+সময়", "(?i)হাসপাতালের\\s+ঠিকানা", "(?i)যোগাযোগ\\s+নম্বর", "(?i)জরুরি\\s+যোগাযোগ"]}}, "response_patterns": {"doctor_response": {"template": "Dr. {doctor_name} ({specialty}) is available {schedule}. Consultation fee: {fee}.", "fields": ["doctor_name", "specialty", "schedule", "fee"]}, "test_response": {"template": "{test_name} costs {price}. Duration: {duration}. Preparation: {preparation}.", "fields": ["test_name", "price", "duration", "preparation"]}, "appointment_response": {"template": "To book an appointment with Dr. {doctor_name}, please call {contact} or visit the {department} department.", "fields": ["doctor_name", "contact", "department"]}, "hospital_info_response": {"template": "Hospital timings: {working_hours}. Contact: {phone}. Address: {address}.", "fields": ["working_hours", "phone", "address"]}}, "language_detection": {"hindi_indicators": ["(?i)[देवनागरी]", "(?i)डॉक्टर", "(?i)अस्पताल", "(?i)जांच", "(?i)कीमत", "(?i)समय"], "bengali_indicators": ["(?i)[বাংলা]", "(?i)ডাক্তার", "(?i)হাসপাতাল", "(?i)পরীক্ষা", "(?i)দাম", "(?i)সময়"], "english_indicators": ["(?i)doctor", "(?i)hospital", "(?i)test", "(?i)price", "(?i)time", "(?i)appointment"]}, "fuzzy_matching": {"enabled": true, "threshold": 0.8, "algorithms": ["<PERSON><PERSON><PERSON><PERSON>", "jaro_winkler"], "max_distance": 2}, "semantic_matching": {"enabled": true, "embedding_model": "sentence-transformers", "similarity_threshold": 0.75, "cache_embeddings": true}, "preprocessing": {"normalize_unicode": true, "remove_punctuation": true, "lowercase": true, "remove_extra_spaces": true, "handle_transliteration": true}, "metadata": {"total_patterns": 156, "supported_languages": ["en", "hi", "bn"], "pattern_categories": ["doctor", "test", "common", "hospital_info"], "compatibility": "shared_redis_v1.0"}}