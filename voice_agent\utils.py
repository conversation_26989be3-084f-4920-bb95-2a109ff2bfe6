import os
import logging
import time
import base64
import json
import asyncio
from typing import Dict, Any, Optional, Tuple
import threading
import hashlib
import datetime
from datetime import datetime, timedelta
import subprocess
from contextlib import closing
import socket
import tempfile
import re

import paramiko
from sshtunnel import SSHTunnelForwarder

# Module-local logger – configuration stays with the host app
logger = logging.getLogger(__name__)

# Active SSH tunnel managers
active_tunnels = {}

def create_ssh_tunnel(ssh_host: str, ssh_user: str, ssh_private_key: str,
                     remote_host: str, remote_port: int, local_port: int = None,
                     hospital_id: str = None, bind_all_interfaces: bool = False) -> SSHTunnelForwarder:
    """
    Create an SSH tunnel to a remote database server with robust port management.

    Production-ready implementation that fixes:
    1. Race condition in port allocation by using SSHTunnelForwarder's port 0 binding
    2. Security leak by properly cleaning up temporary SSH key files

    Args:
        ssh_host: SSH server hostname
        ssh_user: SSH username
        ssh_private_key: SSH private key (path or base64 content)
        remote_host: Remote host to tunnel to
        remote_port: Remote port to tunnel to
        local_port: Specific local port to use (optional, will use port 0 for auto-allocation if None)
        hospital_id: Hospital ID for port tracking (optional but recommended)
        bind_all_interfaces: If True, bind to 0.0.0.0 (security risk), otherwise bind to 127.0.0.1

    Returns:
        SSHTunnelForwarder instance with tunnel.local_bind_port containing actual port
    """
    # Track allocated resources for cleanup on failure
    allocated_port_for_cleanup = None
    temp_key_file = None
    reserved_socket = None

    try:
        # Resolve private key path or content with secure temporary file handling
        # Check if it's a file path (Unix: starts with '/', Windows: contains ':' or starts with '\')
        if (ssh_private_key.startswith('/') or
            (len(ssh_private_key) > 1 and ssh_private_key[1] == ':') or
            ssh_private_key.startswith('\\')):
            # It's a file path
            private_key_path = ssh_private_key
        else:
            # It's base64 encoded key content - create secure temporary file
            try:
                key_content = base64.b64decode(ssh_private_key).decode('utf-8')

                # Create secure temporary file with proper cleanup
                temp_key_file = tempfile.NamedTemporaryFile(
                    mode='w',
                    delete=False,
                    prefix="ssh_key_",
                    suffix=".pem",
                    dir=tempfile.gettempdir()
                )
                temp_key_file.write(key_content)
                temp_key_file.close()

                # Set secure permissions (owner read-only)
                os.chmod(temp_key_file.name, 0o600)
                private_key_path = temp_key_file.name

                logger.info(f"Created secure temporary SSH key file: {private_key_path}")

            except Exception as e:
                logger.error(f"Error processing SSH key: {e}")
                # Clean up temp file if created
                if temp_key_file and os.path.exists(temp_key_file.name):
                    try:
                        os.unlink(temp_key_file.name)
                    except OSError:
                        pass
                raise

        # Fix Issue 1: Robust port allocation that prevents race conditions
        if local_port is None:
            if hospital_id:
                # Check if PortManager already has a port allocated for this hospital
                existing_port = port_manager.get_allocated_port(hospital_id, "ssh_tunnel")
                if existing_port:
                    # Verify the existing port is still available
                    try:
                        test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                        bind_address = '0.0.0.0' if bind_all_interfaces else '127.0.0.1'
                        test_socket.bind((bind_address, existing_port))
                        test_socket.close()
                        local_port = existing_port
                        logger.info(f"Reusing existing PortManager allocation: port {existing_port} for hospital {hospital_id}")
                    except OSError:
                        logger.warning(f"Previously allocated port {existing_port} is no longer available, will allocate new port")
                        # Release the stale allocation
                        port_manager.release_port(hospital_id, "ssh_tunnel")
                        local_port = 0  # Fall back to auto-allocation
                else:
                    # No existing allocation, use auto-allocation for race-condition-free operation
                    local_port = 0
                    logger.info(f"No existing port allocation for hospital {hospital_id}, using auto-allocation")
            else:
                # No hospital_id provided, use auto-allocation
                local_port = 0
                logger.info("Using port 0 for auto-allocation by SSHTunnelForwarder (prevents race conditions)")
        else:
            # If specific port requested, reserve it with a socket to prevent race conditions
            try:
                reserved_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                reserved_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)

                # Determine bind address
                bind_address = '0.0.0.0' if bind_all_interfaces else '127.0.0.1'
                reserved_socket.bind((bind_address, local_port))

                logger.info(f"Reserved port {local_port} for SSH tunnel (preventing race condition)")

            except OSError as e:
                logger.warning(f"Requested port {local_port} is not available: {e}")
                # Clean up socket if created
                if reserved_socket:
                    reserved_socket.close()
                    reserved_socket = None

                # Fallback to auto-allocation
                local_port = 0
                logger.info("Falling back to port 0 for auto-allocation")

        # Determine bind address for security
        bind_address = '0.0.0.0' if bind_all_interfaces else '127.0.0.1'
        if bind_all_interfaces:
            logger.warning(f"SSH tunnel binding to all interfaces (0.0.0.0) - this exposes the database port to the network!")
        else:
            logger.info(f"SSH tunnel binding to loopback interface (127.0.0.1) for security")

        # Create SSH tunnel with race-condition-free port allocation
        tunnel = SSHTunnelForwarder(
            (ssh_host, 22),
            ssh_username=ssh_user,
            ssh_pkey=private_key_path,
            remote_bind_address=(remote_host, remote_port),
            local_bind_address=(bind_address, local_port)
        )

        # Release reserved socket before starting tunnel (if we had one)
        if reserved_socket:
            reserved_socket.close()
            reserved_socket = None
            logger.debug("Released reserved socket before tunnel start")

        # Start the tunnel
        tunnel.start()

        # Get the actual allocated port (important when using port 0)
        actual_port = tunnel.local_bind_port

        # Update port manager tracking if hospital_id provided
        if hospital_id:
            # Store the actual allocated port for tracking (important for database.py integration)
            try:
                with port_manager._lock:
                    # Initialize hospital entry if needed
                    if hospital_id not in port_manager._allocated_ports:
                        port_manager._allocated_ports[hospital_id] = {}

                    # Register the actual allocated port
                    port_manager._allocated_ports[hospital_id]["ssh_tunnel"] = actual_port
                    port_manager._port_locks[actual_port] = threading.Lock()
                    allocated_port_for_cleanup = (hospital_id, "ssh_tunnel")

                logger.info(f"Registered allocated port {actual_port} for hospital {hospital_id} in PortManager")
            except Exception as port_reg_error:
                logger.warning(f"Failed to register allocated port {actual_port} in PortManager: {port_reg_error}")

        logger.info(f"SSH tunnel established to {ssh_host}:{remote_port} on local port {actual_port} (bound to {bind_address})")

        # Fix Issue 2: Clean up temporary SSH key file after successful tunnel creation
        if temp_key_file:
            try:
                os.unlink(temp_key_file.name)
                logger.info(f"Cleaned up temporary SSH key file: {temp_key_file.name}")
            except OSError as cleanup_error:
                logger.warning(f"Failed to clean up temporary SSH key file {temp_key_file.name}: {cleanup_error}")

        return tunnel

    except Exception as e:
        # Comprehensive cleanup on failure
        logger.error(f"Error creating SSH tunnel: {e}")

        # Clean up reserved socket
        if reserved_socket:
            try:
                reserved_socket.close()
            except Exception:
                pass

        # Clean up allocated port
        if allocated_port_for_cleanup:
            hospital_id_cleanup, service_type_cleanup = allocated_port_for_cleanup
            logger.warning(f"SSH tunnel creation failed, releasing allocated port for hospital {hospital_id_cleanup}")
            try:
                port_manager.release_port(hospital_id_cleanup, service_type_cleanup)
            except Exception as port_cleanup_error:
                logger.error(f"Failed to release port during cleanup: {port_cleanup_error}")

        # Fix Issue 2: Clean up temporary SSH key file on failure
        if temp_key_file:
            try:
                os.unlink(temp_key_file.name)
                logger.info(f"Cleaned up temporary SSH key file after failure: {temp_key_file.name}")
            except OSError:
                # File might not exist or already cleaned up
                pass

        raise

def close_ssh_tunnel(tunnel: SSHTunnelForwarder, hospital_id: str = None):
    """
    Close an SSH tunnel and clean up port allocation.

    Args:
        tunnel: SSHTunnelForwarder instance to close
        hospital_id: Hospital ID for port cleanup (optional but recommended)
    """
    if tunnel and tunnel.is_active:
        try:
            # Get the local port before closing
            local_port = tunnel.local_bind_port

            tunnel.stop()
            logger.info(f"SSH tunnel closed (was using port {local_port})")

            # Clean up port allocation if hospital_id provided
            if hospital_id:
                port_manager.release_port(hospital_id, "ssh_tunnel")
                logger.info(f"Released port allocation for hospital {hospital_id}")

        except Exception as e:
            logger.error(f"Error closing SSH tunnel: {e}")

def test_db_connection(host: str, port: int, user: str, password: str, database: str) -> bool:
    """
    Test if a database connection is working
    """
    try:
        import psycopg2
        conn = psycopg2.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=database
        )
        conn.close()
        return True
    except Exception as e:
        logger.error(f"Database connection test failed: {e}")
        return False

def record_call(call_id: str, audio_data: bytes, metadata: Dict[str, Any]) -> str:
    """
    Record call audio to a file
    Returns the file path
    """
    try:
        # Create recordings directory if it doesn't exist
        recordings_dir = os.path.join(os.getcwd(), 'recordings')
        os.makedirs(recordings_dir, exist_ok=True)
        
        # Generate filename with timestamp
        from .datetime_utils import get_timestamp_for_filename
        timestamp = get_timestamp_for_filename()
        filename = f"{call_id}_{timestamp}.mp3"
        filepath = os.path.join(recordings_dir, filename)
        
        # Write audio data to file
        with open(filepath, 'wb') as f:
            f.write(audio_data)
        
        # Write metadata to a companion JSON file
        metadata_filepath = f"{filepath}.json"
        with open(metadata_filepath, 'w') as f:
            json.dump(metadata, f, indent=2)
        
        logger.info(f"Call recording saved to {filepath}")
        return filepath
    
    except Exception as e:
        logger.error(f"Error recording call: {e}")
        return ""

def get_free_port(start_port: int = 8000, end_port: int = 9000) -> int:
    """
    Find a free port in the given range (legacy function - use PortManager for new code)
    """
    for port in range(start_port, end_port):
        with closing(socket.socket(socket.AF_INET, socket.SOCK_STREAM)) as sock:
            res = sock.connect_ex(('localhost', port))
            if res != 0:
                return port

    raise RuntimeError(f"No free ports available in range {start_port}-{end_port}")


def get_ephemeral_port() -> int:
    """
    Get a free ephemeral port by binding to port 0 and letting OS assign.

    WARNING: This function has a race condition - the port is discovered but not reserved.
    Between discovery and actual binding by the caller, another process can grab the same port.

    For production SSH tunnels, prefer using port 0 directly with SSHTunnelForwarder
    or use the reserved socket approach in create_ssh_tunnel().

    Returns:
        int: A free port number assigned by the OS (but not reserved)
    """
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        s.bind(('', 0))
        s.listen(1)
        port = s.getsockname()[1]
    return port


class PortManager:
    """
    Production-grade port manager for multi-hospital deployments.
    Handles port allocation, tracking, and cleanup to prevent collisions.
    """

    def __init__(self):
        self._allocated_ports = {}  # hospital_id -> {service_type: port}
        self._port_locks = {}       # port -> lock for thread safety
        self._lock = threading.Lock()

    def allocate_port(self, hospital_id: str, service_type: str = "ssh_tunnel") -> int:
        """
        Allocate a port for a specific hospital and service type.

        Args:
            hospital_id: Unique hospital identifier
            service_type: Type of service (ssh_tunnel, database, etc.)

        Returns:
            int: Allocated port number

        Raises:
            RuntimeError: If unable to allocate a port
        """
        with self._lock:
            # Check if we already have a port allocated for this hospital/service
            if hospital_id in self._allocated_ports:
                existing_port = self._allocated_ports[hospital_id].get(service_type)
                if existing_port and self._is_port_available(existing_port):
                    logger.info(f"Reusing existing port {existing_port} for hospital {hospital_id} service {service_type}")
                    return existing_port
                elif existing_port:
                    logger.warning(f"Previously allocated port {existing_port} for hospital {hospital_id} is no longer available")

            # Allocate a new ephemeral port with collision prevention
            try:
                max_attempts = 10  # Prevent infinite loops
                attempts = 0

                while attempts < max_attempts:
                    port = get_ephemeral_port()

                    # Fix Issue 1: Check if port is already allocated to another hospital/service
                    if port not in self._port_locks:
                        # Port is unique, proceed with allocation
                        break

                    attempts += 1
                    logger.debug(f"Port {port} already allocated, trying again (attempt {attempts}/{max_attempts})")

                if attempts >= max_attempts:
                    raise RuntimeError(f"Unable to find unique port after {max_attempts} attempts")

                # Initialize hospital entry if needed
                if hospital_id not in self._allocated_ports:
                    self._allocated_ports[hospital_id] = {}

                # Store the allocation
                self._allocated_ports[hospital_id][service_type] = port
                self._port_locks[port] = threading.Lock()

                logger.info(f"Allocated ephemeral port {port} for hospital {hospital_id} service {service_type}")
                return port

            except Exception as e:
                logger.error(f"Failed to allocate port for hospital {hospital_id}: {e}")
                raise RuntimeError(f"Unable to allocate port for hospital {hospital_id}: {e}")

    def release_port(self, hospital_id: str, service_type: str = "ssh_tunnel") -> bool:
        """
        Release a port allocation for a specific hospital and service.

        Args:
            hospital_id: Hospital identifier
            service_type: Service type

        Returns:
            bool: True if port was released, False if not found
        """
        with self._lock:
            if hospital_id in self._allocated_ports:
                port = self._allocated_ports[hospital_id].pop(service_type, None)
                if port:
                    # Clean up port lock
                    self._port_locks.pop(port, None)

                    # Clean up hospital entry if empty
                    if not self._allocated_ports[hospital_id]:
                        del self._allocated_ports[hospital_id]

                    logger.info(f"Released port {port} for hospital {hospital_id} service {service_type}")
                    return True

            return False

    def get_allocated_port(self, hospital_id: str, service_type: str = "ssh_tunnel") -> Optional[int]:
        """
        Get the currently allocated port for a hospital/service combination.

        Args:
            hospital_id: Hospital identifier
            service_type: Service type

        Returns:
            Optional[int]: Port number if allocated, None otherwise
        """
        with self._lock:
            if hospital_id in self._allocated_ports:
                return self._allocated_ports[hospital_id].get(service_type)
            return None

    def _is_port_available(self, port: int) -> bool:
        """
        Check if a port is available for use.

        Args:
            port: Port number to check

        Returns:
            bool: True if port is available, False otherwise
        """
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return True
        except OSError:
            return False

    def cleanup_hospital(self, hospital_id: str) -> int:
        """
        Clean up all port allocations for a specific hospital.

        Args:
            hospital_id: Hospital identifier

        Returns:
            int: Number of ports released
        """
        with self._lock:
            if hospital_id not in self._allocated_ports:
                return 0

            ports_released = 0
            hospital_ports = self._allocated_ports[hospital_id].copy()

            # Release ports directly without calling release_port to avoid deadlock
            for service_type, port in hospital_ports.items():
                # Clean up port lock
                self._port_locks.pop(port, None)
                ports_released += 1

            # Remove hospital entry
            del self._allocated_ports[hospital_id]

            logger.info(f"Released {ports_released} ports for hospital {hospital_id}")
            return ports_released

    def get_port_status(self) -> Dict[str, Any]:
        """
        Get current port allocation status for monitoring.

        Returns:
            Dict containing port allocation statistics
        """
        with self._lock:
            total_hospitals = len(self._allocated_ports)
            total_ports = len(self._port_locks)

            hospital_breakdown = {}
            for hospital_id, services in self._allocated_ports.items():
                hospital_breakdown[hospital_id] = dict(services)

            return {
                "total_hospitals": total_hospitals,
                "total_allocated_ports": total_ports,
                "hospital_breakdown": hospital_breakdown,
                "port_locks_count": len(self._port_locks)
            }




# Global port manager instance
port_manager = PortManager()

def validate_phone_number(phone: str, region: str = "IN") -> Dict[str, Any]:
    """
    Validate phone number format and length for production use.
    Uses region-specific validation rules for better accuracy.

    Args:
        phone: Phone number to validate
        region: Region code for validation (defaults to India)

    Returns:
        Dict with validation result and error message if invalid
    """
    from .phone_validation_config import PhoneValidationConfig

    return PhoneValidationConfig.validate_phone_number(phone, region)

def sanitize_phone_number(phone: str, region: str = "IN") -> str:
    """
    Sanitize and standardize phone number format.
    Note: Use validate_phone_number() first to ensure the phone number is valid.

    Args:
        phone: Phone number to sanitize
        region: Region code for standardization rules

    Returns:
        Standardized phone number
    """
    from .phone_validation_config import PhoneValidationConfig

    # Validate and get standardized format
    validation_result = PhoneValidationConfig.validate_phone_number(phone, region)

    if validation_result["valid"]:
        return validation_result["standardized"]

    # Fallback: return original if validation fails
    return phone

async def retry_with_backoff(func, *args, max_retries=3, initial_backoff=1, **kwargs):
    """
    Retry a function with exponential backoff
    """
    retries = 0
    backoff = initial_backoff
    
    while True:
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            retries += 1
            if retries > max_retries:
                logger.error(f"Maximum retries ({max_retries}) exceeded: {e}")
                raise
            
            logger.warning(f"Retry {retries}/{max_retries} after error: {e}")
            await asyncio.sleep(backoff)
            backoff *= 2  # Exponential backoff

def format_phone_for_display(phone: str) -> str:
    """
    Format phone number for display
    Example: +************ -> +91 98765 43210
    """
    if not phone:
        return ""
    
    # Remove any non-digit characters except +
    cleaned = ''.join(c for c in phone if c.isdigit() or c == '+')
    
    # Handle Indian number format
    if cleaned.startswith('+91') and len(cleaned) == 13:
        return f"{cleaned[:3]} {cleaned[3:8]} {cleaned[8:]}"
    
    # Simple formatting for other numbers
    if cleaned.startswith('+'):
        country_code = cleaned[:3]
        rest = cleaned[3:]
        if len(rest) > 5:
            return f"{country_code} {rest[:len(rest)-5]} {rest[-5:]}"
    
    # If we can't apply special formatting, return cleaned number
    return cleaned

def detect_language_from_text(text: str) -> str:
    """
    Simple heuristic to detect language from text with improved mixed-language handling.
    Returns language code: 'en', 'hi', or 'bn'
    """
    if not text:
        return 'hi'  # Default to Hindi (primary language)

    # Remove non-alphabetic characters for better detection
    cleaned_text = ''.join(c for c in text if c.isalpha())
    if not cleaned_text:
        return 'hi'  # Default to Hindi if no alphabetic characters

    # Define character ranges for different scripts
    devanagari_range = (0x0900, 0x097F)  # Hindi
    bengali_range = (0x0980, 0x09FF)  # Bengali

    # Count characters in each script
    devanagari_count = 0
    bengali_count = 0
    latin_count = 0

    for char in cleaned_text:
        char_code = ord(char)
        if devanagari_range[0] <= char_code <= devanagari_range[1]:
            devanagari_count += 1
        elif bengali_range[0] <= char_code <= bengali_range[1]:
            bengali_count += 1
        elif 'a' <= char.lower() <= 'z':
            latin_count += 1

    # Only return a language if it has significant representation
    total_chars = len(cleaned_text)
    if total_chars > 0:
        # Calculate percentages
        devanagari_pct = devanagari_count / total_chars
        bengali_pct = bengali_count / total_chars
        latin_pct = latin_count / total_chars

        # Require at least 30% confidence for non-default languages
        if devanagari_pct >= 0.3 and devanagari_count > max(bengali_count, latin_count):
            return 'hi'
        elif bengali_pct >= 0.3 and bengali_count > max(devanagari_count, latin_count):
            return 'bn'
        elif latin_pct >= 0.3 and latin_count > max(devanagari_count, bengali_count):
            return 'en'

    # Default to Hindi if no clear dominant script or insufficient confidence
    return 'hi'

def generate_call_id() -> str:
    """
    Generate a unique call ID
    """
    timestamp = int(time.time())
    random_part = os.urandom(4).hex()
    return f"call_{timestamp}_{random_part}"

def format_duration(seconds: int) -> str:
    """
    Format duration in seconds to MM:SS format
    """
    minutes = seconds // 60
    remaining_seconds = seconds % 60
    return f"{minutes}:{remaining_seconds:02d}"


def parse_natural_language_time(time_input: str, reference_date: Optional[datetime] = None) -> Optional[datetime]:
    """
    Parse natural language time expressions into proper datetime objects.

    Handles expressions like:
    - "3 PM tomorrow"
    - "10 AM today"
    - "2:30 PM tomorrow"
    - "9 AM next Monday"
    - "4 PM"
    - "tomorrow at 3 PM"
    - "today at 10:30 AM"

    Args:
        time_input: Natural language time string
        reference_date: Reference date for relative calculations (defaults to now)

    Returns:
        datetime object or None if parsing fails
    """
    if not time_input or not isinstance(time_input, str):
        return None

    # Use current time as reference if not provided
    if reference_date is None:
        from .datetime_utils import parse_datetime_robust
        reference_date = datetime.now()

    # Clean and normalize the input
    time_input = time_input.strip().lower()

    try:
        # Pattern 1: "X PM/AM tomorrow" or "X:XX PM/AM tomorrow"
        tomorrow_pattern = r'(\d{1,2})(?::(\d{2}))?\s*(am|pm)\s+tomorrow'
        match = re.search(tomorrow_pattern, time_input)
        if match:
            hour = int(match.group(1))
            minute = int(match.group(2)) if match.group(2) else 0
            period = match.group(3)

            # Convert to 24-hour format
            if period == 'pm' and hour != 12:
                hour += 12
            elif period == 'am' and hour == 12:
                hour = 0

            # Calculate tomorrow's date
            tomorrow = reference_date + timedelta(days=1)
            return tomorrow.replace(hour=hour, minute=minute, second=0, microsecond=0)

        # Pattern 2: "tomorrow at X PM/AM" or "tomorrow at X:XX PM/AM"
        tomorrow_at_pattern = r'tomorrow\s+at\s+(\d{1,2})(?::(\d{2}))?\s*(am|pm)'
        match = re.search(tomorrow_at_pattern, time_input)
        if match:
            hour = int(match.group(1))
            minute = int(match.group(2)) if match.group(2) else 0
            period = match.group(3)

            # Convert to 24-hour format
            if period == 'pm' and hour != 12:
                hour += 12
            elif period == 'am' and hour == 12:
                hour = 0

            # Calculate tomorrow's date
            tomorrow = reference_date + timedelta(days=1)
            return tomorrow.replace(hour=hour, minute=minute, second=0, microsecond=0)

        # Pattern 3: "X PM/AM today" or "X:XX PM/AM today"
        today_pattern = r'(\d{1,2})(?::(\d{2}))?\s*(am|pm)\s+today'
        match = re.search(today_pattern, time_input)
        if match:
            hour = int(match.group(1))
            minute = int(match.group(2)) if match.group(2) else 0
            period = match.group(3)

            # Convert to 24-hour format
            if period == 'pm' and hour != 12:
                hour += 12
            elif period == 'am' and hour == 12:
                hour = 0

            return reference_date.replace(hour=hour, minute=minute, second=0, microsecond=0)

        # Pattern 4: "today at X PM/AM" or "today at X:XX PM/AM"
        today_at_pattern = r'today\s+at\s+(\d{1,2})(?::(\d{2}))?\s*(am|pm)'
        match = re.search(today_at_pattern, time_input)
        if match:
            hour = int(match.group(1))
            minute = int(match.group(2)) if match.group(2) else 0
            period = match.group(3)

            # Convert to 24-hour format
            if period == 'pm' and hour != 12:
                hour += 12
            elif period == 'am' and hour == 12:
                hour = 0

            return reference_date.replace(hour=hour, minute=minute, second=0, microsecond=0)

        # Pattern 5: Just "X PM/AM" or "X:XX PM/AM" (assume today)
        time_only_pattern = r'^(\d{1,2})(?::(\d{2}))?\s*(am|pm)$'
        match = re.search(time_only_pattern, time_input)
        if match:
            hour = int(match.group(1))
            minute = int(match.group(2)) if match.group(2) else 0
            period = match.group(3)

            # Convert to 24-hour format
            if period == 'pm' and hour != 12:
                hour += 12
            elif period == 'am' and hour == 12:
                hour = 0

            return reference_date.replace(hour=hour, minute=minute, second=0, microsecond=0)

        # Pattern 6: "X o'clock tomorrow/today"
        oclock_pattern = r'(\d{1,2})\s*o\'?clock\s+(tomorrow|today)'
        match = re.search(oclock_pattern, time_input)
        if match:
            hour = int(match.group(1))
            day_ref = match.group(2)

            # Assume PM for hours 1-11, AM for 12
            if hour == 12:
                hour = 12  # Keep as 12 PM
            elif 1 <= hour <= 11:
                hour += 12  # Convert to PM (1 o'clock = 1 PM)

            if day_ref == 'tomorrow':
                target_date = reference_date + timedelta(days=1)
            else:  # today
                target_date = reference_date

            return target_date.replace(hour=hour, minute=0, second=0, microsecond=0)

        # If no patterns match, try to parse as standard datetime formats
        # This handles cases like "2025-06-15 15:30" or "15:30"

        # Try HH:MM format (assume today)
        time_24h_pattern = r'^(\d{1,2}):(\d{2})$'
        match = re.search(time_24h_pattern, time_input)
        if match:
            hour = int(match.group(1))
            minute = int(match.group(2))

            if 0 <= hour <= 23 and 0 <= minute <= 59:
                return reference_date.replace(hour=hour, minute=minute, second=0, microsecond=0)

        # If all patterns fail, return None
        logger.warning(f"Could not parse natural language time: '{time_input}'")
        return None

    except (ValueError, AttributeError) as e:
        logger.error(f"Error parsing natural language time '{time_input}': {e}")
        return None


def format_appointment_datetime(appointment_time: str, appointment_date: Optional[str] = None) -> Optional[str]:
    """
    Convert natural language appointment time to database-compatible datetime string.

    This function replaces the problematic string concatenation that creates
    invalid timestamps like "2025-06-12 3 PM tomorrow".

    Args:
        appointment_time: Natural language time (e.g., "3 PM tomorrow", "10 AM today")
        appointment_date: Optional specific date (YYYY-MM-DD format)

    Returns:
        ISO format datetime string (YYYY-MM-DD HH:MM:SS) or None if parsing fails
    """
    try:
        # If we have a specific date, use it as reference
        reference_date = None
        if appointment_date:
            from .datetime_utils import parse_date_robust
            parsed_date = parse_date_robust(appointment_date)
            if parsed_date:
                # Convert date to datetime for compatibility
                reference_date = datetime.combine(parsed_date, datetime.min.time())
            else:
                logger.warning(f"Invalid appointment_date format: {appointment_date}")

        # Parse the natural language time
        parsed_datetime = parse_natural_language_time(appointment_time, reference_date)

        if parsed_datetime:
            # Return in PostgreSQL-compatible format using datetime_utils
            from .datetime_utils import format_for_postgres
            formatted_datetime = format_for_postgres(parsed_datetime)
            if formatted_datetime:
                return formatted_datetime
            else:
                # Fallback to manual formatting
                return parsed_datetime.strftime('%Y-%m-%d %H:%M:%S')
        else:
            logger.error(f"Failed to parse appointment time: '{appointment_time}'")
            return None

    except Exception as e:
        logger.error(f"Error formatting appointment datetime: {e}")
        return None
