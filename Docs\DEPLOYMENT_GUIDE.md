# Shared Redis Implementation - Deployment Guide

This guide provides step-by-step instructions for deploying the shared Redis implementation across all applications in the Voice Health Portal system.

## 🚀 Quick Deployment

### 1. Prerequisites

- **Redis Server**: Redis 6.0+ running and accessible
- **Python 3.8+**: For voice_agent and shared components
- **Node.js 16+**: For whatsapp_agent and staff_portal
- **Environment Variables**: Properly configured Redis settings

### 2. Install Dependencies

```bash
# Install Python dependencies for shared Redis
cd shared/redis
pip install -r requirements.txt

# Install Node.js dependencies (if not already installed)
cd ../../whatsapp_agent
npm install redis

cd ../staff_portal
npm install redis
```

### 3. Environment Configuration

Create a `.env` file or set environment variables:

```bash
# Core Redis Settings
REDIS_URL=redis://localhost:6379/0
REDIS_MAX_CONNECTIONS=50
REDIS_SOCKET_TIMEOUT=5.0

# Semantic Processing
REDIS_INDIC_BERT_ENABLED=true
REDIS_SEMANTIC_SIMILARITY_THRESHOLD=0.8
REDIS_EMBEDDING_DIMENSION=768

# Application Features
REDIS_VOICE_AGENT_ENABLED=true
REDIS_WHATSAPP_AGENT_ENABLED=true
REDIS_STAFF_PORTAL_ENABLED=true

# Cache TTL Settings
REDIS_DEFAULT_TTL=3600
REDIS_SEMANTIC_CACHE_TTL=86400
REDIS_CALL_CONTEXT_TTL=600

# Monitoring
REDIS_ENABLE_MONITORING=true
REDIS_METRICS_INTERVAL=60
```

### 4. Test the Implementation

```bash
# Run comprehensive tests
cd shared/redis
python test_shared_redis.py

# Run integration demo
python comprehensive_integration_example.py
```

## 📋 Application-Specific Deployment

### Voice Agent Migration

1. **Update imports** in `voice_agent/cache_manager.py`:
   ```python
   # OLD
   from .cache_manager import redis_manager
   
   # NEW
   from voice_agent.enhanced_cache_manager import redis_manager
   ```

2. **Enhanced features** are automatically available:
   ```python
   # Semantic caching with IndicBERT
   await redis_manager.cache_semantic_response_async(
       "डॉक्टर शर्मा कब आएंगे?", 
       "डॉ. शर्मा शाम 5 बजे आएंगे।", 
       hospital_id
   )
   
   # Enhanced call context management
   await redis_manager.save_call_context_async(call_id, context_data)
   ```

3. **Data migration** (if needed):
   ```python
   from shared.redis.migration_helper import VoiceAgentMigrationHelper
   
   migration_helper = VoiceAgentMigrationHelper()
   await migration_helper.migrate_existing_data(old_redis_client, hospital_ids)
   ```

### WhatsApp Agent Migration

1. **Update imports** in `whatsapp_agent/lib/redis.js`:
   ```javascript
   // The file is already updated to use shared implementation
   import { initializeRedis, getRedisClient } from './lib/redis.js';
   ```

2. **Enhanced features** are automatically available:
   ```javascript
   // Multilingual doctor availability
   await redis.cacheDoctorAvailability(hospitalId, doctorId, status, estimatedTime);
   
   // Semantic search across languages
   const matches = await redis.searchSimilarResponses(query, hospitalId, category);
   ```

### Staff Portal Migration

1. **Update imports** in `staff_portal/lib/redis_client.js`:
   ```javascript
   // The file is already updated to use shared implementation
   import { RedisClient } from './lib/redis_client.js';
   ```

2. **Enhanced features** are automatically available:
   ```javascript
   // Enhanced doctor availability management
   await redisClient.updateDoctorAvailability(hospitalId, doctorId, status, estimatedTime);
   
   // Batch updates for better performance
   await redisClient.batchUpdateDoctorAvailability(hospitalId, doctorUpdates);
   ```

## 🔧 Production Configuration

### Redis Server Setup

1. **Memory Configuration**:
   ```redis
   # redis.conf
   maxmemory 2gb
   maxmemory-policy allkeys-lru
   save 900 1
   save 300 10
   save 60 10000
   ```

2. **Security Settings**:
   ```redis
   # Enable authentication
   requirepass your_secure_password
   
   # Bind to specific interfaces
   bind 127.0.0.1 ********
   
   # Disable dangerous commands
   rename-command FLUSHDB ""
   rename-command FLUSHALL ""
   ```

3. **Performance Tuning**:
   ```redis
   # TCP settings
   tcp-keepalive 300
   timeout 0
   
   # Client settings
   maxclients 10000
   
   # Logging
   loglevel notice
   logfile /var/log/redis/redis-server.log
   ```

### Application Configuration

1. **Voice Agent** (`voice_agent/.env`):
   ```bash
   REDIS_URL=redis://:password@redis-server:6379/0
   REDIS_INDIC_BERT_ENABLED=true
   REDIS_CALL_CONTEXT_TTL=600
   REDIS_SEMANTIC_CACHE_TTL=86400
   ```

2. **WhatsApp Agent** (`whatsapp_agent/.env`):
   ```bash
   REDIS_URL=redis://:password@redis-server:6379/1
   REDIS_WHATSAPP_AGENT_ENABLED=true
   REDIS_DOCTOR_AVAILABILITY_TTL=1800
   ```

3. **Staff Portal** (`staff_portal/.env`):
   ```bash
   REDIS_URL=redis://:password@redis-server:6379/2
   REDIS_STAFF_PORTAL_ENABLED=true
   REDIS_BOOKING_LIMIT_TTL=86400
   ```

## 📊 Monitoring and Maintenance

### Health Checks

1. **Redis Health Check**:
   ```python
   from shared.redis.adapters.python_adapter import get_python_adapter
   
   adapter = get_python_adapter()
   is_healthy = adapter.is_connected()
   ```

2. **Performance Monitoring**:
   ```python
   from shared.redis.cache.monitor import get_cache_monitor
   
   monitor = get_cache_monitor()
   monitor.start_monitoring(interval=60)
   report = monitor.get_performance_report()
   ```

### Optimization

1. **Automatic Optimization**:
   ```python
   from shared.redis.cache.optimizer import get_cache_optimizer
   
   optimizer = get_cache_optimizer()
   optimizer.start_auto_optimization(interval=300)
   ```

2. **Manual Optimization**:
   ```python
   result = optimizer.optimize_ttl_settings(hospital_id)
   recommendations = optimizer.get_optimization_recommendations(hospital_id)
   ```

### Cleanup

1. **Automatic Cleanup**:
   ```python
   from shared.redis.cache.ttl_manager import get_ttl_manager
   
   ttl_manager = get_ttl_manager()
   ttl_manager.start_auto_cleanup(interval=3600)
   ```

2. **Manual Cleanup**:
   ```python
   cleanup_result = ttl_manager.cleanup_expired_keys("semantic:*")
   ```

## 🚨 Troubleshooting

### Common Issues

1. **Connection Errors**:
   - Check Redis server status: `redis-cli ping`
   - Verify network connectivity
   - Check authentication credentials

2. **Memory Issues**:
   - Monitor Redis memory usage: `redis-cli info memory`
   - Adjust TTL settings for better memory management
   - Enable automatic cleanup

3. **Performance Issues**:
   - Check cache hit rates
   - Monitor connection pool usage
   - Optimize TTL settings

### Debug Mode

Enable debug logging:
```python
import logging
logging.getLogger('shared.redis').setLevel(logging.DEBUG)
```

### Configuration Validation

```python
from shared.redis.config_manager import get_config_manager

config_manager = get_config_manager()
validation = config_manager.validate_configuration()

if not validation["valid"]:
    print("Configuration errors:", validation["errors"])
```

## 🔄 Rollback Plan

If issues occur, you can rollback to the original implementations:

1. **Voice Agent**: Revert imports to use original `cache_manager.py`
2. **WhatsApp Agent**: Revert to original Redis client implementation
3. **Staff Portal**: Revert to original Redis client

The shared Redis implementation is designed to be backward compatible, so existing functionality should continue to work.

## 📈 Performance Benchmarks

Expected performance improvements:

- **Cache Hit Rate**: 85-95% (vs 60-70% previously)
- **Response Time**: <50ms for cached queries (vs 100-200ms)
- **Memory Usage**: 30-40% reduction through optimization
- **Concurrent Connections**: Support for 100+ concurrent operations

## 🔐 Security Considerations

1. **Authentication**: Always use Redis AUTH in production
2. **Network Security**: Use TLS for Redis connections
3. **Access Control**: Implement proper firewall rules
4. **Data Encryption**: Consider Redis encryption at rest
5. **Audit Logging**: Enable comprehensive logging

## 📞 Support

For issues and questions:
1. Check this deployment guide
2. Review the troubleshooting section
3. Check Redis server logs
4. Verify environment configuration
5. Run the test suite to identify specific issues
