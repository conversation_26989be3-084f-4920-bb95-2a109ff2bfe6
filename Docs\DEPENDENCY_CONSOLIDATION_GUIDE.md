# Dependency Consolidation Guide

This guide explains the new dependency structure after consolidating Redis implementations and semantic processing dependencies.

## 📋 Overview

The Voice Health Portal system has been restructured to use a **shared Redis implementation** with consolidated dependencies. This eliminates duplication and ensures consistent behavior across all components.

### Key Changes

1. **Semantic Processing Dependencies** moved from `voice_agent/` to `shared/redis/`
2. **IndicBERT Integration** consolidated into shared implementation
3. **Cache Monitoring** unified across all agents
4. **Redis Dependencies** centralized in shared folder

## 🏗️ New Dependency Structure

### Voice Agent (`voice_agent/requirements.txt`)
**Contains only voice-agent-specific dependencies:**
- FastAPI and web framework components
- Firebase and database connections
- Voice processing (Google Cloud TTS)
- Network and SSH utilities
- Basic NLP tools (NLTK, spaCy)
- Fuzzy matching for voice queries

**Removed dependencies (moved to shared):**
- `sentence-transformers` → `shared/redis/`
- `transformers` → `shared/redis/`
- `torch` → `shared/redis/`
- `indic-nlp-library` → `shared/redis/`
- `redis` → `shared/redis/`
- `numpy` → `shared/redis/`
- `scipy` → `shared/redis/`

### Shared Redis (`shared/redis/requirements.txt`)
**Contains all Redis and semantic processing dependencies:**
- Redis client and async support
- IndicBERT and transformer models
- Semantic similarity processing
- Scientific computing libraries
- Cache monitoring and optimization
- Performance monitoring tools

### WhatsApp Agent (`whatsapp_agent/package.json`)
**Node.js dependencies remain unchanged:**
- Uses Node.js Redis client (`redis` package)
- Integrates with shared Redis through Node.js adapter

### Staff Portal (`staff_portal/package.json`)
**Node.js dependencies remain unchanged:**
- Uses `ioredis` for Redis connections
- Integrates with shared Redis through Node.js adapter

## 🚀 Installation Instructions

### 1. Install Shared Redis Dependencies (Required First)

```bash
# Install shared Redis dependencies (Python)
cd shared/redis
pip install -r requirements.txt
```

This installs:
- Redis client and async support
- IndicBERT and transformer models
- Semantic processing libraries
- Cache monitoring tools

### 2. Install Voice Agent Dependencies

```bash
cd voice_agent
pip install -r requirements.txt
```

This installs only voice-agent-specific dependencies.

### 3. Install WhatsApp Agent Dependencies

```bash
cd whatsapp_agent
npm install
```

Node.js Redis client is already included in package.json.

### 4. Install Staff Portal Dependencies

```bash
cd staff_portal
npm install
```

ioredis client is already included in package.json.

## 🔧 Development Setup

### For Voice Agent Development

```bash
# 1. Install shared dependencies first
cd shared/redis
pip install -r requirements.txt

# 2. Install voice agent dependencies
cd ../../voice_agent
pip install -r requirements.txt

# 3. Start voice agent
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

### For WhatsApp Agent Development

```bash
# 1. Ensure shared Redis is available (Python dependencies)
cd shared/redis
pip install -r requirements.txt

# 2. Install WhatsApp agent dependencies
cd ../../whatsapp_agent
npm install

# 3. Start WhatsApp agent
npm run dev
```

### For Staff Portal Development

```bash
# 1. Ensure shared Redis is available
cd shared/redis
pip install -r requirements.txt

# 2. Install staff portal dependencies
cd ../../staff_portal
npm install

# 3. Start staff portal
npm run dev
```

## 📦 Dependency Details

### Shared Redis Dependencies (`shared/redis/requirements.txt`)

| Package | Version | Purpose |
|---------|---------|---------|
| `redis` | 5.2.1 | Core Redis client |
| `transformers` | 4.52.4 | IndicBERT support |
| `torch` | 2.7.1 | ML framework |
| `sentence-transformers` | 4.1.0 | Semantic embeddings |
| `indic-nlp-library` | 0.92 | Indian language processing |
| `numpy` | 2.2.5 | Scientific computing |
| `scipy` | 1.15.3 | Advanced computations |
| `psutil` | 7.0.0 | Performance monitoring |

### Voice Agent Dependencies (`voice_agent/requirements.txt`)

| Package | Version | Purpose |
|---------|---------|---------|
| `fastapi` | 0.115.13 | Web framework |
| `firebase-admin` | 6.2.0 | Firebase integration |
| `google-cloud-texttospeech` | 2.26.0 | Voice synthesis |
| `rapidfuzz` | 3.13.0 | Fuzzy matching |
| `nltk` | 3.9.1 | Basic NLP |
| `spacy` | 3.8.7 | Advanced NLP |

## 🔄 Migration from Old Structure

If you have an existing installation:

### 1. Uninstall Old Dependencies (Optional)

```bash
cd voice_agent
pip uninstall sentence-transformers transformers torch indic-nlp-library redis numpy scipy
```

### 2. Install New Structure

```bash
# Install shared dependencies
cd ../shared/redis
pip install -r requirements.txt

# Reinstall voice agent dependencies
cd ../../voice_agent
pip install -r requirements.txt
```

## ✅ Verification

### Test Shared Redis Installation

```python
# Test script to verify shared Redis installation
python -c "
from shared.redis.adapters.python_adapter import get_python_adapter
from shared.redis.cache.monitor import get_cache_monitor
print('✅ Shared Redis installation successful')
"
```

### Test Voice Agent Installation

```python
# Test script to verify voice agent installation
python -c "
import fastapi
import firebase_admin
print('✅ Voice Agent installation successful')
"
```

## 🐛 Troubleshooting

### Common Issues

1. **Import Error: No module named 'shared'**
   - Ensure you're running from the project root
   - Add project root to PYTHONPATH if needed

2. **Redis Connection Error**
   - Verify Redis server is running
   - Check Redis configuration in environment variables

3. **IndicBERT Model Download Issues**
   - Ensure internet connection for model download
   - Check disk space for model storage

### Getting Help

- Check the shared Redis deployment guide: `shared/redis/DEPLOYMENT_GUIDE.md`
- Review integration examples: `shared/redis/*_integration_example.*`
- Verify environment configuration files

## 📚 Additional Resources

- [Shared Redis Implementation Guide](shared/redis/README.md)
- [Voice Agent Documentation](voice_agent/README.md)
- [WhatsApp Agent Documentation](whatsapp_agent/README.md)
- [Staff Portal Documentation](staff_portal/README.md)
