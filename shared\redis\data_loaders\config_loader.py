"""
Enhanced configuration data loader for shared Redis implementation.
Loads language templates and hospital configurations with improved caching and error handling.
"""

import json
import logging
import os
from typing import Dict, Any, List, Optional
from pathlib import Path

logger = logging.getLogger(__name__)


class ConfigDataLoader:
    """
    Production-ready configuration data loader for language templates and hospital configs.
    Enhanced version with better caching, error handling, and fallback mechanisms.
    """

    def __init__(self, config_base_path: Optional[str] = None):
        """
        Initialize configuration data loader.
        
        Args:
            config_base_path: Base path for configuration files (optional)
        """
        self.config_base_path = config_base_path or self._get_default_config_path()
        self.languages_path = os.path.join(self.config_base_path, "languages")
        self.hospital_templates_path = os.path.join(self.config_base_path, "hospital_templates")
        
        # Cache for loaded configurations
        self._language_cache = {}
        self._hospital_cache = {}
        self._query_patterns_cache = None
        
        logger.info(f"Config loader initialized with base path: {self.config_base_path}")

    def _get_default_config_path(self) -> str:
        """Get default configuration path."""
        # Try shared Redis config first, then fall back to voice_agent config
        shared_config = os.path.join(os.path.dirname(__file__), "..", "config")
        voice_agent_config = os.path.join(os.path.dirname(__file__), "..", "..", "..", "voice_agent", "config")
        
        if os.path.exists(shared_config):
            return shared_config
        elif os.path.exists(voice_agent_config):
            return voice_agent_config
        else:
            # Create shared config directory if it doesn't exist
            os.makedirs(shared_config, exist_ok=True)
            return shared_config

    def load_language_templates(self, language: str) -> Optional[Dict[str, Any]]:
        """
        Load language templates with caching.
        
        Args:
            language: Language code (e.g., 'en', 'hi', 'bn')
            
        Returns:
            Language templates dictionary or None if not found
        """
        # Check cache first
        if language in self._language_cache:
            return self._language_cache[language]
        
        try:
            template_file = os.path.join(self.languages_path, f"query_templates_{language}.json")
            
            if not os.path.exists(template_file):
                logger.warning(f"Language template file not found: {template_file}")
                return None
            
            with open(template_file, 'r', encoding='utf-8') as f:
                templates = json.load(f)
            
            # Validate template structure
            if not self._validate_language_template(templates, language):
                logger.error(f"Invalid template structure for language: {language}")
                return None
            
            # Cache the templates
            self._language_cache[language] = templates
            logger.info(f"Loaded language templates for: {language}")
            return templates
            
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in language template {language}: {e}")
            return None
        except Exception as e:
            logger.error(f"Error loading language templates for {language}: {e}")
            return None

    def _validate_language_template(self, templates: Dict[str, Any], language: str) -> bool:
        """Validate language template structure."""
        required_fields = ['language', 'doctor_name_queries', 'test_name_queries']
        
        for field in required_fields:
            if field not in templates:
                logger.error(f"Missing required field '{field}' in {language} template")
                return False
        
        if templates.get('language') != language:
            logger.warning(f"Language mismatch in template: expected {language}, got {templates.get('language')}")
        
        return True

    def load_hospital_template(self, hospital_id: str) -> Optional[Dict[str, Any]]:
        """
        Load hospital-specific template with fallback to default.
        
        Args:
            hospital_id: Hospital identifier
            
        Returns:
            Hospital template dictionary or None if not found
        """
        # Check cache first
        cache_key = f"hospital_{hospital_id}"
        if cache_key in self._hospital_cache:
            return self._hospital_cache[cache_key]
        
        try:
            # Try hospital-specific template first
            hospital_file = os.path.join(self.hospital_templates_path, f"{hospital_id}.json")
            
            if os.path.exists(hospital_file):
                with open(hospital_file, 'r', encoding='utf-8') as f:
                    template = json.load(f)
                logger.info(f"Loaded hospital-specific template for: {hospital_id}")
            else:
                # Fall back to default template
                default_file = os.path.join(self.hospital_templates_path, "default.json")
                if os.path.exists(default_file):
                    with open(default_file, 'r', encoding='utf-8') as f:
                        template = json.load(f)
                    logger.info(f"Loaded default template for hospital: {hospital_id}")
                else:
                    logger.warning(f"No template found for hospital {hospital_id} and no default template")
                    return None
            
            # Validate template structure
            if not self._validate_hospital_template(template, hospital_id):
                logger.error(f"Invalid hospital template structure for: {hospital_id}")
                return None
            
            # Cache the template
            self._hospital_cache[cache_key] = template
            return template
            
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in hospital template {hospital_id}: {e}")
            return None
        except Exception as e:
            logger.error(f"Error loading hospital template for {hospital_id}: {e}")
            return None

    def _validate_hospital_template(self, template: Dict[str, Any], hospital_id: str) -> bool:
        """Validate hospital template structure."""
        required_fields = ['hospital_info', 'languages']
        
        for field in required_fields:
            if field not in template:
                logger.error(f"Missing required field '{field}' in hospital template for {hospital_id}")
                return False
        
        # Validate languages field
        if not isinstance(template.get('languages'), list):
            logger.error(f"Invalid 'languages' field in hospital template for {hospital_id}")
            return False
        
        return True

    def load_query_patterns(self) -> Dict[str, Any]:
        """
        Load query patterns configuration.
        
        Returns:
            Query patterns dictionary
        """
        if self._query_patterns_cache is not None:
            return self._query_patterns_cache
        
        try:
            patterns_file = os.path.join(self.hospital_templates_path, "query_patterns.json")
            
            if os.path.exists(patterns_file):
                with open(patterns_file, 'r', encoding='utf-8') as f:
                    patterns = json.load(f)
                logger.info("Loaded query patterns configuration")
            else:
                # Return default patterns if file doesn't exist
                patterns = self._get_default_query_patterns()
                logger.info("Using default query patterns")
            
            self._query_patterns_cache = patterns
            return patterns
            
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in query patterns: {e}")
            return self._get_default_query_patterns()
        except Exception as e:
            logger.error(f"Error loading query patterns: {e}")
            return self._get_default_query_patterns()

    def _get_default_query_patterns(self) -> Dict[str, Any]:
        """Get default query patterns."""
        return {
            "doctor_patterns": [
                r"(?i)dr\.?\s*{name}",
                r"(?i)doctor\s+{name}",
                r"(?i){name}\s+doctor"
            ],
            "specialty_patterns": [
                r"(?i){specialty}\s+doctor",
                r"(?i){specialty}\s+specialist",
                r"(?i)doctor\s+{specialty}"
            ],
            "test_patterns": [
                r"(?i){test}\s+price",
                r"(?i){test}\s+cost",
                r"(?i)cost\s+of\s+{test}"
            ],
            "common_patterns": {
                "greeting": [r"(?i)hello", r"(?i)hi", r"(?i)namaste"],
                "help": [r"(?i)help", r"(?i)assist"],
                "appointment": [r"(?i)appointment", r"(?i)booking"]
            }
        }

    def get_supported_languages(self) -> List[str]:
        """
        Get list of supported languages.
        
        Returns:
            List of language codes
        """
        try:
            if not os.path.exists(self.languages_path):
                return ["en", "hi", "bn"]  # Default languages
            
            languages = []
            for file in os.listdir(self.languages_path):
                if file.startswith("query_templates_") and file.endswith(".json"):
                    lang_code = file.replace("query_templates_", "").replace(".json", "")
                    languages.append(lang_code)
            
            return languages if languages else ["en", "hi", "bn"]
            
        except Exception as e:
            logger.error(f"Error getting supported languages: {e}")
            return ["en", "hi", "bn"]

    def get_supported_hospitals(self) -> List[str]:
        """
        Get list of hospitals with specific templates.
        
        Returns:
            List of hospital IDs
        """
        try:
            if not os.path.exists(self.hospital_templates_path):
                return []
            
            hospitals = []
            for file in os.listdir(self.hospital_templates_path):
                if file.endswith(".json") and file != "default.json" and file != "query_patterns.json":
                    hospital_id = file.replace(".json", "")
                    hospitals.append(hospital_id)
            
            return hospitals
            
        except Exception as e:
            logger.error(f"Error getting supported hospitals: {e}")
            return []

    def clear_cache(self):
        """Clear all cached configurations."""
        self._language_cache.clear()
        self._hospital_cache.clear()
        self._query_patterns_cache = None
        logger.info("Configuration cache cleared")

    def reload_config(self, language: str = None, hospital_id: str = None):
        """
        Reload specific configuration from disk.
        
        Args:
            language: Language to reload (optional)
            hospital_id: Hospital to reload (optional)
        """
        if language:
            self._language_cache.pop(language, None)
            self.load_language_templates(language)
        
        if hospital_id:
            cache_key = f"hospital_{hospital_id}"
            self._hospital_cache.pop(cache_key, None)
            self.load_hospital_template(hospital_id)
        
        if not language and not hospital_id:
            self.clear_cache()
            
        logger.info(f"Reloaded configuration - language: {language}, hospital: {hospital_id}")

    def create_config_directories(self):
        """Create configuration directories if they don't exist."""
        try:
            os.makedirs(self.languages_path, exist_ok=True)
            os.makedirs(self.hospital_templates_path, exist_ok=True)
            logger.info("Created configuration directories")
        except Exception as e:
            logger.error(f"Error creating configuration directories: {e}")
