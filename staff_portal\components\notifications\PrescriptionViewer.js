import React, { useState } from 'react';
import { db } from '../../lib/firebase';
import { doc, updateDoc } from 'firebase/firestore';

/**
 * PrescriptionViewer component to display OCR results from prescription images
 * Allows staff to review and take action on prescription notifications
 */
export default function PrescriptionViewer({ notification, hospitalId, onClose }) {
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [sending, setSending] = useState(false);

  if (!notification) return null;

  const getConfidenceLabel = (confidence) => {
    if (!confidence) return 'N/A';
    const percentage = Math.round(confidence * 100);
    
    if (percentage < 50) return `Very Low (${percentage}%)`;
    if (percentage < 70) return `Low (${percentage}%)`;
    if (percentage < 85) return `Moderate (${percentage}%)`;
    if (percentage < 95) return `Good (${percentage}%)`;
    return `High (${percentage}%)`;
  };

  const getConfidenceColor = (confidence) => {
    if (!confidence) return 'text-gray-500';
    const percentage = confidence * 100;
    
    if (percentage < 50) return 'text-red-600';
    if (percentage < 70) return 'text-orange-500';
    if (percentage < 85) return 'text-yellow-500';
    if (percentage < 95) return 'text-green-500';
    return 'text-green-600';
  };

  const handleResolve = async () => {
    try {
      setLoading(true);
      const notificationRef = doc(db, `hospital_${hospitalId}_data/notifications/notifications/${notification.id}`);
      await updateDoc(notificationRef, {
        status: 'resolved',
        viewed: true,
        resolved_by: 'staff', // This would ideally be the current user's ID
        resolved_at: new Date(),
        updated_at: new Date()
      });
      if (onClose) onClose();
    } catch (error) {
      console.error('Error resolving notification:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSendMessage = async () => {
    if (!message.trim()) return;
    
    try {
      setSending(true);
      
      // This would integrate with your WhatsApp service to send a message
      // For now, we'll just update the notification status
      const notificationRef = doc(db, `hospital_${hospitalId}_data/notifications/notifications/${notification.id}`);
      await updateDoc(notificationRef, {
        staff_message: message,
        status: 'staff_responded',
        viewed: true,
        updated_at: new Date()
      });
      
      setMessage('');
      if (onClose) onClose();
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setSending(false);
    }
  };

  return (
    <div className="prescription-viewer bg-white rounded-lg shadow-lg overflow-hidden max-w-4xl mx-auto">
      <div className="p-4 bg-blue-50 border-b border-blue-100 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-blue-800">Prescription Review</h3>
        <button 
          onClick={onClose}
          className="text-gray-500 hover:text-gray-700"
        >
          <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Left column: Image */}
          <div className="prescription-image">
            <h4 className="text-sm font-medium text-gray-500 mb-2">Original Prescription Image</h4>
            {notification.mediaUrl ? (
              <div className="border border-gray-200 rounded-lg overflow-hidden bg-gray-50">
                <img 
                  src={notification.mediaUrl} 
                  alt="Prescription" 
                  className="w-full h-auto object-contain max-h-80"
                />
              </div>
            ) : (
              <div className="border border-gray-200 rounded-lg p-4 bg-gray-50 text-gray-400 text-center">
                No image available
              </div>
            )}
          </div>

          {/* Right column: OCR details */}
          <div className="ocr-details">
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-500 mb-2">Patient Information</h4>
              <p className="text-sm"><strong>Phone Number:</strong> {notification.phoneNumber}</p>
              {notification.prescriptionDetails?.patientName && (
                <p className="text-sm"><strong>Patient Name:</strong> {notification.prescriptionDetails.patientName}</p>
              )}
              <p className="text-sm"><strong>Received:</strong> {new Date(notification.timestamp).toLocaleString()}</p>
            </div>

            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-500 mb-2">OCR Confidence</h4>
              <p className={`text-sm font-medium ${getConfidenceColor(notification.confidence)}`}>
                {getConfidenceLabel(notification.confidence)}
              </p>
            </div>

            {notification.prescriptionDetails?.tests && (
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-500 mb-2">Detected Tests</h4>
                <div className="bg-green-50 p-2 rounded border border-green-100">
                  <p className="text-sm text-green-800">{notification.prescriptionDetails.tests}</p>
                </div>
              </div>
            )}

            {notification.prescriptionDetails?.date && (
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-500 mb-2">Detected Date</h4>
                <p className="text-sm">{notification.prescriptionDetails.date}</p>
              </div>
            )}

            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-500 mb-2">Extracted Text</h4>
              <div className="bg-gray-50 p-3 rounded border border-gray-200 max-h-40 overflow-y-auto">
                <pre className="text-xs text-gray-700 whitespace-pre-wrap">{notification.ocrText || 'No text extracted'}</pre>
              </div>
            </div>
          </div>
        </div>

        {/* Message and action section */}
        <div className="mt-6 border-t border-gray-200 pt-4">
          <h4 className="text-sm font-medium text-gray-500 mb-2">Send Response to Patient (via WhatsApp)</h4>
          <textarea
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            rows="3"
            placeholder="Type your message to the patient..."
            value={message}
            onChange={(e) => setMessage(e.target.value)}
          ></textarea>
          
          <div className="mt-4 flex justify-end space-x-2">
            <button
              className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400"
              onClick={handleResolve}
              disabled={loading}
            >
              {loading ? 'Resolving...' : 'Mark as Resolved'}
            </button>
            <button
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              onClick={handleSendMessage}
              disabled={!message.trim() || sending}
            >
              {sending ? 'Sending...' : 'Send Message'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
