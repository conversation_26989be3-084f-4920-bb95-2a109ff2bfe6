/**
 * Common API utilities for consistent request handling
 */

/**
 * Safely parse response content based on content type
 * Handles both JSON and non-JSON responses gracefully
 */
const safeParseResponse = async (response) => {
  try {
    const contentType = response.headers.get('content-type');

    if (contentType && contentType.includes('application/json')) {
      return await response.json();
    } else {
      // For non-JSON responses, return as text
      const textData = await response.text();

      // Try to create a consistent response structure for non-JSON responses
      if (response.ok) {
        return {
          success: true,
          message: textData || 'Operation completed successfully',
          data: textData
        };
      } else {
        return {
          success: false,
          message: textData || `HTTP ${response.status}: ${response.statusText}`,
          error: textData || response.statusText
        };
      }
    }
  } catch (error) {
    // If parsing fails completely, return a structured error
    return {
      success: false,
      message: `Failed to parse response: ${error.message}`,
      error: error.message
    };
  }
};

/**
 * Standard API request configuration
 */
export const createApiConfig = (method, body = null, additionalHeaders = {}) => {
  const config = {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...additionalHeaders
    }
  };

  if (body) {
    config.body = JSON.stringify(body);
  }

  return config;
};

/**
 * Generic API request handler with error handling
 * Handles both JSON and non-JSON responses gracefully
 */
export const makeApiRequest = async (url, method, body = null, options = {}) => {
  try {
    const config = createApiConfig(method, body, options.headers);
    const response = await fetch(url, config);

    // Use safe parsing to handle both JSON and non-JSON responses
    const data = await safeParseResponse(response);

    return {
      success: response.ok,
      status: response.status,
      data,
      response
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      data: null
    };
  }
};

/**
 * Build URL with query parameters
 */
export const buildUrlWithParams = (baseUrl, params = {}) => {
  const url = new URL(baseUrl, window.location.origin);
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== null && value !== undefined && value !== '') {
      url.searchParams.append(key, value);
    }
  });
  
  return url.toString();
};

/**
 * Standard request body builder for admin operations
 */
export const buildAdminRequestBody = (entityData, hospitalId, entityType) => {
  return {
    hospitalId,
    [`${entityType}Data`]: {
      ...entityData,
      hospital_id: hospitalId
    }
  };
};

/**
 * Handle API response with consistent error handling
 * Uses safe parsing to handle both JSON and non-JSON responses
 */
export const handleApiResponse = async (response, successCallback, errorCallback) => {
  try {
    // Use safe parsing to handle both JSON and non-JSON responses
    const data = await safeParseResponse(response);

    if (data.success) {
      if (successCallback) {
        return await successCallback(data);
      }
      return { success: true, data: data.data };
    } else {
      if (errorCallback) {
        return await errorCallback(data);
      }
      return { success: false, error: data.message || data.error };
    }
  } catch (error) {
    if (errorCallback) {
      return await errorCallback({ message: error.message });
    }
    return { success: false, error: error.message };
  }
};

/**
 * Retry mechanism for failed requests
 */
export const retryApiRequest = async (requestFn, maxRetries = 3, delay = 1000) => {
  let lastError;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const result = await requestFn();
      if (result.success) {
        return result;
      }
      lastError = result.error;
    } catch (error) {
      lastError = error.message;
    }
    
    if (attempt < maxRetries) {
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }
  
  return { success: false, error: lastError };
};

/**
 * Batch API requests with concurrency control
 */
export const batchApiRequests = async (requests, maxConcurrency = 5) => {
  const results = [];
  const executing = [];
  
  for (const request of requests) {
    const promise = request().then(result => {
      executing.splice(executing.indexOf(promise), 1);
      return result;
    });
    
    results.push(promise);
    executing.push(promise);
    
    if (executing.length >= maxConcurrency) {
      await Promise.race(executing);
    }
  }
  
  return Promise.all(results);
};

/**
 * API request with timeout and safe response parsing
 * Handles both JSON and non-JSON responses gracefully
 */
export const makeApiRequestWithTimeout = async (url, method, body = null, timeout = 30000) => {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    const config = createApiConfig(method, body);
    config.signal = controller.signal;

    const response = await fetch(url, config);
    clearTimeout(timeoutId);

    // Use safe parsing to handle both JSON and non-JSON responses
    const data = await safeParseResponse(response);

    return {
      success: response.ok,
      status: response.status,
      data,
      response
    };
  } catch (error) {
    clearTimeout(timeoutId);

    if (error.name === 'AbortError') {
      return {
        success: false,
        error: 'Request timeout',
        data: null
      };
    }

    return {
      success: false,
      error: error.message,
      data: null
    };
  }
};

/**
 * Validate API response structure
 */
export const validateApiResponse = (data, requiredFields = []) => {
  if (!data || typeof data !== 'object') {
    return { valid: false, error: 'Invalid response format' };
  }
  
  if (!data.hasOwnProperty('success')) {
    return { valid: false, error: 'Missing success field in response' };
  }
  
  for (const field of requiredFields) {
    if (!data.hasOwnProperty(field)) {
      return { valid: false, error: `Missing required field: ${field}` };
    }
  }
  
  return { valid: true };
};

/**
 * Format error message for user display
 */
export const formatErrorMessage = (error, fallbackMessage = 'An unexpected error occurred') => {
  if (typeof error === 'string') {
    return error;
  }
  
  if (error && error.message) {
    return error.message;
  }
  
  if (error && error.details) {
    return Array.isArray(error.details) ? error.details.join(', ') : error.details;
  }
  
  return fallbackMessage;
};
