#!/usr/bin/env python3
"""
Startup script for the LLM API server

This script starts the unified LLM service HTTP API server that can be used
by both the voice agent and WhatsApp agent applications.
"""

import os
import sys
import logging
import argparse
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from shared.llm_api_server import run_server
from shared.config import validate_config

def setup_logging(log_level: str = "INFO"):
    """Set up logging configuration."""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('llm_service.log')
        ]
    )

def check_environment():
    """Check if required environment variables are set."""
    required_vars = ['OPENAI_API_KEY']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"ERROR: Missing required environment variables: {', '.join(missing_vars)}")
        print("Please set these variables before starting the server.")
        return False
    
    return True

def main():
    """Main function to start the LLM API server."""
    parser = argparse.ArgumentParser(description='Start the LLM API server')
    parser.add_argument('--host', default='0.0.0.0', help='Host to bind to (default: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=8001, help='Port to bind to (default: 8001)')
    parser.add_argument('--reload', action='store_true', help='Enable auto-reload for development')
    parser.add_argument('--log-level', default='INFO', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], 
                       help='Log level (default: INFO)')
    
    args = parser.parse_args()
    
    # Set up logging
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    logger.info("Starting LLM API server...")
    
    # Check environment
    if not check_environment():
        sys.exit(1)
    
    # Validate configuration
    config_status = validate_config()
    if not config_status["valid"]:
        logger.warning(f"Configuration issues detected: {config_status['issues']}")
        logger.warning("Server will start but some features may not work correctly.")
    else:
        logger.info("Configuration validation passed.")
    
    # Start the server
    try:
        logger.info(f"Starting server on {args.host}:{args.port}")
        run_server(
            host=args.host,
            port=args.port,
            reload=args.reload
        )
    except KeyboardInterrupt:
        logger.info("Server stopped by user.")
    except Exception as e:
        logger.error(f"Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
