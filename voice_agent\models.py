from typing import Dict, List, Optional, Any
from pydantic import BaseModel

class SIPTrunk(BaseModel):
    provider: str
    sip_endpoint: str
    auth_token: str

class SSHTunnel(BaseModel):
    user: str
    host: str
    private_key_path: str

class HospitalConfig(BaseModel):
    name: str
    sip_trunk: SIPTrunk
    languages: List[str]
    db_connection_string: str  # Changed from db_postgres to match Firebase field name
    ssh_tunnel: Optional[Dict[str, str]] = None
    created_at: str
    emergency_number: Optional[str] = "911"

    # Optional fields that might be present in Firebase
    id: Optional[str] = None
    address: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None

class Doctor(BaseModel):
    id: str
    name: str
    specialty: str
    schedule: Dict[str, Any]
    availability: Dict[str, List[str]]
    price: float

class Test(BaseModel):
    id: str
    name: str
    description: str
    duration: int
    cost: float
    requirements: str

class StaffMember(BaseModel):
    id: str
    name: str
    role: str
    credentials: Dict[str, str]
    contact: Optional[Dict[str, str]] = None

class Appointment(BaseModel):
    id: int
    patient_name: str
    phone: str
    doctor_id: str
    time: str
    status: str
    created_at: str

class TestBooking(BaseModel):
    id: int
    patient_name: str
    phone: str
    test_type_id: str
    time: str
    status: str
    result_url: Optional[str] = None
    created_at: str

class CallRecord(BaseModel):
    id: int
    caller_number: str
    timestamp: str
    duration: int
    recording: bytes
    hospital_id: str

class CallContext(BaseModel):
    call_id: str
    hospital_id: str
    caller_number: str
    language: Optional[str] = None
    state: str = "greeting"
    appointment_data: Dict[str, Any] = {}
    timestamp: str
